"""
Operation Models
Database models for operation tracking and logging
"""

from datetime import datetime
from typing import Optional
from enum import Enum

from sqlalchemy import String, DateTime, Integer, Float, Text, Enum as SQLEnum, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship

from ..core.database import Base


class OperationStatus(str, Enum):
    """Operation status enumeration"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class OperationType(str, Enum):
    """Operation type enumeration"""
    DATABASE_CLEANUP = "database_cleanup"
    SYSTEM_ANALYSIS = "system_analysis"
    BACKUP_CREATION = "backup_creation"
    ID_MODIFICATION = "id_modification"
    BATCH_OPERATION = "batch_operation"


class Operation(Base):
    """Operation tracking model"""
    
    __tablename__ = "operations"
    
    id: Mapped[int] = mapped_column(primary_key=True)
    task_id: Mapped[str] = mapped_column(String(36), unique=True, nullable=False)  # UUID
    
    # Operation details
    operation_type: Mapped[OperationType] = mapped_column(SQLEnum(OperationType), nullable=False)
    status: Mapped[OperationStatus] = mapped_column(SQLEnum(OperationStatus), default=OperationStatus.PENDING)
    
    # Progress tracking
    progress: Mapped[float] = mapped_column(Float, default=0.0)
    message: Mapped[Optional[str]] = mapped_column(String(500))
    
    # Timing
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    
    # Results and errors
    result: Mapped[Optional[str]] = mapped_column(Text)  # JSON string
    error: Mapped[Optional[str]] = mapped_column(Text)
    
    # User and priority
    user_id: Mapped[Optional[str]] = mapped_column(String(50))  # Reference to user
    priority: Mapped[int] = mapped_column(Integer, default=5)
    
    # Parameters
    parameters: Mapped[Optional[str]] = mapped_column(Text)  # JSON string
    
    # Relationships
    logs: Mapped[list["OperationLog"]] = relationship("OperationLog", back_populates="operation")
    
    def __repr__(self) -> str:
        return f"<Operation(id={self.id}, task_id='{self.task_id}', type='{self.operation_type}', status='{self.status}')>"


class OperationLog(Base):
    """Operation log entries model"""
    
    __tablename__ = "operation_logs"
    
    id: Mapped[int] = mapped_column(primary_key=True)
    operation_id: Mapped[int] = mapped_column(ForeignKey("operations.id"), nullable=False)
    
    # Log details
    level: Mapped[str] = mapped_column(String(10), nullable=False)  # INFO, WARNING, ERROR
    message: Mapped[str] = mapped_column(Text, nullable=False)
    timestamp: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # Additional context
    context: Mapped[Optional[str]] = mapped_column(Text)  # JSON string
    
    # Relationships
    operation: Mapped["Operation"] = relationship("Operation", back_populates="logs")
    
    def __repr__(self) -> str:
        return f"<OperationLog(id={self.id}, operation_id={self.operation_id}, level='{self.level}')>"
