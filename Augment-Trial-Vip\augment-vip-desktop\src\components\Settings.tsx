import React, { useState, useEffect } from "react";
import {
  Settings as SettingsIcon,
  Monitor,
  Moon,
  Sun,
  Globe,
  Shield,
  Bell,
  Download,
  Upload,
  Trash2,
  Refresh<PERSON>w,
  Save,
  RotateCcw,
  Info,
  AlertTriangle,
  CheckCircle,
  Folder,
  Key,
  Database,
  Zap,
  Clock,
  HardDrive,
  Wifi,
  Volume2,
  Eye,
  Lock,
  User,
  Mail,
  Smartphone,
} from "lucide-react";
import useAppStore from "../store/useAppStore";
import { <PERSON><PERSON>, Card, CardContent, Badge } from "./ui";

interface SettingsSection {
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  description: string;
}

interface AppSettings {
  // General
  theme: 'light' | 'dark' | 'system';
  language: string;
  autoStart: boolean;
  minimizeToTray: boolean;
  closeToTray: boolean;
  
  // Notifications
  enableNotifications: boolean;
  soundEnabled: boolean;
  showDesktopNotifications: boolean;
  notificationDuration: number;
  
  // Operations
  autoCleanEnabled: boolean;
  autoCleanInterval: number; // hours
  confirmDangerousOperations: boolean;
  showOperationProgress: boolean;
  
  // Backup
  autoBackupEnabled: boolean;
  backupLocation: string;
  backupRetention: number; // days
  compressBackups: boolean;
  
  // Security
  requirePasswordForOperations: boolean;
  encryptBackups: boolean;
  logOperations: boolean;
  maxLogEntries: number;
  
  // Performance
  maxConcurrentOperations: number;
  enableHardwareAcceleration: boolean;
  memoryLimit: number; // MB
  
  // Advanced
  debugMode: boolean;
  telemetryEnabled: boolean;
  autoUpdatesEnabled: boolean;
  betaUpdates: boolean;
}

const Settings: React.FC = () => {
  const { theme, setTheme, addOperationLog } = useAppStore();
  
  const [activeSection, setActiveSection] = useState<string>('general');
  const [settings, setSettings] = useState<AppSettings>({
    // General
    theme: theme,
    language: 'en',
    autoStart: true,
    minimizeToTray: true,
    closeToTray: false,
    
    // Notifications
    enableNotifications: true,
    soundEnabled: true,
    showDesktopNotifications: true,
    notificationDuration: 5,
    
    // Operations
    autoCleanEnabled: false,
    autoCleanInterval: 24,
    confirmDangerousOperations: true,
    showOperationProgress: true,
    
    // Backup
    autoBackupEnabled: true,
    backupLocation: '/Users/<USER>/AugmentVIP/Backups',
    backupRetention: 30,
    compressBackups: true,
    
    // Security
    requirePasswordForOperations: false,
    encryptBackups: false,
    logOperations: true,
    maxLogEntries: 1000,
    
    // Performance
    maxConcurrentOperations: 3,
    enableHardwareAcceleration: true,
    memoryLimit: 512,
    
    // Advanced
    debugMode: false,
    telemetryEnabled: true,
    autoUpdatesEnabled: true,
    betaUpdates: false,
  });

  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const sections: SettingsSection[] = [
    {
      id: 'general',
      title: 'General',
      icon: SettingsIcon,
      description: 'Basic application settings and preferences'
    },
    {
      id: 'notifications',
      title: 'Notifications',
      icon: Bell,
      description: 'Configure alerts and notification preferences'
    },
    {
      id: 'operations',
      title: 'Operations',
      icon: Zap,
      description: 'Settings for cleanup and system operations'
    },
    {
      id: 'backup',
      title: 'Backup',
      icon: Database,
      description: 'Backup and restore configuration'
    },
    {
      id: 'security',
      title: 'Security',
      icon: Shield,
      description: 'Security and privacy settings'
    },
    {
      id: 'performance',
      title: 'Performance',
      icon: Zap,
      description: 'Performance and resource management'
    },
    {
      id: 'advanced',
      title: 'Advanced',
      icon: SettingsIcon,
      description: 'Advanced settings and developer options'
    },
  ];

  useEffect(() => {
    // Sync theme changes with the store
    if (settings.theme !== theme) {
      setTheme(settings.theme as any);
    }
  }, [settings.theme, theme, setTheme]);

  const updateSetting = <K extends keyof AppSettings>(key: K, value: AppSettings[K]) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    setHasUnsavedChanges(true);
  };

  const saveSettings = () => {
    // Here you would typically save to localStorage or send to backend
    localStorage.setItem('augment-vip-settings', JSON.stringify(settings));
    setHasUnsavedChanges(false);
    
    addOperationLog({
      operation: 'settings' as any,
      status: 'success',
      message: 'Settings saved successfully',
      details: 'All settings have been saved to local storage',
    });
  };

  const resetSettings = () => {
    if (confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.')) {
      // Reset to default values
      setSettings({
        theme: 'system',
        language: 'en',
        autoStart: true,
        minimizeToTray: true,
        closeToTray: false,
        enableNotifications: true,
        soundEnabled: true,
        showDesktopNotifications: true,
        notificationDuration: 5,
        autoCleanEnabled: false,
        autoCleanInterval: 24,
        confirmDangerousOperations: true,
        showOperationProgress: true,
        autoBackupEnabled: true,
        backupLocation: '/Users/<USER>/AugmentVIP/Backups',
        backupRetention: 30,
        compressBackups: true,
        requirePasswordForOperations: false,
        encryptBackups: false,
        logOperations: true,
        maxLogEntries: 1000,
        maxConcurrentOperations: 3,
        enableHardwareAcceleration: true,
        memoryLimit: 512,
        debugMode: false,
        telemetryEnabled: true,
        autoUpdatesEnabled: true,
        betaUpdates: false,
      });
      setHasUnsavedChanges(true);
    }
  };

  const exportSettings = () => {
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'augment-vip-settings.json';
    link.click();
    URL.revokeObjectURL(url);
  };

  const importSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const importedSettings = JSON.parse(e.target?.result as string);
          setSettings(importedSettings);
          setHasUnsavedChanges(true);
          addOperationLog({
            operation: 'import' as any,
            status: 'success',
            message: 'Settings imported successfully',
            details: `Imported from ${file.name}`,
          });
        } catch (error) {
          alert('Failed to import settings. Please check the file format.');
        }
      };
      reader.readAsText(file);
    }
  };

  const renderNotificationsSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Notification Preferences</h3>
        <div className="space-y-3">
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={settings.enableNotifications}
              onChange={(e) => updateSetting('enableNotifications', e.target.checked)}
              className="rounded"
            />
            <div>
              <span className="text-sm font-medium">Enable notifications</span>
              <p className="text-xs text-gray-500">Show notifications for operations and events</p>
            </div>
          </label>

          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={settings.soundEnabled}
              onChange={(e) => updateSetting('soundEnabled', e.target.checked)}
              className="rounded"
            />
            <div>
              <span className="text-sm font-medium">Sound notifications</span>
              <p className="text-xs text-gray-500">Play sounds for important notifications</p>
            </div>
          </label>

          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={settings.showDesktopNotifications}
              onChange={(e) => updateSetting('showDesktopNotifications', e.target.checked)}
              className="rounded"
            />
            <div>
              <span className="text-sm font-medium">Desktop notifications</span>
              <p className="text-xs text-gray-500">Show system notifications on desktop</p>
            </div>
          </label>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">Notification Duration</label>
        <select
          value={settings.notificationDuration}
          onChange={(e) => updateSetting('notificationDuration', parseInt(e.target.value))}
          className="w-full px-3 py-2 border border-gray-300 rounded-md"
        >
          <option value={3}>3 seconds</option>
          <option value={5}>5 seconds</option>
          <option value={10}>10 seconds</option>
          <option value={0}>Until dismissed</option>
        </select>
      </div>
    </div>
  );

  const renderOperationsSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Automatic Operations</h3>
        <div className="space-y-4">
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={settings.autoCleanEnabled}
              onChange={(e) => updateSetting('autoCleanEnabled', e.target.checked)}
              className="rounded"
            />
            <div>
              <span className="text-sm font-medium">Enable automatic cleaning</span>
              <p className="text-xs text-gray-500">Automatically run cleanup operations</p>
            </div>
          </label>

          {settings.autoCleanEnabled && (
            <div className="ml-6">
              <label className="block text-sm font-medium mb-2">Clean every</label>
              <select
                value={settings.autoCleanInterval}
                onChange={(e) => updateSetting('autoCleanInterval', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value={6}>6 hours</option>
                <option value={12}>12 hours</option>
                <option value={24}>24 hours</option>
                <option value={48}>48 hours</option>
                <option value={168}>1 week</option>
              </select>
            </div>
          )}
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-4">Safety & Confirmation</h3>
        <div className="space-y-3">
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={settings.confirmDangerousOperations}
              onChange={(e) => updateSetting('confirmDangerousOperations', e.target.checked)}
              className="rounded"
            />
            <div>
              <span className="text-sm font-medium">Confirm dangerous operations</span>
              <p className="text-xs text-gray-500">Ask for confirmation before destructive actions</p>
            </div>
          </label>

          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={settings.showOperationProgress}
              onChange={(e) => updateSetting('showOperationProgress', e.target.checked)}
              className="rounded"
            />
            <div>
              <span className="text-sm font-medium">Show operation progress</span>
              <p className="text-xs text-gray-500">Display progress dialogs during operations</p>
            </div>
          </label>
        </div>
      </div>
    </div>
  );

  const renderBackupSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Automatic Backup</h3>
        <div className="space-y-4">
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={settings.autoBackupEnabled}
              onChange={(e) => updateSetting('autoBackupEnabled', e.target.checked)}
              className="rounded"
            />
            <div>
              <span className="text-sm font-medium">Enable automatic backups</span>
              <p className="text-xs text-gray-500">Automatically create backups before operations</p>
            </div>
          </label>

          <div>
            <label className="block text-sm font-medium mb-2">Backup Location</label>
            <div className="flex space-x-2">
              <input
                type="text"
                value={settings.backupLocation}
                onChange={(e) => updateSetting('backupLocation', e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
              />
              <Button size="sm" variant="outline">
                <Folder className="w-4 h-4" />
              </Button>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Backup Retention</label>
            <select
              value={settings.backupRetention}
              onChange={(e) => updateSetting('backupRetention', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value={7}>7 days</option>
              <option value={14}>14 days</option>
              <option value={30}>30 days</option>
              <option value={90}>90 days</option>
              <option value={365}>1 year</option>
            </select>
          </div>

          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={settings.compressBackups}
              onChange={(e) => updateSetting('compressBackups', e.target.checked)}
              className="rounded"
            />
            <div>
              <span className="text-sm font-medium">Compress backups</span>
              <p className="text-xs text-gray-500">Reduce backup file size using compression</p>
            </div>
          </label>
        </div>
      </div>
    </div>
  );

  const renderPerformanceSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Resource Management</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Maximum Concurrent Operations</label>
            <input
              type="number"
              value={settings.maxConcurrentOperations}
              onChange={(e) => updateSetting('maxConcurrentOperations', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              min="1"
              max="10"
            />
            <p className="text-xs text-gray-500 mt-1">Higher values may improve speed but use more resources</p>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Memory Limit (MB)</label>
            <input
              type="number"
              value={settings.memoryLimit}
              onChange={(e) => updateSetting('memoryLimit', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              min="256"
              max="4096"
              step="256"
            />
            <p className="text-xs text-gray-500 mt-1">Maximum memory usage for operations</p>
          </div>

          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={settings.enableHardwareAcceleration}
              onChange={(e) => updateSetting('enableHardwareAcceleration', e.target.checked)}
              className="rounded"
            />
            <div>
              <span className="text-sm font-medium">Enable hardware acceleration</span>
              <p className="text-xs text-gray-500">Use GPU acceleration when available</p>
            </div>
          </label>
        </div>
      </div>
    </div>
  );

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Access Control</h3>
        <div className="space-y-3">
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={settings.requirePasswordForOperations}
              onChange={(e) => updateSetting('requirePasswordForOperations', e.target.checked)}
              className="rounded"
            />
            <div>
              <span className="text-sm font-medium">Require password for operations</span>
              <p className="text-xs text-gray-500">Protect sensitive operations with password</p>
            </div>
          </label>

          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={settings.encryptBackups}
              onChange={(e) => updateSetting('encryptBackups', e.target.checked)}
              className="rounded"
            />
            <div>
              <span className="text-sm font-medium">Encrypt backups</span>
              <p className="text-xs text-gray-500">Encrypt backup files for security</p>
            </div>
          </label>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-4">Logging & Audit</h3>
        <div className="space-y-4">
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={settings.logOperations}
              onChange={(e) => updateSetting('logOperations', e.target.checked)}
              className="rounded"
            />
            <div>
              <span className="text-sm font-medium">Log all operations</span>
              <p className="text-xs text-gray-500">Keep detailed logs of all operations</p>
            </div>
          </label>

          <div>
            <label className="block text-sm font-medium mb-2">Maximum Log Entries</label>
            <input
              type="number"
              value={settings.maxLogEntries}
              onChange={(e) => updateSetting('maxLogEntries', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              min="100"
              max="10000"
              step="100"
            />
          </div>
        </div>
      </div>
    </div>
  );

  const renderAdvancedSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Developer Options</h3>
        <div className="space-y-3">
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={settings.debugMode}
              onChange={(e) => updateSetting('debugMode', e.target.checked)}
              className="rounded"
            />
            <div>
              <span className="text-sm font-medium">Debug mode</span>
              <p className="text-xs text-gray-500">Enable detailed logging and debug features</p>
            </div>
          </label>

          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={settings.telemetryEnabled}
              onChange={(e) => updateSetting('telemetryEnabled', e.target.checked)}
              className="rounded"
            />
            <div>
              <span className="text-sm font-medium">Send usage data</span>
              <p className="text-xs text-gray-500">Help improve the app by sending anonymous usage data</p>
            </div>
          </label>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-4">Updates</h3>
        <div className="space-y-3">
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={settings.autoUpdatesEnabled}
              onChange={(e) => updateSetting('autoUpdatesEnabled', e.target.checked)}
              className="rounded"
            />
            <div>
              <span className="text-sm font-medium">Automatic updates</span>
              <p className="text-xs text-gray-500">Automatically download and install updates</p>
            </div>
          </label>

          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={settings.betaUpdates}
              onChange={(e) => updateSetting('betaUpdates', e.target.checked)}
              className="rounded"
            />
            <div>
              <span className="text-sm font-medium">Beta updates</span>
              <p className="text-xs text-gray-500">Receive pre-release versions with new features</p>
            </div>
          </label>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-4">Data Management</h3>
        <div className="space-y-3">
          <Button variant="outline" onClick={exportSettings} className="w-full">
            <Download className="w-4 h-4 mr-2" />
            Export Settings
          </Button>

          <div>
            <input
              type="file"
              accept=".json"
              onChange={importSettings}
              className="hidden"
              id="import-settings"
            />
            <Button
              variant="outline"
              onClick={() => document.getElementById('import-settings')?.click()}
              className="w-full"
            >
              <Upload className="w-4 h-4 mr-2" />
              Import Settings
            </Button>
          </div>

          <Button variant="outline" onClick={resetSettings} className="w-full text-red-600 hover:text-red-700">
            <RotateCcw className="w-4 h-4 mr-2" />
            Reset to Defaults
          </Button>
        </div>
      </div>
    </div>
  );

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Appearance</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Theme</label>
            <div className="flex space-x-2">
              {[
                { value: 'light', label: 'Light', icon: Sun },
                { value: 'dark', label: 'Dark', icon: Moon },
                { value: 'system', label: 'System', icon: Monitor },
              ].map(({ value, label, icon: Icon }) => (
                <button
                  key={value}
                  onClick={() => updateSetting('theme', value as any)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
                    settings.theme === value
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{label}</span>
                </button>
              ))}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Language</label>
            <select
              value={settings.language}
              onChange={(e) => updateSetting('language', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="en">English</option>
              <option value="es">Español</option>
              <option value="fr">Français</option>
              <option value="de">Deutsch</option>
              <option value="zh">中文</option>
              <option value="ja">日本語</option>
            </select>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-4">Startup & Window</h3>
        <div className="space-y-3">
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={settings.autoStart}
              onChange={(e) => updateSetting('autoStart', e.target.checked)}
              className="rounded"
            />
            <div>
              <span className="text-sm font-medium">Start with system</span>
              <p className="text-xs text-gray-500">Launch Augment VIP when your computer starts</p>
            </div>
          </label>

          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={settings.minimizeToTray}
              onChange={(e) => updateSetting('minimizeToTray', e.target.checked)}
              className="rounded"
            />
            <div>
              <span className="text-sm font-medium">Minimize to system tray</span>
              <p className="text-xs text-gray-500">Hide window in system tray when minimized</p>
            </div>
          </label>

          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={settings.closeToTray}
              onChange={(e) => updateSetting('closeToTray', e.target.checked)}
              className="rounded"
            />
            <div>
              <span className="text-sm font-medium">Close to system tray</span>
              <p className="text-xs text-gray-500">Keep running in background when window is closed</p>
            </div>
          </label>
        </div>
      </div>
    </div>
  );

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">Settings</h1>
        <p className="text-gray-600">Configure your Augment VIP preferences and options</p>
      </div>

      <div className="flex gap-6">
        {/* Sidebar */}
        <div className="w-64 flex-shrink-0">
          <div className="space-y-1">
            {sections.map((section) => {
              const Icon = section.icon;
              return (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${
                    activeSection === section.id
                      ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border border-blue-200'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <div>
                    <div className="font-medium">{section.title}</div>
                    <div className="text-xs text-gray-500">{section.description}</div>
                  </div>
                </button>
              );
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1">
          <Card className="p-6">
            {activeSection === 'general' && renderGeneralSettings()}
            {activeSection === 'notifications' && renderNotificationsSettings()}
            {activeSection === 'operations' && renderOperationsSettings()}
            {activeSection === 'backup' && renderBackupSettings()}
            {activeSection === 'security' && renderSecuritySettings()}
            {activeSection === 'performance' && renderPerformanceSettings()}
            {activeSection === 'advanced' && renderAdvancedSettings()}
          </Card>

          {/* Save/Reset Actions */}
          {hasUnsavedChanges && (
            <div className="mt-4 flex justify-between items-center p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="w-5 h-5 text-yellow-600" />
                <span className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                  You have unsaved changes
                </span>
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" onClick={() => setHasUnsavedChanges(false)}>
                  Discard
                </Button>
                <Button size="sm" onClick={saveSettings}>
                  <Save className="w-4 h-4 mr-2" />
                  Save Changes
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Settings;
