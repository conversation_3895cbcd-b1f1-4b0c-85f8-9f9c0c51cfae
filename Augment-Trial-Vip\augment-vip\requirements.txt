# Core Framework
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Database & ORM
sqlalchemy>=2.0.23
alembic>=1.13.0
asyncpg>=0.29.0  # PostgreSQL async driver
aiomysql>=0.2.0  # MySQL async driver
aiosqlite>=0.19.0  # SQLite async driver
redis>=5.0.1

# Security & Authentication
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6
cryptography>=41.0.8

# HTTP & Networking
httpx>=0.25.2
aiohttp>=3.9.1
websockets>=12.0

# Background Tasks & Queue
celery>=5.3.4
kombu>=5.3.4

# Monitoring & Logging
structlog>=23.2.0
sentry-sdk[fastapi]>=1.38.0
prometheus-client>=0.19.0

# Development & Testing
pytest>=7.4.3
pytest-asyncio>=0.21.1
pytest-cov>=4.1.0
httpx>=0.25.2  # For testing async clients
factory-boy>=3.3.0

# Utilities
python-dotenv>=1.0.0
typer>=0.9.0  # Modern CLI framework (upgrade from click)
rich>=13.7.0  # Better console output (upgrade from colorama)
tqdm>=4.66.1  # Keep for progress bars

# Data Processing
pandas>=2.1.4
numpy>=1.26.2

# System Monitoring
psutil>=5.9.6

# File Operations
aiofiles>=23.2.1
watchdog>=3.0.0

# Validation & Serialization
email-validator>=2.1.0
python-slugify>=8.0.1

# Development Tools (optional, for development environment)
black>=23.11.0
isort>=5.12.0
mypy>=1.7.1
pre-commit>=3.6.0
