import React, { useEffect, useState, Suspense, memo } from "react";
import Layout from "./components/Layout";
import { useUIStore, useSystemInfo } from "./core/store";
import { initializeServices, destroyServices, isServicesReady } from "./core/services/ServiceManager";
import { CacheService } from "./core/services/CacheService";
import { PerformanceService } from "./core/services/PerformanceService";
import LoadingSpinner from "./components/LoadingSpinner";
import {
  LazyDashboard,
  LazyOperations,
  LazyBackup,
  LazySettings,
  ComponentLoader
} from "./shared/components/OptimizedComponents";
import { usePerformance } from "./shared/hooks/usePerformance";
import { PerformanceDashboard } from "./shared/components/PerformanceDashboard";
import { CommandPalette } from "./shared/components/CommandPalette";
import { WebSocketService } from "./core/services/WebSocketService";
import { PluginService } from "./core/services/PluginService";
import { AuthService } from "./core/services/AuthService";
import { MonitoringService } from "./core/services/MonitoringService";
import { UserManagementService } from "./core/services/UserManagementService";
import { useGlobalShortcuts, useNavigationShortcuts } from "./shared/hooks/useKeyboardShortcuts";
import { PageTransition } from "./shared/components/AnimationSystem";
import { LoginForm } from "./shared/components/LoginForm";
import { EnterpriseDashboard } from "./shared/components/EnterpriseDashboard";

// Memoized tab components for better performance
const MemoizedTabContent = memo(({
  activeTab,
  userManagementService,
  monitoringService,
  authService
}: {
  activeTab: string;
  userManagementService?: any;
  monitoringService?: any;
  authService?: any;
}) => {
  const { metrics } = usePerformance('TabContent');

  switch (activeTab) {
    case "dashboard":
      return (
        <Suspense fallback={<ComponentLoader name="Dashboard" />}>
          <EnterpriseDashboard
            userManagementService={userManagementService}
            monitoringService={monitoringService}
            authService={authService}
          />
        </Suspense>
      );
    case "operations":
      return (
        <Suspense fallback={<ComponentLoader name="Operations" />}>
          <LazyOperations />
        </Suspense>
      );
    case "backups":
      return (
        <Suspense fallback={<ComponentLoader name="Backup" />}>
          <LazyBackup />
        </Suspense>
      );
    case "settings":
      return (
        <Suspense fallback={<ComponentLoader name="Settings" />}>
          <LazySettings />
        </Suspense>
      );
    default:
      return (
        <Suspense fallback={<ComponentLoader name="Dashboard" />}>
          <EnterpriseDashboard
            userManagementService={userManagementService}
            monitoringService={monitoringService}
            authService={authService}
          />
        </Suspense>
      );
  }
});

MemoizedTabContent.displayName = 'MemoizedTabContent';

// Demo mode flag to disable WebSocket for clean demo
const DEMO_MODE = true;

function App() {
  const { activeTab, theme, setTheme, setActiveTab, addNotification, setLoading, setError } = useUIStore();
  const { setSystemInfo, setAppVersion } = useSystemInfo();
  const [servicesInitialized, setServicesInitialized] = useState(false);
  const [cacheService, setCacheService] = useState<CacheService | null>(null);
  const [performanceService, setPerformanceService] = useState<PerformanceService | null>(null);
  const [webSocketService, setWebSocketService] = useState<WebSocketService | null>(null);
  const [pluginService, setPluginService] = useState<PluginService | null>(null);
  const [authService, setAuthService] = useState<AuthService | null>(null);
  const [monitoringService, setMonitoringService] = useState<MonitoringService | null>(null);
  const [userManagementService, setUserManagementService] = useState<UserManagementService | null>(null);
  const [isCommandPaletteOpen, setIsCommandPaletteOpen] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loginError, setLoginError] = useState<string>('');
  const [isLoggingIn, setIsLoggingIn] = useState(false);

  const { metrics } = usePerformance('App', {
    enableProfiling: true,
    trackMemory: true,
    logSlowRenders: true,
  });

  // Global keyboard shortcuts
  useGlobalShortcuts({
    onOpenCommandPalette: () => setIsCommandPaletteOpen(true),
    onToggleTheme: () => {
      const newTheme = theme === 'light' ? 'dark' : theme === 'dark' ? 'system' : 'light';
      setTheme(newTheme);
    },
    onToggleSidebar: () => {
      // Would integrate with sidebar state
      console.log('Toggle sidebar');
    },
    onRefresh: () => {
      window.location.reload();
    },
  });

  // Navigation shortcuts
  useNavigationShortcuts({
    onNavigateToDashboard: () => setActiveTab('dashboard'),
    onNavigateToOperations: () => setActiveTab('operations'),
    onNavigateToBackups: () => setActiveTab('backups'),
    onNavigateToSettings: () => setActiveTab('settings'),
  });

  // Handle login
  const handleLogin = async (credentials: { email: string; password: string; rememberMe: boolean }) => {
    if (!authService) {
      setLoginError('Authentication service not available');
      return;
    }

    setIsLoggingIn(true);
    setLoginError('');

    try {
      const result = await authService.login(credentials);

      if (result.success) {
        setIsAuthenticated(true);
        addNotification({
          type: 'success',
          title: 'Welcome back!',
          message: `Successfully signed in as ${result.user?.email}`,
        });
      } else {
        setLoginError(result.error || 'Login failed');
      }
    } catch (error) {
      setLoginError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setIsLoggingIn(false);
    }
  };

  // Handle logout
  const handleLogout = async () => {
    if (authService) {
      await authService.logout();
      setIsAuthenticated(false);
      addNotification({
        type: 'info',
        title: 'Signed out',
        message: 'You have been successfully signed out',
      });
    }
  };

  useEffect(() => {
    const initializeApp = async () => {
      const startTime = performance.now();

      try {
        setLoading('global', true);

        // Initialize performance monitoring first
        const perfService = new PerformanceService({
          slowRender: 16,
          slowNetwork: 1000,
          highMemory: 100,
          longOperation: 5000,
        });
        await perfService.initialize();
        setPerformanceService(perfService);

        // Initialize cache service
        const cache = new CacheService({
          maxSize: 50 * 1024 * 1024, // 50MB
          defaultTTL: 5 * 60 * 1000, // 5 minutes
          maxEntries: 1000,
        });
        await cache.initialize();
        setCacheService(cache);

        // Initialize WebSocket service (conditional for demo)
        if (!DEMO_MODE) {
          const wsService = new WebSocketService({
            url: 'ws://localhost:8080/ws',
            reconnectInterval: 5000,
            maxReconnectAttempts: 3,
          });
          await wsService.initialize();
          setWebSocketService(wsService);
        } else {
          console.log('🚀 Demo Mode: WebSocket service disabled - running in offline mode');
        }

        // Initialize authentication service
        const auth = new AuthService();
        await auth.initialize();
        setAuthService(auth);

        // Make services globally available
        (globalThis as any).authService = auth;
        (globalThis as any).performanceService = perfService;
        (globalThis as any).cacheService = cache;

        // Initialize monitoring service
        const monitoring = new MonitoringService();
        await monitoring.initialize();
        setMonitoringService(monitoring);

        // Initialize user management service
        const userMgmt = new UserManagementService();
        await userMgmt.initialize();
        setUserManagementService(userMgmt);

        // Initialize plugin service
        const plugins = new PluginService();
        await plugins.initialize();
        setPluginService(plugins);

        // Check authentication status
        setIsAuthenticated(auth.isAuthenticated());

        // Initialize theme from system/storage
        if (window.themeAPI) {
          const savedTheme = window.themeAPI.getTheme();
          setTheme(savedTheme as any);
        }

        // Initialize services
        await initializeServices();
        setServicesInitialized(true);

        // Load system information with caching
        if (window.electronAPI) {
          try {
            const systemInfo = await cache.getOrSet(
              'system-info',
              async () => {
                const electronSystemInfo = await window.electronAPI.getSystemInfo();
                return {
                  platform: electronSystemInfo.platform,
                  arch: electronSystemInfo.arch,
                  version: electronSystemInfo.version,
                  homedir: electronSystemInfo.homedir,
                  username: electronSystemInfo.username,
                  totalMemory: 0,
                  freeMemory: 0,
                  cpuCount: 0,
                  uptime: 0,
                };
              },
              60000 // Cache for 1 minute
            );
            setSystemInfo(systemInfo);

            const appVersion = await cache.getOrSet(
              'app-version',
              () => window.electronAPI.getAppVersion(),
              300000 // Cache for 5 minutes
            );
            setAppVersion(appVersion);
          } catch (error) {
            console.error("Failed to load system info:", error);
            setError('global', 'Failed to load system information');
            perfService.recordMetric('error:system_info', 1, 'operation', { error });
          }
        }

        // Set up command output listener
        if (window.electronAPI) {
          window.electronAPI.onCommandOutput((data) => {
            console.log("Command output:", data);
            perfService.recordUserInteraction('command_output', 0, { data });
          });

          window.electronAPI.onQuickClean(() => {
            console.log("Quick clean triggered from system tray");
            perfService.recordUserInteraction('quick_clean_tray', 0);
            addNotification({
              type: 'info',
              title: 'Quick Clean',
              message: 'Quick clean operation triggered from system tray',
            });
          });
        }

        const initTime = performance.now() - startTime;
        perfService.recordMetric('app:initialization', initTime, 'operation');

        addNotification({
          type: 'success',
          title: 'Application Ready',
          message: `Augment VIP Desktop initialized in ${Math.round(initTime)}ms`,
        });

      } catch (error) {
        const initTime = performance.now() - startTime;
        console.error("Failed to initialize app:", error);
        setError('global', 'Failed to initialize application');

        if (performanceService) {
          performanceService.recordMetric('app:initialization_failed', initTime, 'operation', { error });
        }

        addNotification({
          type: 'error',
          title: 'Initialization Failed',
          message: 'Failed to initialize the application. Please restart.',
          persistent: true,
        });
      } finally {
        setLoading('global', false);
      }
    };

    initializeApp();

    // Cleanup on unmount
    return () => {
      if (window.electronAPI) {
        window.electronAPI.removeAllListeners("command-output");
        window.electronAPI.removeAllListeners("quick-clean");
      }

      // Destroy services
      Promise.all([
        destroyServices(),
        cacheService?.destroy(),
        performanceService?.destroy(),
        // webSocketService?.destroy(), // Disabled for demo
        pluginService?.destroy(),
        authService?.destroy(),
        monitoringService?.destroy(),
        userManagementService?.destroy(),
      ]).catch(console.error);
    };
  }, [setTheme, setSystemInfo, setAppVersion, addNotification, setLoading, setError]);

  // Performance monitoring for tab changes
  useEffect(() => {
    if (performanceService) {
      const startTime = performance.now();

      // Record tab change performance
      const cleanup = () => {
        const duration = performance.now() - startTime;
        performanceService.recordUserInteraction('tab_change', duration, {
          tab: activeTab,
          metrics: metrics
        });
      };

      return cleanup;
    }
  }, [activeTab, performanceService, metrics]);

  const renderActiveTab = () => {
    if (!servicesInitialized || !isServicesReady()) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <LoadingSpinner size="lg" />
            <p className="mt-4 text-gray-600 dark:text-gray-400">
              Initializing services...
            </p>
            {performanceService && (
              <p className="mt-2 text-sm text-gray-500 dark:text-gray-500">
                Performance monitoring active
              </p>
            )}
          </div>
        </div>
      );
    }

    return (
      <MemoizedTabContent
        activeTab={activeTab}
        userManagementService={userManagementService}
        monitoringService={monitoringService}
        authService={authService}
      />
    );
  };

  // Show login form if not authenticated
  if (!isAuthenticated) {
    return (
      <div className={theme === "dark" ? "dark" : ""}>
        <LoginForm
          onLogin={handleLogin}
          isLoading={isLoggingIn}
          error={loginError}
        />
      </div>
    );
  }

  return (
    <div className={theme === "dark" ? "dark" : ""}>
      <Layout>
        <PageTransition isVisible={servicesInitialized && isServicesReady()}>
          <React.Suspense fallback={<ComponentLoader name="Content" />}>
            {renderActiveTab()}
          </React.Suspense>
        </PageTransition>
      </Layout>

      {/* Command Palette */}
      <CommandPalette
        isOpen={isCommandPaletteOpen}
        onClose={() => setIsCommandPaletteOpen(false)}
        commands={[
          // Add logout command
          {
            id: 'logout',
            title: 'Sign Out',
            description: 'Sign out of your account',
            icon: <span>🚪</span>,
            category: 'navigation' as const,
            keywords: ['logout', 'signout', 'exit'],
            action: handleLogout,
          },
          // Plugin commands
          ...(pluginService?.getCommands().map(cmd => ({
            id: cmd.id,
            title: cmd.title,
            description: cmd.description,
            icon: <span>🔌</span>,
            category: 'operations' as const,
            keywords: [cmd.title.toLowerCase()],
            action: cmd.action,
            shortcut: cmd.shortcut,
          })) || [])
        ]}
      />

      {/* Performance Dashboard - only in development */}
      {process.env.NODE_ENV === 'development' && <PerformanceDashboard />}
    </div>
  );
}

export default App;
