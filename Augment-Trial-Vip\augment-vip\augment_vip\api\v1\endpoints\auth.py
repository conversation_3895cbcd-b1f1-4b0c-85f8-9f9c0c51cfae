"""
Authentication Endpoints
JWT token management and user authentication
"""

from datetime import <PERSON><PERSON><PERSON>
from typing import Dict, Any

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials
from pydantic import BaseModel, Field
import structlog

from ....core.config import get_settings
from ....core.security import <PERSON><PERSON><PERSON><PERSON><PERSON>, get_current_user, security
from ....core.exceptions import AuthenticationError, ValidationError

logger = structlog.get_logger(__name__)
settings = get_settings()

router = APIRouter()


class LoginRequest(BaseModel):
    """Login request model"""
    username: str = Field(..., min_length=3, max_length=50)
    password: str = Field(..., min_length=settings.PASSWORD_MIN_LENGTH)


class TokenResponse(BaseModel):
    """Token response model"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


class RefreshTokenRequest(BaseModel):
    """Refresh token request model"""
    refresh_token: str


class UserInfo(BaseModel):
    """User information model"""
    user_id: str
    username: str
    permissions: list[str] = []


@router.post("/login", response_model=TokenResponse)
async def login(login_data: LoginRequest) -> TokenResponse:
    """
    Authenticate user and return JWT tokens
    """
    logger.info("Login attempt", username=login_data.username)
    
    # In a real application, verify credentials against database
    # For now, we'll use a simple check
    if not _verify_credentials(login_data.username, login_data.password):
        logger.warning("Login failed", username=login_data.username)
        raise AuthenticationError("Invalid credentials")
    
    # Create tokens
    user_data = {"sub": login_data.username, "username": login_data.username}
    
    access_token = AuthManager.create_access_token(
        data=user_data,
        expires_delta=timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    )
    
    refresh_token = AuthManager.create_refresh_token(data=user_data)
    
    logger.info("Login successful", username=login_data.username)
    
    return TokenResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    )


@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(refresh_data: RefreshTokenRequest) -> TokenResponse:
    """
    Refresh access token using refresh token
    """
    try:
        # Verify refresh token
        payload = AuthManager.verify_token(refresh_data.refresh_token, token_type="refresh")
        
        # Create new access token
        user_data = {"sub": payload["sub"], "username": payload["username"]}
        
        access_token = AuthManager.create_access_token(
            data=user_data,
            expires_delta=timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        )
        
        # Create new refresh token
        new_refresh_token = AuthManager.create_refresh_token(data=user_data)
        
        logger.info("Token refreshed", username=payload["username"])
        
        return TokenResponse(
            access_token=access_token,
            refresh_token=new_refresh_token,
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        )
        
    except AuthenticationError:
        raise
    except Exception as e:
        logger.error("Token refresh failed", error=str(e))
        raise AuthenticationError("Invalid refresh token")


@router.get("/me", response_model=UserInfo)
async def get_current_user_info(
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> UserInfo:
    """
    Get current user information
    """
    return UserInfo(
        user_id=current_user["user_id"],
        username=current_user["payload"]["username"],
        permissions=["read", "write"]  # Placeholder permissions
    )


@router.post("/logout")
async def logout(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, str]:
    """
    Logout user (invalidate token)
    """
    # In a real application, you would add the token to a blacklist
    logger.info("User logged out", username=current_user["payload"]["username"])
    
    return {"message": "Successfully logged out"}


class ChangePasswordRequest(BaseModel):
    """Change password request model"""
    old_password: str = Field(..., min_length=settings.PASSWORD_MIN_LENGTH)
    new_password: str = Field(..., min_length=settings.PASSWORD_MIN_LENGTH)


@router.post("/change-password")
async def change_password(
    password_data: ChangePasswordRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, str]:
    """
    Change user password
    """
    username = current_user["payload"]["username"]

    # Verify old password
    if not _verify_credentials(username, password_data.old_password):
        raise AuthenticationError("Invalid current password")

    # Validate new password
    if password_data.old_password == password_data.new_password:
        raise ValidationError("New password must be different from current password")

    # In a real application, update password in database
    new_password_hash = AuthManager.hash_password(password_data.new_password)
    
    logger.info("Password changed", username=username)
    
    return {"message": "Password changed successfully"}


def _verify_credentials(username: str, password: str) -> bool:
    """
    Verify user credentials (placeholder implementation)
    In a real application, this would check against the database
    """
    # Simple demo credentials
    demo_users = {
        "admin": "admin123",
        "user": "user123",
        "demo": "demo123"
    }
    
    return demo_users.get(username) == password
