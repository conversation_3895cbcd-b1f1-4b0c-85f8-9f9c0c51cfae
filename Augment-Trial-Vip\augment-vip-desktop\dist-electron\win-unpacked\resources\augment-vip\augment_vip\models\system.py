"""
System Models
Database models for system state and metrics
"""

from datetime import datetime
from typing import Optional

from sqlalchemy import String, DateTime, Float, Integer, Text, Boolean
from sqlalchemy.orm import Mapped, mapped_column

from ..core.database import Base


class SystemState(Base):
    """System state snapshot model"""
    
    __tablename__ = "system_states"
    
    id: Mapped[int] = mapped_column(primary_key=True)
    
    # System information
    platform: Mapped[str] = mapped_column(String(50), nullable=False)
    architecture: Mapped[str] = mapped_column(String(20), nullable=False)
    python_version: Mapped[str] = mapped_column(String(20), nullable=False)
    
    # Resource usage
    cpu_percent: Mapped[float] = mapped_column(Float, nullable=False)
    memory_total: Mapped[int] = mapped_column(Integer, nullable=False)
    memory_used: Mapped[int] = mapped_column(Integer, nullable=False)
    memory_percent: Mapped[float] = mapped_column(Float, nullable=False)
    disk_total: Mapped[int] = mapped_column(Integer, nullable=False)
    disk_used: Mapped[int] = mapped_column(Integer, nullable=False)
    disk_percent: Mapped[float] = mapped_column(Float, nullable=False)
    
    # Process information
    total_processes: Mapped[int] = mapped_column(Integer, nullable=False)
    running_applications: Mapped[Optional[str]] = mapped_column(Text)  # JSON array
    
    # Analysis results
    warnings: Mapped[Optional[str]] = mapped_column(Text)  # JSON array
    recommendations: Mapped[Optional[str]] = mapped_column(Text)  # JSON array
    safe_to_proceed: Mapped[bool] = mapped_column(Boolean, default=True)
    
    # Timing
    timestamp: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    analysis_duration: Mapped[float] = mapped_column(Float, nullable=False)
    
    # User context
    user_id: Mapped[Optional[str]] = mapped_column(String(50))
    
    def __repr__(self) -> str:
        return f"<SystemState(id={self.id}, timestamp='{self.timestamp}', platform='{self.platform}')>"


class SystemMetric(Base):
    """System metrics time series model"""
    
    __tablename__ = "system_metrics"
    
    id: Mapped[int] = mapped_column(primary_key=True)
    
    # Metric identification
    metric_name: Mapped[str] = mapped_column(String(100), nullable=False)
    metric_type: Mapped[str] = mapped_column(String(20), nullable=False)  # counter, gauge, histogram
    
    # Metric value
    value: Mapped[float] = mapped_column(Float, nullable=False)
    
    # Labels and context
    labels: Mapped[Optional[str]] = mapped_column(Text)  # JSON object
    instance: Mapped[str] = mapped_column(String(50), default="default")
    
    # Timing
    timestamp: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    def __repr__(self) -> str:
        return f"<SystemMetric(id={self.id}, name='{self.metric_name}', value={self.value}, timestamp='{self.timestamp}')>"
