import { create } from 'zustand';

export interface OperationLog {
  id: string;
  timestamp: Date;
  operation: 'clean' | 'modify-ids' | 'all';
  status: 'running' | 'success' | 'error';
  message: string;
  details?: string;
}

export interface SystemInfo {
  platform: string;
  arch: string;
  version: string;
  homedir: string;
  username: string;
}

export interface AppState {
  // Theme
  theme: 'light' | 'dark';
  
  // System info
  systemInfo: SystemInfo | null;
  appVersion: string;
  
  // Operations
  isOperationRunning: boolean;
  currentOperation: string | null;
  operationProgress: number;
  operationLogs: OperationLog[];
  
  // Settings
  autoCleanEnabled: boolean;
  autoCleanInterval: number; // in hours
  showNotifications: boolean;
  minimizeToTray: boolean;
  
  // UI State
  activeTab: 'dashboard' | 'operations' | 'backups' | 'settings';
  sidebarCollapsed: boolean;
}

export interface AppActions {
  // Theme actions
  setTheme: (theme: 'light' | 'dark') => void;
  toggleTheme: () => void;
  
  // System info actions
  setSystemInfo: (info: SystemInfo) => void;
  setAppVersion: (version: string) => void;
  
  // Operation actions
  startOperation: (operation: string) => void;
  finishOperation: (success: boolean, message: string, details?: string) => void;
  updateOperationProgress: (progress: number) => void;
  addOperationLog: (log: Omit<OperationLog, 'id' | 'timestamp'>) => void;
  clearOperationLogs: () => void;
  
  // Settings actions
  updateSettings: (settings: Partial<Pick<AppState, 'autoCleanEnabled' | 'autoCleanInterval' | 'showNotifications' | 'minimizeToTray'>>) => void;
  
  // UI actions
  setActiveTab: (tab: AppState['activeTab']) => void;
  toggleSidebar: () => void;
}

const useAppStore = create<AppState & AppActions>((set, get) => ({
  // Initial state
  theme: 'light',
  systemInfo: null,
  appVersion: '1.0.0',
  isOperationRunning: false,
  currentOperation: null,
  operationProgress: 0,
  operationLogs: [],
  autoCleanEnabled: false,
  autoCleanInterval: 24,
  showNotifications: true,
  minimizeToTray: true,
  activeTab: 'dashboard',
  sidebarCollapsed: false,

  // Theme actions
  setTheme: (theme) => {
    set({ theme });
    if (typeof window !== 'undefined' && window.themeAPI) {
      window.themeAPI.setTheme(theme);
    }
  },
  
  toggleTheme: () => {
    const currentTheme = get().theme;
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    get().setTheme(newTheme);
  },

  // System info actions
  setSystemInfo: (systemInfo) => set({ systemInfo }),
  setAppVersion: (appVersion) => set({ appVersion }),

  // Operation actions
  startOperation: (operation) => set({
    isOperationRunning: true,
    currentOperation: operation,
    operationProgress: 0
  }),
  
  finishOperation: (success, message, details) => {
    const { currentOperation } = get();
    set({
      isOperationRunning: false,
      currentOperation: null,
      operationProgress: 100
    });
    
    if (currentOperation) {
      get().addOperationLog({
        operation: currentOperation as any,
        status: success ? 'success' : 'error',
        message,
        details
      });
    }
  },
  
  updateOperationProgress: (operationProgress) => set({ operationProgress }),
  
  addOperationLog: (logData) => {
    const newLog: OperationLog = {
      ...logData,
      id: Date.now().toString(),
      timestamp: new Date()
    };
    
    set((state) => ({
      operationLogs: [newLog, ...state.operationLogs].slice(0, 100) // Keep only last 100 logs
    }));
  },
  
  clearOperationLogs: () => set({ operationLogs: [] }),

  // Settings actions
  updateSettings: (settings) => set(settings),

  // UI actions
  setActiveTab: (activeTab) => set({ activeTab }),
  toggleSidebar: () => set((state) => ({ sidebarCollapsed: !state.sidebarCollapsed }))
}));

export default useAppStore;
