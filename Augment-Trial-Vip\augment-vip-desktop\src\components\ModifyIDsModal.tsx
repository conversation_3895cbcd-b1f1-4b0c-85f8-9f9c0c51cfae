import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON>, 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>gle, 
  CheckCircle, 
  FileText,
  RefreshCw,
  Shield,
  Info,
  ArrowRight
} from 'lucide-react';

interface IDModificationPreview {
  storage_path: string;
  storage_exists: boolean;
  storage_size: number;
  current_machine_id: string | null;
  current_device_id: string | null;
  new_machine_id: string;
  new_device_id: string;
  backup_location: string;
  safe_to_modify: boolean;
  warnings: string[];
  recommendations: string[];
  other_telemetry_keys: Array<{
    key: string;
    value: string;
  }>;
}

interface ModifyIDsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

export const ModifyIDsModal: React.FC<ModifyIDsModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
}) => {
  const [preview, setPreview] = useState<IDModificationPreview | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [scanProgress, setScanProgress] = useState(0);
  const [scanStep, setScanStep] = useState("");

  useEffect(() => {
    if (isOpen && window.electronAPI) {
      startPreviewScan();
    }
  }, [isOpen]);

  const startPreviewScan = async () => {
    if (!window.electronAPI) return;

    setIsScanning(true);
    setScanProgress(0);
    setScanStep("Analyzing VS Code storage...");

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setScanProgress(prev => {
          if (prev >= 90) return prev;
          return prev + Math.random() * 20;
        });
      }, 200);

      const result = await window.electronAPI.executePythonCommand('preview-modify', ['--json']);
      clearInterval(progressInterval);
      setScanProgress(100);

      if (result.success && result.stdout) {
        const previewData = JSON.parse(result.stdout.trim());
        setPreview(previewData);
        setScanStep("Preview completed!");
      } else {
        throw new Error(result.stderr || "Preview failed");
      }
    } catch (error) {
      console.error("ID modification preview failed:", error);
      setScanStep(`Preview failed: ${error}`);
    } finally {
      setIsScanning(false);
    }
  };

  const formatSize = (bytes: number) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  const maskId = (id: string | null) => {
    if (!id || id === "Not set") return id;
    if (id.length <= 8) return id;
    return `${id.substring(0, 4)}...${id.substring(id.length - 4)}`;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-slate-800 rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-slate-600">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
              <Key className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Modify Telemetry IDs Preview
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Review ID changes before proceeding
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden flex flex-col">
          {isScanning ? (
            /* Scanning in Progress */
            <div className="flex-1 flex items-center justify-center p-8">
              <div className="text-center max-w-md">
                <Loader className="w-16 h-16 text-purple-600 dark:text-purple-400 mx-auto mb-4 animate-spin" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Analyzing VS Code Storage
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  {scanStep}
                </p>
                
                <div className="w-full bg-gray-200 dark:bg-slate-700 rounded-full h-2 mb-2">
                  <div
                    className="h-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full transition-all duration-300"
                    style={{ width: `${scanProgress}%` }}
                  />
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {Math.round(scanProgress)}% complete
                </div>
              </div>
            </div>
          ) : preview ? (
            /* Preview Results */
            <div className="flex-1 overflow-y-auto p-6">
              {/* Safety Status */}
              <div className={`p-4 rounded-xl border-2 mb-6 ${
                preview.safe_to_modify 
                  ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-700'
                  : 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-700'
              }`}>
                <div className="flex items-center space-x-3">
                  {preview.safe_to_modify ? (
                    <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
                  ) : (
                    <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
                  )}
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white">
                      {preview.safe_to_modify ? 'Safe to Modify' : 'Cannot Modify Safely'}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {preview.safe_to_modify 
                        ? 'The ID modification can proceed safely'
                        : 'Issues detected that prevent safe modification'
                      }
                    </p>
                  </div>
                </div>
              </div>

              {/* Storage Information */}
              <div className="bg-gray-50 dark:bg-slate-700 rounded-lg p-4 mb-6">
                <h4 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                  <FileText className="w-4 h-4 mr-2" />
                  Storage Information
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Location:</span>
                    <span className="text-gray-900 dark:text-white font-mono text-xs">
                      {preview.storage_path.split('/').pop() || preview.storage_path.split('\\').pop()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Size:</span>
                    <span className="text-gray-900 dark:text-white">
                      {formatSize(preview.storage_size)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Backup:</span>
                    <span className="text-gray-900 dark:text-white font-mono text-xs">
                      {preview.backup_location.split('/').pop() || preview.backup_location.split('\\').pop()}
                    </span>
                  </div>
                </div>
              </div>

              {/* ID Changes */}
              <div className="mb-6">
                <h4 className="font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  ID Changes
                </h4>
                
                <div className="space-y-4">
                  {/* Machine ID */}
                  <div className="bg-white dark:bg-slate-700 border border-gray-200 dark:border-slate-600 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h5 className="font-medium text-gray-900 dark:text-white">Machine ID</h5>
                      <span className="text-xs text-gray-500 dark:text-gray-400">telemetry.machineId</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="flex-1">
                        <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Current</div>
                        <div className="font-mono text-sm bg-gray-100 dark:bg-slate-600 p-2 rounded">
                          {maskId(preview.current_machine_id) || "Not set"}
                        </div>
                      </div>
                      <ArrowRight className="w-4 h-4 text-gray-400" />
                      <div className="flex-1">
                        <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">New</div>
                        <div className="font-mono text-sm bg-green-100 dark:bg-green-900/20 p-2 rounded">
                          {maskId(preview.new_machine_id)}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Device ID */}
                  <div className="bg-white dark:bg-slate-700 border border-gray-200 dark:border-slate-600 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h5 className="font-medium text-gray-900 dark:text-white">Device ID</h5>
                      <span className="text-xs text-gray-500 dark:text-gray-400">telemetry.devDeviceId</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="flex-1">
                        <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Current</div>
                        <div className="font-mono text-sm bg-gray-100 dark:bg-slate-600 p-2 rounded">
                          {maskId(preview.current_device_id) || "Not set"}
                        </div>
                      </div>
                      <ArrowRight className="w-4 h-4 text-gray-400" />
                      <div className="flex-1">
                        <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">New</div>
                        <div className="font-mono text-sm bg-green-100 dark:bg-green-900/20 p-2 rounded">
                          {maskId(preview.new_device_id)}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Other Telemetry Keys */}
              {preview.other_telemetry_keys.length > 0 && (
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                    <Info className="w-4 h-4 mr-2" />
                    Other Telemetry Settings (Unchanged)
                  </h4>
                  <div className="bg-gray-50 dark:bg-slate-700 rounded-lg p-4 max-h-32 overflow-y-auto">
                    <div className="space-y-2">
                      {preview.other_telemetry_keys.map((item, index) => (
                        <div key={index} className="text-sm flex justify-between">
                          <span className="font-mono text-xs text-blue-600 dark:text-blue-400">
                            {item.key}
                          </span>
                          <span className="text-gray-600 dark:text-gray-400 truncate ml-2">
                            {item.value}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Warnings */}
              {preview.warnings.length > 0 && (
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                    <AlertTriangle className="w-4 h-4 mr-2 text-yellow-500" />
                    Warnings
                  </h4>
                  <div className="space-y-2">
                    {preview.warnings.map((warning, index) => (
                      <div key={index} className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-3">
                        <p className="text-sm text-yellow-800 dark:text-yellow-200">{warning}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Recommendations */}
              {preview.recommendations.length > 0 && (
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                    <Shield className="w-4 h-4 mr-2 text-green-500" />
                    Recommendations
                  </h4>
                  <div className="space-y-2">
                    {preview.recommendations.map((recommendation, index) => (
                      <div key={index} className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-3">
                        <p className="text-sm text-blue-800 dark:text-blue-200">{recommendation}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            /* Error State */
            <div className="flex-1 flex items-center justify-center p-8">
              <div className="text-center">
                <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Preview Failed
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Unable to generate preview. Please try again.
                </p>
                <button
                  onClick={startPreviewScan}
                  className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  Retry Preview
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        {preview && !isScanning && (
          <div className="p-6 border-t border-gray-200 dark:border-slate-600 flex justify-between">
            <button
              onClick={onClose}
              className="px-6 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
            >
              Cancel
            </button>
            <div className="flex space-x-3">
              <button
                onClick={startPreviewScan}
                className="px-6 py-2 bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-slate-600 transition-colors"
              >
                Refresh Preview
              </button>
              <button
                onClick={handleConfirm}
                disabled={!preview.safe_to_modify}
                className={`px-6 py-2 rounded-lg font-medium transition-colors ${
                  preview.safe_to_modify
                    ? 'bg-purple-600 text-white hover:bg-purple-700'
                    : 'bg-gray-300 dark:bg-slate-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                }`}
              >
                Proceed with Modification
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
