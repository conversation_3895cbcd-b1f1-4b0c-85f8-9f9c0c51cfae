{"version": 3, "file": "plist.js", "sourceRoot": "", "sources": ["../../src/util/plist.ts"], "names": [], "mappings": ";;AA2BA,sCAIC;AAED,wCAGC;AApCD,iCAAoC;AACpC,kCAAiC;AAQjC,SAAS,cAAc,CAAC,GAAe;IACrC,IAAI,GAAG,KAAK,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QAC5C,OAAO,GAAG,CAAA;IACZ,CAAC;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QACvB,OAAO,GAAG,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;IAChC,CAAC;IAED,MAAM,MAAM,GAAgB,EAAE,CAAA;IAC9B,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;SACb,IAAI,EAAE;SACN,OAAO,CAAC,GAAG,CAAC,EAAE;QACb,MAAM,CAAC,GAAG,CAAC,GAAG,cAAc,CAAE,GAAmB,CAAC,GAAG,CAAC,CAAC,CAAA;IACzD,CAAC,CAAC,CAAA;IACJ,OAAO,MAAM,CAAA;AACf,CAAC;AAEM,KAAK,UAAU,aAAa,CAAC,IAAY,EAAE,IAAgB;IAChE,MAAM,UAAU,GAAG,cAAc,CAAC,IAAI,CAAC,CAAA;IACvC,MAAM,KAAK,GAAG,IAAA,aAAK,EAAC,UAAU,CAAC,CAAA;IAC/B,MAAM,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;AACjC,CAAC;AAEM,KAAK,UAAU,cAAc,CAAI,IAAY;IAClD,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;IAC5C,OAAO,IAAA,aAAK,EAAC,IAAI,CAAM,CAAA;AACzB,CAAC", "sourcesContent": ["import { build, parse } from \"plist\"\nimport * as fs from \"fs/promises\"\n\ntype PlistValue = string | number | boolean | Date | PlistObject | PlistValue[]\n\ninterface PlistObject {\n  [key: string]: PlistValue\n}\n\nfunction sortObjectKeys(obj: PlistValue): PlistValue {\n  if (obj === null || typeof obj !== \"object\") {\n    return obj\n  }\n\n  if (Array.isArray(obj)) {\n    return obj.map(sortObjectKeys)\n  }\n\n  const result: PlistObject = {}\n  Object.keys(obj)\n    .sort()\n    .forEach(key => {\n      result[key] = sortObjectKeys((obj as PlistObject)[key])\n    })\n  return result\n}\n\nexport async function savePlistFile(path: string, data: PlistValue): Promise<void> {\n  const sortedData = sortObjectKeys(data)\n  const plist = build(sortedData)\n  await fs.writeFile(path, plist)\n}\n\nexport async function parsePlistFile<T>(file: string): Promise<T> {\n  const data = await fs.readFile(file, \"utf8\")\n  return parse(data) as T\n}\n\nexport type { PlistValue, PlistObject }\n"]}