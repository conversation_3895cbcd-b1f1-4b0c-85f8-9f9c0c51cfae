import{r as V,j as c,D as Dt,M as dh,H as fh,A as Mf,S as Dn,a as Of,b as Ef,c as mh,X as Nu,d as hh,P as gh,O as xh,C as bh,T as yh,e as pf,f as ph,I as vh,L as pu,g as Hl,h as Mn,i as vf,k as Uf,l as Fs,m as Cf,n as jf,o as Su,p as jh,q as Is,s as Ws,F as Nh,t as Bf,u as Nf,v as Js,Z as vu,w as Sh,x as wh,y as Ah,B as zh,z as Th,E as kh,G as Dh,U as Mh}from"./ui-ZHYupI7r.js";import{r as Oh,a as Eh}from"./vendor-DJG_os-6.js";import{c as Uh}from"./utils-jn9BeX0-.js";(function(){const j=document.createElement("link").relList;if(j&&j.supports&&j.supports("modulepreload"))return;for(const w of document.querySelectorAll('link[rel="modulepreload"]'))o(w);new MutationObserver(w=>{for(const v of w)if(v.type==="childList")for(const O of v.addedNodes)O.tagName==="LINK"&&O.rel==="modulepreload"&&o(O)}).observe(document,{childList:!0,subtree:!0});function g(w){const v={};return w.integrity&&(v.integrity=w.integrity),w.referrerPolicy&&(v.referrerPolicy=w.referrerPolicy),w.crossOrigin==="use-credentials"?v.credentials="include":w.crossOrigin==="anonymous"?v.credentials="omit":v.credentials="same-origin",v}function o(w){if(w.ep)return;w.ep=!0;const v=g(w);fetch(w.href,v)}})();var hu={exports:{}},zn={},gu={exports:{}},xu={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sf;function Ch(){return Sf||(Sf=1,function(r){function j(f,z){var E=f.length;f.push(z);e:for(;0<E;){var ee=E-1>>>1,de=f[ee];if(0<w(de,z))f[ee]=z,f[E]=de,E=ee;else break e}}function g(f){return f.length===0?null:f[0]}function o(f){if(f.length===0)return null;var z=f[0],E=f.pop();if(E!==z){f[0]=E;e:for(var ee=0,de=f.length,ve=de>>>1;ee<ve;){var Se=2*(ee+1)-1,he=f[Se],Be=Se+1,Nt=f[Be];if(0>w(he,E))Be<de&&0>w(Nt,he)?(f[ee]=Nt,f[Be]=E,ee=Be):(f[ee]=he,f[Se]=E,ee=Se);else if(Be<de&&0>w(Nt,E))f[ee]=Nt,f[Be]=E,ee=Be;else break e}}return z}function w(f,z){var E=f.sortIndex-z.sortIndex;return E!==0?E:f.id-z.id}if(r.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var v=performance;r.unstable_now=function(){return v.now()}}else{var O=Date,D=O.now();r.unstable_now=function(){return O.now()-D}}var R=[],G=[],T=1,C=null,q=3,Z=!1,ae=!1,M=!1,Y=!1,$=typeof setTimeout=="function"?setTimeout:null,P=typeof clearTimeout=="function"?clearTimeout:null,F=typeof setImmediate<"u"?setImmediate:null;function K(f){for(var z=g(G);z!==null;){if(z.callback===null)o(G);else if(z.startTime<=f)o(G),z.sortIndex=z.expirationTime,j(R,z);else break;z=g(G)}}function ce(f){if(M=!1,K(f),!ae)if(g(R)!==null)ae=!0,k||(k=!0,U());else{var z=g(G);z!==null&&Ye(ce,z.startTime-f)}}var k=!1,ne=-1,ie=5,ke=-1;function Ke(){return Y?!0:!(r.unstable_now()-ke<ie)}function B(){if(Y=!1,k){var f=r.unstable_now();ke=f;var z=!0;try{e:{ae=!1,M&&(M=!1,P(ne),ne=-1),Z=!0;var E=q;try{t:{for(K(f),C=g(R);C!==null&&!(C.expirationTime>f&&Ke());){var ee=C.callback;if(typeof ee=="function"){C.callback=null,q=C.priorityLevel;var de=ee(C.expirationTime<=f);if(f=r.unstable_now(),typeof de=="function"){C.callback=de,K(f),z=!0;break t}C===g(R)&&o(R),K(f)}else o(R);C=g(R)}if(C!==null)z=!0;else{var ve=g(G);ve!==null&&Ye(ce,ve.startTime-f),z=!1}}break e}finally{C=null,q=E,Z=!1}z=void 0}}finally{z?U():k=!1}}}var U;if(typeof F=="function")U=function(){F(B)};else if(typeof MessageChannel<"u"){var re=new MessageChannel,Ae=re.port2;re.port1.onmessage=B,U=function(){Ae.postMessage(null)}}else U=function(){$(B,0)};function Ye(f,z){ne=$(function(){f(r.unstable_now())},z)}r.unstable_IdlePriority=5,r.unstable_ImmediatePriority=1,r.unstable_LowPriority=4,r.unstable_NormalPriority=3,r.unstable_Profiling=null,r.unstable_UserBlockingPriority=2,r.unstable_cancelCallback=function(f){f.callback=null},r.unstable_forceFrameRate=function(f){0>f||125<f?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):ie=0<f?Math.floor(1e3/f):5},r.unstable_getCurrentPriorityLevel=function(){return q},r.unstable_next=function(f){switch(q){case 1:case 2:case 3:var z=3;break;default:z=q}var E=q;q=z;try{return f()}finally{q=E}},r.unstable_requestPaint=function(){Y=!0},r.unstable_runWithPriority=function(f,z){switch(f){case 1:case 2:case 3:case 4:case 5:break;default:f=3}var E=q;q=f;try{return z()}finally{q=E}},r.unstable_scheduleCallback=function(f,z,E){var ee=r.unstable_now();switch(typeof E=="object"&&E!==null?(E=E.delay,E=typeof E=="number"&&0<E?ee+E:ee):E=ee,f){case 1:var de=-1;break;case 2:de=250;break;case 5:de=1073741823;break;case 4:de=1e4;break;default:de=5e3}return de=E+de,f={id:T++,callback:z,priorityLevel:f,startTime:E,expirationTime:de,sortIndex:-1},E>ee?(f.sortIndex=E,j(G,f),g(R)===null&&f===g(G)&&(M?(P(ne),ne=-1):M=!0,Ye(ce,E-ee))):(f.sortIndex=de,j(R,f),ae||Z||(ae=!0,k||(k=!0,U()))),f},r.unstable_shouldYield=Ke,r.unstable_wrapCallback=function(f){var z=q;return function(){var E=q;q=z;try{return f.apply(this,arguments)}finally{q=E}}}}(xu)),xu}var wf;function Bh(){return wf||(wf=1,gu.exports=Ch()),gu.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Af;function Rh(){if(Af)return zn;Af=1;var r=Bh(),j=Oh(),g=Eh();function o(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)t+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function w(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function v(e){var t=e,l=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(l=t.return),e=t.return;while(e)}return t.tag===3?l:null}function O(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function D(e){if(v(e)!==e)throw Error(o(188))}function R(e){var t=e.alternate;if(!t){if(t=v(e),t===null)throw Error(o(188));return t!==e?null:e}for(var l=e,a=t;;){var n=l.return;if(n===null)break;var s=n.alternate;if(s===null){if(a=n.return,a!==null){l=a;continue}break}if(n.child===s.child){for(s=n.child;s;){if(s===l)return D(n),e;if(s===a)return D(n),t;s=s.sibling}throw Error(o(188))}if(l.return!==a.return)l=n,a=s;else{for(var i=!1,u=n.child;u;){if(u===l){i=!0,l=n,a=s;break}if(u===a){i=!0,a=n,l=s;break}u=u.sibling}if(!i){for(u=s.child;u;){if(u===l){i=!0,l=s,a=n;break}if(u===a){i=!0,a=s,l=n;break}u=u.sibling}if(!i)throw Error(o(189))}}if(l.alternate!==a)throw Error(o(190))}if(l.tag!==3)throw Error(o(188));return l.stateNode.current===l?e:t}function G(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=G(e),t!==null)return t;e=e.sibling}return null}var T=Object.assign,C=Symbol.for("react.element"),q=Symbol.for("react.transitional.element"),Z=Symbol.for("react.portal"),ae=Symbol.for("react.fragment"),M=Symbol.for("react.strict_mode"),Y=Symbol.for("react.profiler"),$=Symbol.for("react.provider"),P=Symbol.for("react.consumer"),F=Symbol.for("react.context"),K=Symbol.for("react.forward_ref"),ce=Symbol.for("react.suspense"),k=Symbol.for("react.suspense_list"),ne=Symbol.for("react.memo"),ie=Symbol.for("react.lazy"),ke=Symbol.for("react.activity"),Ke=Symbol.for("react.memo_cache_sentinel"),B=Symbol.iterator;function U(e){return e===null||typeof e!="object"?null:(e=B&&e[B]||e["@@iterator"],typeof e=="function"?e:null)}var re=Symbol.for("react.client.reference");function Ae(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===re?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case ae:return"Fragment";case Y:return"Profiler";case M:return"StrictMode";case ce:return"Suspense";case k:return"SuspenseList";case ke:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case Z:return"Portal";case F:return(e.displayName||"Context")+".Provider";case P:return(e._context.displayName||"Context")+".Consumer";case K:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ne:return t=e.displayName||null,t!==null?t:Ae(e.type)||"Memo";case ie:t=e._payload,e=e._init;try{return Ae(e(t))}catch{}}return null}var Ye=Array.isArray,f=j.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,z=g.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,E={pending:!1,data:null,method:null,action:null},ee=[],de=-1;function ve(e){return{current:e}}function Se(e){0>de||(e.current=ee[de],ee[de]=null,de--)}function he(e,t){de++,ee[de]=e.current,e.current=t}var Be=ve(null),Nt=ve(null),Zt=ve(null),On=ve(null);function En(e,t){switch(he(Zt,t),he(Nt,e),he(Be,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?Zd(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=Zd(t),e=Kd(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}Se(Be),he(Be,e)}function Gl(){Se(Be),Se(Nt),Se(Zt)}function Ps(e){e.memoizedState!==null&&he(On,e);var t=Be.current,l=Kd(t,e.type);t!==l&&(he(Nt,e),he(Be,l))}function Un(e){Nt.current===e&&(Se(Be),Se(Nt)),On.current===e&&(Se(On),jn._currentValue=E)}var ec=Object.prototype.hasOwnProperty,tc=r.unstable_scheduleCallback,lc=r.unstable_cancelCallback,Xf=r.unstable_shouldYield,Lf=r.unstable_requestPaint,St=r.unstable_now,Vf=r.unstable_getCurrentPriorityLevel,Au=r.unstable_ImmediatePriority,zu=r.unstable_UserBlockingPriority,Cn=r.unstable_NormalPriority,Zf=r.unstable_LowPriority,Tu=r.unstable_IdlePriority,Kf=r.log,Jf=r.unstable_setDisableYieldValue,ka=null,tt=null;function Kt(e){if(typeof Kf=="function"&&Jf(e),tt&&typeof tt.setStrictMode=="function")try{tt.setStrictMode(ka,e)}catch{}}var lt=Math.clz32?Math.clz32:Ff,$f=Math.log,Wf=Math.LN2;function Ff(e){return e>>>=0,e===0?32:31-($f(e)/Wf|0)|0}var Bn=256,Rn=4194304;function vl(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function qn(e,t,l){var a=e.pendingLanes;if(a===0)return 0;var n=0,s=e.suspendedLanes,i=e.pingedLanes;e=e.warmLanes;var u=a&134217727;return u!==0?(a=u&~s,a!==0?n=vl(a):(i&=u,i!==0?n=vl(i):l||(l=u&~e,l!==0&&(n=vl(l))))):(u=a&~s,u!==0?n=vl(u):i!==0?n=vl(i):l||(l=a&~e,l!==0&&(n=vl(l)))),n===0?0:t!==0&&t!==n&&(t&s)===0&&(s=n&-n,l=t&-t,s>=l||s===32&&(l&4194048)!==0)?t:n}function Da(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function If(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ku(){var e=Bn;return Bn<<=1,(Bn&4194048)===0&&(Bn=256),e}function Du(){var e=Rn;return Rn<<=1,(Rn&62914560)===0&&(Rn=4194304),e}function ac(e){for(var t=[],l=0;31>l;l++)t.push(e);return t}function Ma(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Pf(e,t,l,a,n,s){var i=e.pendingLanes;e.pendingLanes=l,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=l,e.entangledLanes&=l,e.errorRecoveryDisabledLanes&=l,e.shellSuspendCounter=0;var u=e.entanglements,d=e.expirationTimes,b=e.hiddenUpdates;for(l=i&~l;0<l;){var N=31-lt(l),A=1<<N;u[N]=0,d[N]=-1;var y=b[N];if(y!==null)for(b[N]=null,N=0;N<y.length;N++){var p=y[N];p!==null&&(p.lane&=-536870913)}l&=~A}a!==0&&Mu(e,a,0),s!==0&&n===0&&e.tag!==0&&(e.suspendedLanes|=s&~(i&~t))}function Mu(e,t,l){e.pendingLanes|=t,e.suspendedLanes&=~t;var a=31-lt(t);e.entangledLanes|=t,e.entanglements[a]=e.entanglements[a]|1073741824|l&4194090}function Ou(e,t){var l=e.entangledLanes|=t;for(e=e.entanglements;l;){var a=31-lt(l),n=1<<a;n&t|e[a]&t&&(e[a]|=t),l&=~n}}function nc(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function sc(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Eu(){var e=z.p;return e!==0?e:(e=window.event,e===void 0?32:mf(e.type))}function e0(e,t){var l=z.p;try{return z.p=e,t()}finally{z.p=l}}var Jt=Math.random().toString(36).slice(2),Ve="__reactFiber$"+Jt,$e="__reactProps$"+Jt,Yl="__reactContainer$"+Jt,cc="__reactEvents$"+Jt,t0="__reactListeners$"+Jt,l0="__reactHandles$"+Jt,Uu="__reactResources$"+Jt,Oa="__reactMarker$"+Jt;function ic(e){delete e[Ve],delete e[$e],delete e[cc],delete e[t0],delete e[l0]}function Ql(e){var t=e[Ve];if(t)return t;for(var l=e.parentNode;l;){if(t=l[Yl]||l[Ve]){if(l=t.alternate,t.child!==null||l!==null&&l.child!==null)for(e=Fd(e);e!==null;){if(l=e[Ve])return l;e=Fd(e)}return t}e=l,l=e.parentNode}return null}function Xl(e){if(e=e[Ve]||e[Yl]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function Ea(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(o(33))}function Ll(e){var t=e[Uu];return t||(t=e[Uu]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Re(e){e[Oa]=!0}var Cu=new Set,Bu={};function jl(e,t){Vl(e,t),Vl(e+"Capture",t)}function Vl(e,t){for(Bu[e]=t,e=0;e<t.length;e++)Cu.add(t[e])}var a0=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ru={},qu={};function n0(e){return ec.call(qu,e)?!0:ec.call(Ru,e)?!1:a0.test(e)?qu[e]=!0:(Ru[e]=!0,!1)}function Hn(e,t,l){if(n0(t))if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var a=t.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+l)}}function _n(e,t,l){if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+l)}}function Mt(e,t,l,a){if(a===null)e.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(l);return}e.setAttributeNS(t,l,""+a)}}var uc,Hu;function Zl(e){if(uc===void 0)try{throw Error()}catch(l){var t=l.stack.trim().match(/\n( *(at )?)/);uc=t&&t[1]||"",Hu=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+uc+e+Hu}var rc=!1;function oc(e,t){if(!e||rc)return"";rc=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(t){var A=function(){throw Error()};if(Object.defineProperty(A.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(A,[])}catch(p){var y=p}Reflect.construct(e,[],A)}else{try{A.call()}catch(p){y=p}e.call(A.prototype)}}else{try{throw Error()}catch(p){y=p}(A=e())&&typeof A.catch=="function"&&A.catch(function(){})}}catch(p){if(p&&y&&typeof p.stack=="string")return[p.stack,y.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var s=a.DetermineComponentFrameRoot(),i=s[0],u=s[1];if(i&&u){var d=i.split(`
`),b=u.split(`
`);for(n=a=0;a<d.length&&!d[a].includes("DetermineComponentFrameRoot");)a++;for(;n<b.length&&!b[n].includes("DetermineComponentFrameRoot");)n++;if(a===d.length||n===b.length)for(a=d.length-1,n=b.length-1;1<=a&&0<=n&&d[a]!==b[n];)n--;for(;1<=a&&0<=n;a--,n--)if(d[a]!==b[n]){if(a!==1||n!==1)do if(a--,n--,0>n||d[a]!==b[n]){var N=`
`+d[a].replace(" at new "," at ");return e.displayName&&N.includes("<anonymous>")&&(N=N.replace("<anonymous>",e.displayName)),N}while(1<=a&&0<=n);break}}}finally{rc=!1,Error.prepareStackTrace=l}return(l=e?e.displayName||e.name:"")?Zl(l):""}function s0(e){switch(e.tag){case 26:case 27:case 5:return Zl(e.type);case 16:return Zl("Lazy");case 13:return Zl("Suspense");case 19:return Zl("SuspenseList");case 0:case 15:return oc(e.type,!1);case 11:return oc(e.type.render,!1);case 1:return oc(e.type,!0);case 31:return Zl("Activity");default:return""}}function _u(e){try{var t="";do t+=s0(e),e=e.return;while(e);return t}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function ot(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Gu(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function c0(e){var t=Gu(e)?"checked":"value",l=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),a=""+e[t];if(!e.hasOwnProperty(t)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var n=l.get,s=l.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return n.call(this)},set:function(i){a=""+i,s.call(this,i)}}),Object.defineProperty(e,t,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(i){a=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Gn(e){e._valueTracker||(e._valueTracker=c0(e))}function Yu(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var l=t.getValue(),a="";return e&&(a=Gu(e)?e.checked?"true":"false":e.value),e=a,e!==l?(t.setValue(e),!0):!1}function Yn(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var i0=/[\n"\\]/g;function dt(e){return e.replace(i0,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function dc(e,t,l,a,n,s,i,u){e.name="",i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"?e.type=i:e.removeAttribute("type"),t!=null?i==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+ot(t)):e.value!==""+ot(t)&&(e.value=""+ot(t)):i!=="submit"&&i!=="reset"||e.removeAttribute("value"),t!=null?fc(e,i,ot(t)):l!=null?fc(e,i,ot(l)):a!=null&&e.removeAttribute("value"),n==null&&s!=null&&(e.defaultChecked=!!s),n!=null&&(e.checked=n&&typeof n!="function"&&typeof n!="symbol"),u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"?e.name=""+ot(u):e.removeAttribute("name")}function Qu(e,t,l,a,n,s,i,u){if(s!=null&&typeof s!="function"&&typeof s!="symbol"&&typeof s!="boolean"&&(e.type=s),t!=null||l!=null){if(!(s!=="submit"&&s!=="reset"||t!=null))return;l=l!=null?""+ot(l):"",t=t!=null?""+ot(t):l,u||t===e.value||(e.value=t),e.defaultValue=t}a=a??n,a=typeof a!="function"&&typeof a!="symbol"&&!!a,e.checked=u?e.checked:!!a,e.defaultChecked=!!a,i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(e.name=i)}function fc(e,t,l){t==="number"&&Yn(e.ownerDocument)===e||e.defaultValue===""+l||(e.defaultValue=""+l)}function Kl(e,t,l,a){if(e=e.options,t){t={};for(var n=0;n<l.length;n++)t["$"+l[n]]=!0;for(l=0;l<e.length;l++)n=t.hasOwnProperty("$"+e[l].value),e[l].selected!==n&&(e[l].selected=n),n&&a&&(e[l].defaultSelected=!0)}else{for(l=""+ot(l),t=null,n=0;n<e.length;n++){if(e[n].value===l){e[n].selected=!0,a&&(e[n].defaultSelected=!0);return}t!==null||e[n].disabled||(t=e[n])}t!==null&&(t.selected=!0)}}function Xu(e,t,l){if(t!=null&&(t=""+ot(t),t!==e.value&&(e.value=t),l==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=l!=null?""+ot(l):""}function Lu(e,t,l,a){if(t==null){if(a!=null){if(l!=null)throw Error(o(92));if(Ye(a)){if(1<a.length)throw Error(o(93));a=a[0]}l=a}l==null&&(l=""),t=l}l=ot(t),e.defaultValue=l,a=e.textContent,a===l&&a!==""&&a!==null&&(e.value=a)}function Jl(e,t){if(t){var l=e.firstChild;if(l&&l===e.lastChild&&l.nodeType===3){l.nodeValue=t;return}}e.textContent=t}var u0=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Vu(e,t,l){var a=t.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":a?e.setProperty(t,l):typeof l!="number"||l===0||u0.has(t)?t==="float"?e.cssFloat=l:e[t]=(""+l).trim():e[t]=l+"px"}function Zu(e,t,l){if(t!=null&&typeof t!="object")throw Error(o(62));if(e=e.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||t!=null&&t.hasOwnProperty(a)||(a.indexOf("--")===0?e.setProperty(a,""):a==="float"?e.cssFloat="":e[a]="");for(var n in t)a=t[n],t.hasOwnProperty(n)&&l[n]!==a&&Vu(e,n,a)}else for(var s in t)t.hasOwnProperty(s)&&Vu(e,s,t[s])}function mc(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var r0=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),o0=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Qn(e){return o0.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var hc=null;function gc(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var $l=null,Wl=null;function Ku(e){var t=Xl(e);if(t&&(e=t.stateNode)){var l=e[$e]||null;e:switch(e=t.stateNode,t.type){case"input":if(dc(e,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),t=l.name,l.type==="radio"&&t!=null){for(l=e;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+dt(""+t)+'"][type="radio"]'),t=0;t<l.length;t++){var a=l[t];if(a!==e&&a.form===e.form){var n=a[$e]||null;if(!n)throw Error(o(90));dc(a,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(t=0;t<l.length;t++)a=l[t],a.form===e.form&&Yu(a)}break e;case"textarea":Xu(e,l.value,l.defaultValue);break e;case"select":t=l.value,t!=null&&Kl(e,!!l.multiple,t,!1)}}}var xc=!1;function Ju(e,t,l){if(xc)return e(t,l);xc=!0;try{var a=e(t);return a}finally{if(xc=!1,($l!==null||Wl!==null)&&(Ts(),$l&&(t=$l,e=Wl,Wl=$l=null,Ku(t),e)))for(t=0;t<e.length;t++)Ku(e[t])}}function Ua(e,t){var l=e.stateNode;if(l===null)return null;var a=l[$e]||null;if(a===null)return null;l=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(e=e.type,a=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!a;break e;default:e=!1}if(e)return null;if(l&&typeof l!="function")throw Error(o(231,t,typeof l));return l}var Ot=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),bc=!1;if(Ot)try{var Ca={};Object.defineProperty(Ca,"passive",{get:function(){bc=!0}}),window.addEventListener("test",Ca,Ca),window.removeEventListener("test",Ca,Ca)}catch{bc=!1}var $t=null,yc=null,Xn=null;function $u(){if(Xn)return Xn;var e,t=yc,l=t.length,a,n="value"in $t?$t.value:$t.textContent,s=n.length;for(e=0;e<l&&t[e]===n[e];e++);var i=l-e;for(a=1;a<=i&&t[l-a]===n[s-a];a++);return Xn=n.slice(e,1<a?1-a:void 0)}function Ln(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Vn(){return!0}function Wu(){return!1}function We(e){function t(l,a,n,s,i){this._reactName=l,this._targetInst=n,this.type=a,this.nativeEvent=s,this.target=i,this.currentTarget=null;for(var u in e)e.hasOwnProperty(u)&&(l=e[u],this[u]=l?l(s):s[u]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?Vn:Wu,this.isPropagationStopped=Wu,this}return T(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=Vn)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=Vn)},persist:function(){},isPersistent:Vn}),t}var Nl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Zn=We(Nl),Ba=T({},Nl,{view:0,detail:0}),d0=We(Ba),pc,vc,Ra,Kn=T({},Ba,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Nc,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Ra&&(Ra&&e.type==="mousemove"?(pc=e.screenX-Ra.screenX,vc=e.screenY-Ra.screenY):vc=pc=0,Ra=e),pc)},movementY:function(e){return"movementY"in e?e.movementY:vc}}),Fu=We(Kn),f0=T({},Kn,{dataTransfer:0}),m0=We(f0),h0=T({},Ba,{relatedTarget:0}),jc=We(h0),g0=T({},Nl,{animationName:0,elapsedTime:0,pseudoElement:0}),x0=We(g0),b0=T({},Nl,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),y0=We(b0),p0=T({},Nl,{data:0}),Iu=We(p0),v0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},j0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},N0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function S0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=N0[e])?!!t[e]:!1}function Nc(){return S0}var w0=T({},Ba,{key:function(e){if(e.key){var t=v0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ln(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?j0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Nc,charCode:function(e){return e.type==="keypress"?Ln(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ln(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),A0=We(w0),z0=T({},Kn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Pu=We(z0),T0=T({},Ba,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Nc}),k0=We(T0),D0=T({},Nl,{propertyName:0,elapsedTime:0,pseudoElement:0}),M0=We(D0),O0=T({},Kn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),E0=We(O0),U0=T({},Nl,{newState:0,oldState:0}),C0=We(U0),B0=[9,13,27,32],Sc=Ot&&"CompositionEvent"in window,qa=null;Ot&&"documentMode"in document&&(qa=document.documentMode);var R0=Ot&&"TextEvent"in window&&!qa,er=Ot&&(!Sc||qa&&8<qa&&11>=qa),tr=" ",lr=!1;function ar(e,t){switch(e){case"keyup":return B0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function nr(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Fl=!1;function q0(e,t){switch(e){case"compositionend":return nr(t);case"keypress":return t.which!==32?null:(lr=!0,tr);case"textInput":return e=t.data,e===tr&&lr?null:e;default:return null}}function H0(e,t){if(Fl)return e==="compositionend"||!Sc&&ar(e,t)?(e=$u(),Xn=yc=$t=null,Fl=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return er&&t.locale!=="ko"?null:t.data;default:return null}}var _0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function sr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!_0[e.type]:t==="textarea"}function cr(e,t,l,a){$l?Wl?Wl.push(a):Wl=[a]:$l=a,t=Us(t,"onChange"),0<t.length&&(l=new Zn("onChange","change",null,l,a),e.push({event:l,listeners:t}))}var Ha=null,_a=null;function G0(e){Yd(e,0)}function Jn(e){var t=Ea(e);if(Yu(t))return e}function ir(e,t){if(e==="change")return t}var ur=!1;if(Ot){var wc;if(Ot){var Ac="oninput"in document;if(!Ac){var rr=document.createElement("div");rr.setAttribute("oninput","return;"),Ac=typeof rr.oninput=="function"}wc=Ac}else wc=!1;ur=wc&&(!document.documentMode||9<document.documentMode)}function or(){Ha&&(Ha.detachEvent("onpropertychange",dr),_a=Ha=null)}function dr(e){if(e.propertyName==="value"&&Jn(_a)){var t=[];cr(t,_a,e,gc(e)),Ju(G0,t)}}function Y0(e,t,l){e==="focusin"?(or(),Ha=t,_a=l,Ha.attachEvent("onpropertychange",dr)):e==="focusout"&&or()}function Q0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Jn(_a)}function X0(e,t){if(e==="click")return Jn(t)}function L0(e,t){if(e==="input"||e==="change")return Jn(t)}function V0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var at=typeof Object.is=="function"?Object.is:V0;function Ga(e,t){if(at(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var l=Object.keys(e),a=Object.keys(t);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var n=l[a];if(!ec.call(t,n)||!at(e[n],t[n]))return!1}return!0}function fr(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function mr(e,t){var l=fr(e);e=0;for(var a;l;){if(l.nodeType===3){if(a=e+l.textContent.length,e<=t&&a>=t)return{node:l,offset:t-e};e=a}e:{for(;l;){if(l.nextSibling){l=l.nextSibling;break e}l=l.parentNode}l=void 0}l=fr(l)}}function hr(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?hr(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function gr(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Yn(e.document);t instanceof e.HTMLIFrameElement;){try{var l=typeof t.contentWindow.location.href=="string"}catch{l=!1}if(l)e=t.contentWindow;else break;t=Yn(e.document)}return t}function zc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var Z0=Ot&&"documentMode"in document&&11>=document.documentMode,Il=null,Tc=null,Ya=null,kc=!1;function xr(e,t,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;kc||Il==null||Il!==Yn(a)||(a=Il,"selectionStart"in a&&zc(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),Ya&&Ga(Ya,a)||(Ya=a,a=Us(Tc,"onSelect"),0<a.length&&(t=new Zn("onSelect","select",null,t,l),e.push({event:t,listeners:a}),t.target=Il)))}function Sl(e,t){var l={};return l[e.toLowerCase()]=t.toLowerCase(),l["Webkit"+e]="webkit"+t,l["Moz"+e]="moz"+t,l}var Pl={animationend:Sl("Animation","AnimationEnd"),animationiteration:Sl("Animation","AnimationIteration"),animationstart:Sl("Animation","AnimationStart"),transitionrun:Sl("Transition","TransitionRun"),transitionstart:Sl("Transition","TransitionStart"),transitioncancel:Sl("Transition","TransitionCancel"),transitionend:Sl("Transition","TransitionEnd")},Dc={},br={};Ot&&(br=document.createElement("div").style,"AnimationEvent"in window||(delete Pl.animationend.animation,delete Pl.animationiteration.animation,delete Pl.animationstart.animation),"TransitionEvent"in window||delete Pl.transitionend.transition);function wl(e){if(Dc[e])return Dc[e];if(!Pl[e])return e;var t=Pl[e],l;for(l in t)if(t.hasOwnProperty(l)&&l in br)return Dc[e]=t[l];return e}var yr=wl("animationend"),pr=wl("animationiteration"),vr=wl("animationstart"),K0=wl("transitionrun"),J0=wl("transitionstart"),$0=wl("transitioncancel"),jr=wl("transitionend"),Nr=new Map,Mc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Mc.push("scrollEnd");function pt(e,t){Nr.set(e,t),jl(t,[e])}var Sr=new WeakMap;function ft(e,t){if(typeof e=="object"&&e!==null){var l=Sr.get(e);return l!==void 0?l:(t={value:e,source:t,stack:_u(t)},Sr.set(e,t),t)}return{value:e,source:t,stack:_u(t)}}var mt=[],ea=0,Oc=0;function $n(){for(var e=ea,t=Oc=ea=0;t<e;){var l=mt[t];mt[t++]=null;var a=mt[t];mt[t++]=null;var n=mt[t];mt[t++]=null;var s=mt[t];if(mt[t++]=null,a!==null&&n!==null){var i=a.pending;i===null?n.next=n:(n.next=i.next,i.next=n),a.pending=n}s!==0&&wr(l,n,s)}}function Wn(e,t,l,a){mt[ea++]=e,mt[ea++]=t,mt[ea++]=l,mt[ea++]=a,Oc|=a,e.lanes|=a,e=e.alternate,e!==null&&(e.lanes|=a)}function Ec(e,t,l,a){return Wn(e,t,l,a),Fn(e)}function ta(e,t){return Wn(e,null,null,t),Fn(e)}function wr(e,t,l){e.lanes|=l;var a=e.alternate;a!==null&&(a.lanes|=l);for(var n=!1,s=e.return;s!==null;)s.childLanes|=l,a=s.alternate,a!==null&&(a.childLanes|=l),s.tag===22&&(e=s.stateNode,e===null||e._visibility&1||(n=!0)),e=s,s=s.return;return e.tag===3?(s=e.stateNode,n&&t!==null&&(n=31-lt(l),e=s.hiddenUpdates,a=e[n],a===null?e[n]=[t]:a.push(t),t.lane=l|536870912),s):null}function Fn(e){if(50<mn)throw mn=0,Hi=null,Error(o(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var la={};function W0(e,t,l,a){this.tag=e,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function nt(e,t,l,a){return new W0(e,t,l,a)}function Uc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Et(e,t){var l=e.alternate;return l===null?(l=nt(e.tag,t,e.key,e.mode),l.elementType=e.elementType,l.type=e.type,l.stateNode=e.stateNode,l.alternate=e,e.alternate=l):(l.pendingProps=t,l.type=e.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=e.flags&65011712,l.childLanes=e.childLanes,l.lanes=e.lanes,l.child=e.child,l.memoizedProps=e.memoizedProps,l.memoizedState=e.memoizedState,l.updateQueue=e.updateQueue,t=e.dependencies,l.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},l.sibling=e.sibling,l.index=e.index,l.ref=e.ref,l.refCleanup=e.refCleanup,l}function Ar(e,t){e.flags&=65011714;var l=e.alternate;return l===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=l.childLanes,e.lanes=l.lanes,e.child=l.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=l.memoizedProps,e.memoizedState=l.memoizedState,e.updateQueue=l.updateQueue,e.type=l.type,t=l.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function In(e,t,l,a,n,s){var i=0;if(a=e,typeof e=="function")Uc(e)&&(i=1);else if(typeof e=="string")i=Im(e,l,Be.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case ke:return e=nt(31,l,t,n),e.elementType=ke,e.lanes=s,e;case ae:return Al(l.children,n,s,t);case M:i=8,n|=24;break;case Y:return e=nt(12,l,t,n|2),e.elementType=Y,e.lanes=s,e;case ce:return e=nt(13,l,t,n),e.elementType=ce,e.lanes=s,e;case k:return e=nt(19,l,t,n),e.elementType=k,e.lanes=s,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case $:case F:i=10;break e;case P:i=9;break e;case K:i=11;break e;case ne:i=14;break e;case ie:i=16,a=null;break e}i=29,l=Error(o(130,e===null?"null":typeof e,"")),a=null}return t=nt(i,l,t,n),t.elementType=e,t.type=a,t.lanes=s,t}function Al(e,t,l,a){return e=nt(7,e,a,t),e.lanes=l,e}function Cc(e,t,l){return e=nt(6,e,null,t),e.lanes=l,e}function Bc(e,t,l){return t=nt(4,e.children!==null?e.children:[],e.key,t),t.lanes=l,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var aa=[],na=0,Pn=null,es=0,ht=[],gt=0,zl=null,Ut=1,Ct="";function Tl(e,t){aa[na++]=es,aa[na++]=Pn,Pn=e,es=t}function zr(e,t,l){ht[gt++]=Ut,ht[gt++]=Ct,ht[gt++]=zl,zl=e;var a=Ut;e=Ct;var n=32-lt(a)-1;a&=~(1<<n),l+=1;var s=32-lt(t)+n;if(30<s){var i=n-n%5;s=(a&(1<<i)-1).toString(32),a>>=i,n-=i,Ut=1<<32-lt(t)+n|l<<n|a,Ct=s+e}else Ut=1<<s|l<<n|a,Ct=e}function Rc(e){e.return!==null&&(Tl(e,1),zr(e,1,0))}function qc(e){for(;e===Pn;)Pn=aa[--na],aa[na]=null,es=aa[--na],aa[na]=null;for(;e===zl;)zl=ht[--gt],ht[gt]=null,Ct=ht[--gt],ht[gt]=null,Ut=ht[--gt],ht[gt]=null}var Je=null,ze=null,oe=!1,kl=null,wt=!1,Hc=Error(o(519));function Dl(e){var t=Error(o(418,""));throw La(ft(t,e)),Hc}function Tr(e){var t=e.stateNode,l=e.type,a=e.memoizedProps;switch(t[Ve]=e,t[$e]=a,l){case"dialog":le("cancel",t),le("close",t);break;case"iframe":case"object":case"embed":le("load",t);break;case"video":case"audio":for(l=0;l<gn.length;l++)le(gn[l],t);break;case"source":le("error",t);break;case"img":case"image":case"link":le("error",t),le("load",t);break;case"details":le("toggle",t);break;case"input":le("invalid",t),Qu(t,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),Gn(t);break;case"select":le("invalid",t);break;case"textarea":le("invalid",t),Lu(t,a.value,a.defaultValue,a.children),Gn(t)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||t.textContent===""+l||a.suppressHydrationWarning===!0||Vd(t.textContent,l)?(a.popover!=null&&(le("beforetoggle",t),le("toggle",t)),a.onScroll!=null&&le("scroll",t),a.onScrollEnd!=null&&le("scrollend",t),a.onClick!=null&&(t.onclick=Cs),t=!0):t=!1,t||Dl(e)}function kr(e){for(Je=e.return;Je;)switch(Je.tag){case 5:case 13:wt=!1;return;case 27:case 3:wt=!0;return;default:Je=Je.return}}function Qa(e){if(e!==Je)return!1;if(!oe)return kr(e),oe=!0,!1;var t=e.tag,l;if((l=t!==3&&t!==27)&&((l=t===5)&&(l=e.type,l=!(l!=="form"&&l!=="button")||eu(e.type,e.memoizedProps)),l=!l),l&&ze&&Dl(e),kr(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(l=e.data,l==="/$"){if(t===0){ze=jt(e.nextSibling);break e}t--}else l!=="$"&&l!=="$!"&&l!=="$?"||t++;e=e.nextSibling}ze=null}}else t===27?(t=ze,dl(e.type)?(e=nu,nu=null,ze=e):ze=t):ze=Je?jt(e.stateNode.nextSibling):null;return!0}function Xa(){ze=Je=null,oe=!1}function Dr(){var e=kl;return e!==null&&(Pe===null?Pe=e:Pe.push.apply(Pe,e),kl=null),e}function La(e){kl===null?kl=[e]:kl.push(e)}var _c=ve(null),Ml=null,Bt=null;function Wt(e,t,l){he(_c,t._currentValue),t._currentValue=l}function Rt(e){e._currentValue=_c.current,Se(_c)}function Gc(e,t,l){for(;e!==null;){var a=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,a!==null&&(a.childLanes|=t)):a!==null&&(a.childLanes&t)!==t&&(a.childLanes|=t),e===l)break;e=e.return}}function Yc(e,t,l,a){var n=e.child;for(n!==null&&(n.return=e);n!==null;){var s=n.dependencies;if(s!==null){var i=n.child;s=s.firstContext;e:for(;s!==null;){var u=s;s=n;for(var d=0;d<t.length;d++)if(u.context===t[d]){s.lanes|=l,u=s.alternate,u!==null&&(u.lanes|=l),Gc(s.return,l,e),a||(i=null);break e}s=u.next}}else if(n.tag===18){if(i=n.return,i===null)throw Error(o(341));i.lanes|=l,s=i.alternate,s!==null&&(s.lanes|=l),Gc(i,l,e),i=null}else i=n.child;if(i!==null)i.return=n;else for(i=n;i!==null;){if(i===e){i=null;break}if(n=i.sibling,n!==null){n.return=i.return,i=n;break}i=i.return}n=i}}function Va(e,t,l,a){e=null;for(var n=t,s=!1;n!==null;){if(!s){if((n.flags&524288)!==0)s=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var i=n.alternate;if(i===null)throw Error(o(387));if(i=i.memoizedProps,i!==null){var u=n.type;at(n.pendingProps.value,i.value)||(e!==null?e.push(u):e=[u])}}else if(n===On.current){if(i=n.alternate,i===null)throw Error(o(387));i.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(e!==null?e.push(jn):e=[jn])}n=n.return}e!==null&&Yc(t,e,l,a),t.flags|=262144}function ts(e){for(e=e.firstContext;e!==null;){if(!at(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ol(e){Ml=e,Bt=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function Ze(e){return Mr(Ml,e)}function ls(e,t){return Ml===null&&Ol(e),Mr(e,t)}function Mr(e,t){var l=t._currentValue;if(t={context:t,memoizedValue:l,next:null},Bt===null){if(e===null)throw Error(o(308));Bt=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Bt=Bt.next=t;return l}var F0=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(l,a){e.push(a)}};this.abort=function(){t.aborted=!0,e.forEach(function(l){return l()})}},I0=r.unstable_scheduleCallback,P0=r.unstable_NormalPriority,Ue={$$typeof:F,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Qc(){return{controller:new F0,data:new Map,refCount:0}}function Za(e){e.refCount--,e.refCount===0&&I0(P0,function(){e.controller.abort()})}var Ka=null,Xc=0,sa=0,ca=null;function em(e,t){if(Ka===null){var l=Ka=[];Xc=0,sa=Vi(),ca={status:"pending",value:void 0,then:function(a){l.push(a)}}}return Xc++,t.then(Or,Or),t}function Or(){if(--Xc===0&&Ka!==null){ca!==null&&(ca.status="fulfilled");var e=Ka;Ka=null,sa=0,ca=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function tm(e,t){var l=[],a={status:"pending",value:null,reason:null,then:function(n){l.push(n)}};return e.then(function(){a.status="fulfilled",a.value=t;for(var n=0;n<l.length;n++)(0,l[n])(t)},function(n){for(a.status="rejected",a.reason=n,n=0;n<l.length;n++)(0,l[n])(void 0)}),a}var Er=f.S;f.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&em(e,t),Er!==null&&Er(e,t)};var El=ve(null);function Lc(){var e=El.current;return e!==null?e:pe.pooledCache}function as(e,t){t===null?he(El,El.current):he(El,t.pool)}function Ur(){var e=Lc();return e===null?null:{parent:Ue._currentValue,pool:e}}var Ja=Error(o(460)),Cr=Error(o(474)),ns=Error(o(542)),Vc={then:function(){}};function Br(e){return e=e.status,e==="fulfilled"||e==="rejected"}function ss(){}function Rr(e,t,l){switch(l=e[l],l===void 0?e.push(t):l!==t&&(t.then(ss,ss),t=l),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Hr(e),e;default:if(typeof t.status=="string")t.then(ss,ss);else{if(e=pe,e!==null&&100<e.shellSuspendCounter)throw Error(o(482));e=t,e.status="pending",e.then(function(a){if(t.status==="pending"){var n=t;n.status="fulfilled",n.value=a}},function(a){if(t.status==="pending"){var n=t;n.status="rejected",n.reason=a}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Hr(e),e}throw $a=t,Ja}}var $a=null;function qr(){if($a===null)throw Error(o(459));var e=$a;return $a=null,e}function Hr(e){if(e===Ja||e===ns)throw Error(o(483))}var Ft=!1;function Zc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Kc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function It(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Pt(e,t,l){var a=e.updateQueue;if(a===null)return null;if(a=a.shared,(fe&2)!==0){var n=a.pending;return n===null?t.next=t:(t.next=n.next,n.next=t),a.pending=t,t=Fn(e),wr(e,null,l),t}return Wn(e,a,t,l),Fn(e)}function Wa(e,t,l){if(t=t.updateQueue,t!==null&&(t=t.shared,(l&4194048)!==0)){var a=t.lanes;a&=e.pendingLanes,l|=a,t.lanes=l,Ou(e,l)}}function Jc(e,t){var l=e.updateQueue,a=e.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var n=null,s=null;if(l=l.firstBaseUpdate,l!==null){do{var i={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};s===null?n=s=i:s=s.next=i,l=l.next}while(l!==null);s===null?n=s=t:s=s.next=t}else n=s=t;l={baseState:a.baseState,firstBaseUpdate:n,lastBaseUpdate:s,shared:a.shared,callbacks:a.callbacks},e.updateQueue=l;return}e=l.lastBaseUpdate,e===null?l.firstBaseUpdate=t:e.next=t,l.lastBaseUpdate=t}var $c=!1;function Fa(){if($c){var e=ca;if(e!==null)throw e}}function Ia(e,t,l,a){$c=!1;var n=e.updateQueue;Ft=!1;var s=n.firstBaseUpdate,i=n.lastBaseUpdate,u=n.shared.pending;if(u!==null){n.shared.pending=null;var d=u,b=d.next;d.next=null,i===null?s=b:i.next=b,i=d;var N=e.alternate;N!==null&&(N=N.updateQueue,u=N.lastBaseUpdate,u!==i&&(u===null?N.firstBaseUpdate=b:u.next=b,N.lastBaseUpdate=d))}if(s!==null){var A=n.baseState;i=0,N=b=d=null,u=s;do{var y=u.lane&-536870913,p=y!==u.lane;if(p?(se&y)===y:(a&y)===y){y!==0&&y===sa&&($c=!0),N!==null&&(N=N.next={lane:0,tag:u.tag,payload:u.payload,callback:null,next:null});e:{var L=e,Q=u;y=t;var be=l;switch(Q.tag){case 1:if(L=Q.payload,typeof L=="function"){A=L.call(be,A,y);break e}A=L;break e;case 3:L.flags=L.flags&-65537|128;case 0:if(L=Q.payload,y=typeof L=="function"?L.call(be,A,y):L,y==null)break e;A=T({},A,y);break e;case 2:Ft=!0}}y=u.callback,y!==null&&(e.flags|=64,p&&(e.flags|=8192),p=n.callbacks,p===null?n.callbacks=[y]:p.push(y))}else p={lane:y,tag:u.tag,payload:u.payload,callback:u.callback,next:null},N===null?(b=N=p,d=A):N=N.next=p,i|=y;if(u=u.next,u===null){if(u=n.shared.pending,u===null)break;p=u,u=p.next,p.next=null,n.lastBaseUpdate=p,n.shared.pending=null}}while(!0);N===null&&(d=A),n.baseState=d,n.firstBaseUpdate=b,n.lastBaseUpdate=N,s===null&&(n.shared.lanes=0),il|=i,e.lanes=i,e.memoizedState=A}}function _r(e,t){if(typeof e!="function")throw Error(o(191,e));e.call(t)}function Gr(e,t){var l=e.callbacks;if(l!==null)for(e.callbacks=null,e=0;e<l.length;e++)_r(l[e],t)}var ia=ve(null),cs=ve(0);function Yr(e,t){e=Xt,he(cs,e),he(ia,t),Xt=e|t.baseLanes}function Wc(){he(cs,Xt),he(ia,ia.current)}function Fc(){Xt=cs.current,Se(ia),Se(cs)}var el=0,W=null,ge=null,Oe=null,is=!1,ua=!1,Ul=!1,us=0,Pa=0,ra=null,lm=0;function De(){throw Error(o(321))}function Ic(e,t){if(t===null)return!1;for(var l=0;l<t.length&&l<e.length;l++)if(!at(e[l],t[l]))return!1;return!0}function Pc(e,t,l,a,n,s){return el=s,W=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,f.H=e===null||e.memoizedState===null?Ao:zo,Ul=!1,s=l(a,n),Ul=!1,ua&&(s=Xr(t,l,a,n)),Qr(e),s}function Qr(e){f.H=hs;var t=ge!==null&&ge.next!==null;if(el=0,Oe=ge=W=null,is=!1,Pa=0,ra=null,t)throw Error(o(300));e===null||qe||(e=e.dependencies,e!==null&&ts(e)&&(qe=!0))}function Xr(e,t,l,a){W=e;var n=0;do{if(ua&&(ra=null),Pa=0,ua=!1,25<=n)throw Error(o(301));if(n+=1,Oe=ge=null,e.updateQueue!=null){var s=e.updateQueue;s.lastEffect=null,s.events=null,s.stores=null,s.memoCache!=null&&(s.memoCache.index=0)}f.H=rm,s=t(l,a)}while(ua);return s}function am(){var e=f.H,t=e.useState()[0];return t=typeof t.then=="function"?en(t):t,e=e.useState()[0],(ge!==null?ge.memoizedState:null)!==e&&(W.flags|=1024),t}function ei(){var e=us!==0;return us=0,e}function ti(e,t,l){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l}function li(e){if(is){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}is=!1}el=0,Oe=ge=W=null,ua=!1,Pa=us=0,ra=null}function Fe(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Oe===null?W.memoizedState=Oe=e:Oe=Oe.next=e,Oe}function Ee(){if(ge===null){var e=W.alternate;e=e!==null?e.memoizedState:null}else e=ge.next;var t=Oe===null?W.memoizedState:Oe.next;if(t!==null)Oe=t,ge=e;else{if(e===null)throw W.alternate===null?Error(o(467)):Error(o(310));ge=e,e={memoizedState:ge.memoizedState,baseState:ge.baseState,baseQueue:ge.baseQueue,queue:ge.queue,next:null},Oe===null?W.memoizedState=Oe=e:Oe=Oe.next=e}return Oe}function ai(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function en(e){var t=Pa;return Pa+=1,ra===null&&(ra=[]),e=Rr(ra,e,t),t=W,(Oe===null?t.memoizedState:Oe.next)===null&&(t=t.alternate,f.H=t===null||t.memoizedState===null?Ao:zo),e}function rs(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return en(e);if(e.$$typeof===F)return Ze(e)}throw Error(o(438,String(e)))}function ni(e){var t=null,l=W.updateQueue;if(l!==null&&(t=l.memoCache),t==null){var a=W.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(t={data:a.data.map(function(n){return n.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),l===null&&(l=ai(),W.updateQueue=l),l.memoCache=t,l=t.data[t.index],l===void 0)for(l=t.data[t.index]=Array(e),a=0;a<e;a++)l[a]=Ke;return t.index++,l}function qt(e,t){return typeof t=="function"?t(e):t}function os(e){var t=Ee();return si(t,ge,e)}function si(e,t,l){var a=e.queue;if(a===null)throw Error(o(311));a.lastRenderedReducer=l;var n=e.baseQueue,s=a.pending;if(s!==null){if(n!==null){var i=n.next;n.next=s.next,s.next=i}t.baseQueue=n=s,a.pending=null}if(s=e.baseState,n===null)e.memoizedState=s;else{t=n.next;var u=i=null,d=null,b=t,N=!1;do{var A=b.lane&-536870913;if(A!==b.lane?(se&A)===A:(el&A)===A){var y=b.revertLane;if(y===0)d!==null&&(d=d.next={lane:0,revertLane:0,action:b.action,hasEagerState:b.hasEagerState,eagerState:b.eagerState,next:null}),A===sa&&(N=!0);else if((el&y)===y){b=b.next,y===sa&&(N=!0);continue}else A={lane:0,revertLane:b.revertLane,action:b.action,hasEagerState:b.hasEagerState,eagerState:b.eagerState,next:null},d===null?(u=d=A,i=s):d=d.next=A,W.lanes|=y,il|=y;A=b.action,Ul&&l(s,A),s=b.hasEagerState?b.eagerState:l(s,A)}else y={lane:A,revertLane:b.revertLane,action:b.action,hasEagerState:b.hasEagerState,eagerState:b.eagerState,next:null},d===null?(u=d=y,i=s):d=d.next=y,W.lanes|=A,il|=A;b=b.next}while(b!==null&&b!==t);if(d===null?i=s:d.next=u,!at(s,e.memoizedState)&&(qe=!0,N&&(l=ca,l!==null)))throw l;e.memoizedState=s,e.baseState=i,e.baseQueue=d,a.lastRenderedState=s}return n===null&&(a.lanes=0),[e.memoizedState,a.dispatch]}function ci(e){var t=Ee(),l=t.queue;if(l===null)throw Error(o(311));l.lastRenderedReducer=e;var a=l.dispatch,n=l.pending,s=t.memoizedState;if(n!==null){l.pending=null;var i=n=n.next;do s=e(s,i.action),i=i.next;while(i!==n);at(s,t.memoizedState)||(qe=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),l.lastRenderedState=s}return[s,a]}function Lr(e,t,l){var a=W,n=Ee(),s=oe;if(s){if(l===void 0)throw Error(o(407));l=l()}else l=t();var i=!at((ge||n).memoizedState,l);i&&(n.memoizedState=l,qe=!0),n=n.queue;var u=Kr.bind(null,a,n,e);if(tn(2048,8,u,[e]),n.getSnapshot!==t||i||Oe!==null&&Oe.memoizedState.tag&1){if(a.flags|=2048,oa(9,ds(),Zr.bind(null,a,n,l,t),null),pe===null)throw Error(o(349));s||(el&124)!==0||Vr(a,t,l)}return l}function Vr(e,t,l){e.flags|=16384,e={getSnapshot:t,value:l},t=W.updateQueue,t===null?(t=ai(),W.updateQueue=t,t.stores=[e]):(l=t.stores,l===null?t.stores=[e]:l.push(e))}function Zr(e,t,l,a){t.value=l,t.getSnapshot=a,Jr(t)&&$r(e)}function Kr(e,t,l){return l(function(){Jr(t)&&$r(e)})}function Jr(e){var t=e.getSnapshot;e=e.value;try{var l=t();return!at(e,l)}catch{return!0}}function $r(e){var t=ta(e,2);t!==null&&rt(t,e,2)}function ii(e){var t=Fe();if(typeof e=="function"){var l=e;if(e=l(),Ul){Kt(!0);try{l()}finally{Kt(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:qt,lastRenderedState:e},t}function Wr(e,t,l,a){return e.baseState=l,si(e,ge,typeof a=="function"?a:qt)}function nm(e,t,l,a,n){if(ms(e))throw Error(o(485));if(e=t.action,e!==null){var s={payload:n,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(i){s.listeners.push(i)}};f.T!==null?l(!0):s.isTransition=!1,a(s),l=t.pending,l===null?(s.next=t.pending=s,Fr(t,s)):(s.next=l.next,t.pending=l.next=s)}}function Fr(e,t){var l=t.action,a=t.payload,n=e.state;if(t.isTransition){var s=f.T,i={};f.T=i;try{var u=l(n,a),d=f.S;d!==null&&d(i,u),Ir(e,t,u)}catch(b){ui(e,t,b)}finally{f.T=s}}else try{s=l(n,a),Ir(e,t,s)}catch(b){ui(e,t,b)}}function Ir(e,t,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){Pr(e,t,a)},function(a){return ui(e,t,a)}):Pr(e,t,l)}function Pr(e,t,l){t.status="fulfilled",t.value=l,eo(t),e.state=l,t=e.pending,t!==null&&(l=t.next,l===t?e.pending=null:(l=l.next,t.next=l,Fr(e,l)))}function ui(e,t,l){var a=e.pending;if(e.pending=null,a!==null){a=a.next;do t.status="rejected",t.reason=l,eo(t),t=t.next;while(t!==a)}e.action=null}function eo(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function to(e,t){return t}function lo(e,t){if(oe){var l=pe.formState;if(l!==null){e:{var a=W;if(oe){if(ze){t:{for(var n=ze,s=wt;n.nodeType!==8;){if(!s){n=null;break t}if(n=jt(n.nextSibling),n===null){n=null;break t}}s=n.data,n=s==="F!"||s==="F"?n:null}if(n){ze=jt(n.nextSibling),a=n.data==="F!";break e}}Dl(a)}a=!1}a&&(t=l[0])}}return l=Fe(),l.memoizedState=l.baseState=t,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:to,lastRenderedState:t},l.queue=a,l=No.bind(null,W,a),a.dispatch=l,a=ii(!1),s=mi.bind(null,W,!1,a.queue),a=Fe(),n={state:t,dispatch:null,action:e,pending:null},a.queue=n,l=nm.bind(null,W,n,s,l),n.dispatch=l,a.memoizedState=e,[t,l,!1]}function ao(e){var t=Ee();return no(t,ge,e)}function no(e,t,l){if(t=si(e,t,to)[0],e=os(qt)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var a=en(t)}catch(i){throw i===Ja?ns:i}else a=t;t=Ee();var n=t.queue,s=n.dispatch;return l!==t.memoizedState&&(W.flags|=2048,oa(9,ds(),sm.bind(null,n,l),null)),[a,s,e]}function sm(e,t){e.action=t}function so(e){var t=Ee(),l=ge;if(l!==null)return no(t,l,e);Ee(),t=t.memoizedState,l=Ee();var a=l.queue.dispatch;return l.memoizedState=e,[t,a,!1]}function oa(e,t,l,a){return e={tag:e,create:l,deps:a,inst:t,next:null},t=W.updateQueue,t===null&&(t=ai(),W.updateQueue=t),l=t.lastEffect,l===null?t.lastEffect=e.next=e:(a=l.next,l.next=e,e.next=a,t.lastEffect=e),e}function ds(){return{destroy:void 0,resource:void 0}}function co(){return Ee().memoizedState}function fs(e,t,l,a){var n=Fe();a=a===void 0?null:a,W.flags|=e,n.memoizedState=oa(1|t,ds(),l,a)}function tn(e,t,l,a){var n=Ee();a=a===void 0?null:a;var s=n.memoizedState.inst;ge!==null&&a!==null&&Ic(a,ge.memoizedState.deps)?n.memoizedState=oa(t,s,l,a):(W.flags|=e,n.memoizedState=oa(1|t,s,l,a))}function io(e,t){fs(8390656,8,e,t)}function uo(e,t){tn(2048,8,e,t)}function ro(e,t){return tn(4,2,e,t)}function oo(e,t){return tn(4,4,e,t)}function fo(e,t){if(typeof t=="function"){e=e();var l=t(e);return function(){typeof l=="function"?l():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function mo(e,t,l){l=l!=null?l.concat([e]):null,tn(4,4,fo.bind(null,t,e),l)}function ri(){}function ho(e,t){var l=Ee();t=t===void 0?null:t;var a=l.memoizedState;return t!==null&&Ic(t,a[1])?a[0]:(l.memoizedState=[e,t],e)}function go(e,t){var l=Ee();t=t===void 0?null:t;var a=l.memoizedState;if(t!==null&&Ic(t,a[1]))return a[0];if(a=e(),Ul){Kt(!0);try{e()}finally{Kt(!1)}}return l.memoizedState=[a,t],a}function oi(e,t,l){return l===void 0||(el&1073741824)!==0?e.memoizedState=t:(e.memoizedState=l,e=yd(),W.lanes|=e,il|=e,l)}function xo(e,t,l,a){return at(l,t)?l:ia.current!==null?(e=oi(e,l,a),at(e,t)||(qe=!0),e):(el&42)===0?(qe=!0,e.memoizedState=l):(e=yd(),W.lanes|=e,il|=e,t)}function bo(e,t,l,a,n){var s=z.p;z.p=s!==0&&8>s?s:8;var i=f.T,u={};f.T=u,mi(e,!1,t,l);try{var d=n(),b=f.S;if(b!==null&&b(u,d),d!==null&&typeof d=="object"&&typeof d.then=="function"){var N=tm(d,a);ln(e,t,N,ut(e))}else ln(e,t,a,ut(e))}catch(A){ln(e,t,{then:function(){},status:"rejected",reason:A},ut())}finally{z.p=s,f.T=i}}function cm(){}function di(e,t,l,a){if(e.tag!==5)throw Error(o(476));var n=yo(e).queue;bo(e,n,t,E,l===null?cm:function(){return po(e),l(a)})}function yo(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:E,baseState:E,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:qt,lastRenderedState:E},next:null};var l={};return t.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:qt,lastRenderedState:l},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function po(e){var t=yo(e).next.queue;ln(e,t,{},ut())}function fi(){return Ze(jn)}function vo(){return Ee().memoizedState}function jo(){return Ee().memoizedState}function im(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var l=ut();e=It(l);var a=Pt(t,e,l);a!==null&&(rt(a,t,l),Wa(a,t,l)),t={cache:Qc()},e.payload=t;return}t=t.return}}function um(e,t,l){var a=ut();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},ms(e)?So(t,l):(l=Ec(e,t,l,a),l!==null&&(rt(l,e,a),wo(l,t,a)))}function No(e,t,l){var a=ut();ln(e,t,l,a)}function ln(e,t,l,a){var n={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(ms(e))So(t,n);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var i=t.lastRenderedState,u=s(i,l);if(n.hasEagerState=!0,n.eagerState=u,at(u,i))return Wn(e,t,n,0),pe===null&&$n(),!1}catch{}finally{}if(l=Ec(e,t,n,a),l!==null)return rt(l,e,a),wo(l,t,a),!0}return!1}function mi(e,t,l,a){if(a={lane:2,revertLane:Vi(),action:a,hasEagerState:!1,eagerState:null,next:null},ms(e)){if(t)throw Error(o(479))}else t=Ec(e,l,a,2),t!==null&&rt(t,e,2)}function ms(e){var t=e.alternate;return e===W||t!==null&&t===W}function So(e,t){ua=is=!0;var l=e.pending;l===null?t.next=t:(t.next=l.next,l.next=t),e.pending=t}function wo(e,t,l){if((l&4194048)!==0){var a=t.lanes;a&=e.pendingLanes,l|=a,t.lanes=l,Ou(e,l)}}var hs={readContext:Ze,use:rs,useCallback:De,useContext:De,useEffect:De,useImperativeHandle:De,useLayoutEffect:De,useInsertionEffect:De,useMemo:De,useReducer:De,useRef:De,useState:De,useDebugValue:De,useDeferredValue:De,useTransition:De,useSyncExternalStore:De,useId:De,useHostTransitionStatus:De,useFormState:De,useActionState:De,useOptimistic:De,useMemoCache:De,useCacheRefresh:De},Ao={readContext:Ze,use:rs,useCallback:function(e,t){return Fe().memoizedState=[e,t===void 0?null:t],e},useContext:Ze,useEffect:io,useImperativeHandle:function(e,t,l){l=l!=null?l.concat([e]):null,fs(4194308,4,fo.bind(null,t,e),l)},useLayoutEffect:function(e,t){return fs(4194308,4,e,t)},useInsertionEffect:function(e,t){fs(4,2,e,t)},useMemo:function(e,t){var l=Fe();t=t===void 0?null:t;var a=e();if(Ul){Kt(!0);try{e()}finally{Kt(!1)}}return l.memoizedState=[a,t],a},useReducer:function(e,t,l){var a=Fe();if(l!==void 0){var n=l(t);if(Ul){Kt(!0);try{l(t)}finally{Kt(!1)}}}else n=t;return a.memoizedState=a.baseState=n,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:n},a.queue=e,e=e.dispatch=um.bind(null,W,e),[a.memoizedState,e]},useRef:function(e){var t=Fe();return e={current:e},t.memoizedState=e},useState:function(e){e=ii(e);var t=e.queue,l=No.bind(null,W,t);return t.dispatch=l,[e.memoizedState,l]},useDebugValue:ri,useDeferredValue:function(e,t){var l=Fe();return oi(l,e,t)},useTransition:function(){var e=ii(!1);return e=bo.bind(null,W,e.queue,!0,!1),Fe().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,l){var a=W,n=Fe();if(oe){if(l===void 0)throw Error(o(407));l=l()}else{if(l=t(),pe===null)throw Error(o(349));(se&124)!==0||Vr(a,t,l)}n.memoizedState=l;var s={value:l,getSnapshot:t};return n.queue=s,io(Kr.bind(null,a,s,e),[e]),a.flags|=2048,oa(9,ds(),Zr.bind(null,a,s,l,t),null),l},useId:function(){var e=Fe(),t=pe.identifierPrefix;if(oe){var l=Ct,a=Ut;l=(a&~(1<<32-lt(a)-1)).toString(32)+l,t="«"+t+"R"+l,l=us++,0<l&&(t+="H"+l.toString(32)),t+="»"}else l=lm++,t="«"+t+"r"+l.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:fi,useFormState:lo,useActionState:lo,useOptimistic:function(e){var t=Fe();t.memoizedState=t.baseState=e;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=l,t=mi.bind(null,W,!0,l),l.dispatch=t,[e,t]},useMemoCache:ni,useCacheRefresh:function(){return Fe().memoizedState=im.bind(null,W)}},zo={readContext:Ze,use:rs,useCallback:ho,useContext:Ze,useEffect:uo,useImperativeHandle:mo,useInsertionEffect:ro,useLayoutEffect:oo,useMemo:go,useReducer:os,useRef:co,useState:function(){return os(qt)},useDebugValue:ri,useDeferredValue:function(e,t){var l=Ee();return xo(l,ge.memoizedState,e,t)},useTransition:function(){var e=os(qt)[0],t=Ee().memoizedState;return[typeof e=="boolean"?e:en(e),t]},useSyncExternalStore:Lr,useId:vo,useHostTransitionStatus:fi,useFormState:ao,useActionState:ao,useOptimistic:function(e,t){var l=Ee();return Wr(l,ge,e,t)},useMemoCache:ni,useCacheRefresh:jo},rm={readContext:Ze,use:rs,useCallback:ho,useContext:Ze,useEffect:uo,useImperativeHandle:mo,useInsertionEffect:ro,useLayoutEffect:oo,useMemo:go,useReducer:ci,useRef:co,useState:function(){return ci(qt)},useDebugValue:ri,useDeferredValue:function(e,t){var l=Ee();return ge===null?oi(l,e,t):xo(l,ge.memoizedState,e,t)},useTransition:function(){var e=ci(qt)[0],t=Ee().memoizedState;return[typeof e=="boolean"?e:en(e),t]},useSyncExternalStore:Lr,useId:vo,useHostTransitionStatus:fi,useFormState:so,useActionState:so,useOptimistic:function(e,t){var l=Ee();return ge!==null?Wr(l,ge,e,t):(l.baseState=e,[e,l.queue.dispatch])},useMemoCache:ni,useCacheRefresh:jo},da=null,an=0;function gs(e){var t=an;return an+=1,da===null&&(da=[]),Rr(da,e,t)}function nn(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function xs(e,t){throw t.$$typeof===C?Error(o(525)):(e=Object.prototype.toString.call(t),Error(o(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function To(e){var t=e._init;return t(e._payload)}function ko(e){function t(h,m){if(e){var x=h.deletions;x===null?(h.deletions=[m],h.flags|=16):x.push(m)}}function l(h,m){if(!e)return null;for(;m!==null;)t(h,m),m=m.sibling;return null}function a(h){for(var m=new Map;h!==null;)h.key!==null?m.set(h.key,h):m.set(h.index,h),h=h.sibling;return m}function n(h,m){return h=Et(h,m),h.index=0,h.sibling=null,h}function s(h,m,x){return h.index=x,e?(x=h.alternate,x!==null?(x=x.index,x<m?(h.flags|=67108866,m):x):(h.flags|=67108866,m)):(h.flags|=1048576,m)}function i(h){return e&&h.alternate===null&&(h.flags|=67108866),h}function u(h,m,x,S){return m===null||m.tag!==6?(m=Cc(x,h.mode,S),m.return=h,m):(m=n(m,x),m.return=h,m)}function d(h,m,x,S){var H=x.type;return H===ae?N(h,m,x.props.children,S,x.key):m!==null&&(m.elementType===H||typeof H=="object"&&H!==null&&H.$$typeof===ie&&To(H)===m.type)?(m=n(m,x.props),nn(m,x),m.return=h,m):(m=In(x.type,x.key,x.props,null,h.mode,S),nn(m,x),m.return=h,m)}function b(h,m,x,S){return m===null||m.tag!==4||m.stateNode.containerInfo!==x.containerInfo||m.stateNode.implementation!==x.implementation?(m=Bc(x,h.mode,S),m.return=h,m):(m=n(m,x.children||[]),m.return=h,m)}function N(h,m,x,S,H){return m===null||m.tag!==7?(m=Al(x,h.mode,S,H),m.return=h,m):(m=n(m,x),m.return=h,m)}function A(h,m,x){if(typeof m=="string"&&m!==""||typeof m=="number"||typeof m=="bigint")return m=Cc(""+m,h.mode,x),m.return=h,m;if(typeof m=="object"&&m!==null){switch(m.$$typeof){case q:return x=In(m.type,m.key,m.props,null,h.mode,x),nn(x,m),x.return=h,x;case Z:return m=Bc(m,h.mode,x),m.return=h,m;case ie:var S=m._init;return m=S(m._payload),A(h,m,x)}if(Ye(m)||U(m))return m=Al(m,h.mode,x,null),m.return=h,m;if(typeof m.then=="function")return A(h,gs(m),x);if(m.$$typeof===F)return A(h,ls(h,m),x);xs(h,m)}return null}function y(h,m,x,S){var H=m!==null?m.key:null;if(typeof x=="string"&&x!==""||typeof x=="number"||typeof x=="bigint")return H!==null?null:u(h,m,""+x,S);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case q:return x.key===H?d(h,m,x,S):null;case Z:return x.key===H?b(h,m,x,S):null;case ie:return H=x._init,x=H(x._payload),y(h,m,x,S)}if(Ye(x)||U(x))return H!==null?null:N(h,m,x,S,null);if(typeof x.then=="function")return y(h,m,gs(x),S);if(x.$$typeof===F)return y(h,m,ls(h,x),S);xs(h,x)}return null}function p(h,m,x,S,H){if(typeof S=="string"&&S!==""||typeof S=="number"||typeof S=="bigint")return h=h.get(x)||null,u(m,h,""+S,H);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case q:return h=h.get(S.key===null?x:S.key)||null,d(m,h,S,H);case Z:return h=h.get(S.key===null?x:S.key)||null,b(m,h,S,H);case ie:var I=S._init;return S=I(S._payload),p(h,m,x,S,H)}if(Ye(S)||U(S))return h=h.get(x)||null,N(m,h,S,H,null);if(typeof S.then=="function")return p(h,m,x,gs(S),H);if(S.$$typeof===F)return p(h,m,x,ls(m,S),H);xs(m,S)}return null}function L(h,m,x,S){for(var H=null,I=null,_=m,X=m=0,_e=null;_!==null&&X<x.length;X++){_.index>X?(_e=_,_=null):_e=_.sibling;var ue=y(h,_,x[X],S);if(ue===null){_===null&&(_=_e);break}e&&_&&ue.alternate===null&&t(h,_),m=s(ue,m,X),I===null?H=ue:I.sibling=ue,I=ue,_=_e}if(X===x.length)return l(h,_),oe&&Tl(h,X),H;if(_===null){for(;X<x.length;X++)_=A(h,x[X],S),_!==null&&(m=s(_,m,X),I===null?H=_:I.sibling=_,I=_);return oe&&Tl(h,X),H}for(_=a(_);X<x.length;X++)_e=p(_,h,X,x[X],S),_e!==null&&(e&&_e.alternate!==null&&_.delete(_e.key===null?X:_e.key),m=s(_e,m,X),I===null?H=_e:I.sibling=_e,I=_e);return e&&_.forEach(function(xl){return t(h,xl)}),oe&&Tl(h,X),H}function Q(h,m,x,S){if(x==null)throw Error(o(151));for(var H=null,I=null,_=m,X=m=0,_e=null,ue=x.next();_!==null&&!ue.done;X++,ue=x.next()){_.index>X?(_e=_,_=null):_e=_.sibling;var xl=y(h,_,ue.value,S);if(xl===null){_===null&&(_=_e);break}e&&_&&xl.alternate===null&&t(h,_),m=s(xl,m,X),I===null?H=xl:I.sibling=xl,I=xl,_=_e}if(ue.done)return l(h,_),oe&&Tl(h,X),H;if(_===null){for(;!ue.done;X++,ue=x.next())ue=A(h,ue.value,S),ue!==null&&(m=s(ue,m,X),I===null?H=ue:I.sibling=ue,I=ue);return oe&&Tl(h,X),H}for(_=a(_);!ue.done;X++,ue=x.next())ue=p(_,h,X,ue.value,S),ue!==null&&(e&&ue.alternate!==null&&_.delete(ue.key===null?X:ue.key),m=s(ue,m,X),I===null?H=ue:I.sibling=ue,I=ue);return e&&_.forEach(function(oh){return t(h,oh)}),oe&&Tl(h,X),H}function be(h,m,x,S){if(typeof x=="object"&&x!==null&&x.type===ae&&x.key===null&&(x=x.props.children),typeof x=="object"&&x!==null){switch(x.$$typeof){case q:e:{for(var H=x.key;m!==null;){if(m.key===H){if(H=x.type,H===ae){if(m.tag===7){l(h,m.sibling),S=n(m,x.props.children),S.return=h,h=S;break e}}else if(m.elementType===H||typeof H=="object"&&H!==null&&H.$$typeof===ie&&To(H)===m.type){l(h,m.sibling),S=n(m,x.props),nn(S,x),S.return=h,h=S;break e}l(h,m);break}else t(h,m);m=m.sibling}x.type===ae?(S=Al(x.props.children,h.mode,S,x.key),S.return=h,h=S):(S=In(x.type,x.key,x.props,null,h.mode,S),nn(S,x),S.return=h,h=S)}return i(h);case Z:e:{for(H=x.key;m!==null;){if(m.key===H)if(m.tag===4&&m.stateNode.containerInfo===x.containerInfo&&m.stateNode.implementation===x.implementation){l(h,m.sibling),S=n(m,x.children||[]),S.return=h,h=S;break e}else{l(h,m);break}else t(h,m);m=m.sibling}S=Bc(x,h.mode,S),S.return=h,h=S}return i(h);case ie:return H=x._init,x=H(x._payload),be(h,m,x,S)}if(Ye(x))return L(h,m,x,S);if(U(x)){if(H=U(x),typeof H!="function")throw Error(o(150));return x=H.call(x),Q(h,m,x,S)}if(typeof x.then=="function")return be(h,m,gs(x),S);if(x.$$typeof===F)return be(h,m,ls(h,x),S);xs(h,x)}return typeof x=="string"&&x!==""||typeof x=="number"||typeof x=="bigint"?(x=""+x,m!==null&&m.tag===6?(l(h,m.sibling),S=n(m,x),S.return=h,h=S):(l(h,m),S=Cc(x,h.mode,S),S.return=h,h=S),i(h)):l(h,m)}return function(h,m,x,S){try{an=0;var H=be(h,m,x,S);return da=null,H}catch(_){if(_===Ja||_===ns)throw _;var I=nt(29,_,null,h.mode);return I.lanes=S,I.return=h,I}finally{}}}var fa=ko(!0),Do=ko(!1),xt=ve(null),At=null;function tl(e){var t=e.alternate;he(Ce,Ce.current&1),he(xt,e),At===null&&(t===null||ia.current!==null||t.memoizedState!==null)&&(At=e)}function Mo(e){if(e.tag===22){if(he(Ce,Ce.current),he(xt,e),At===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(At=e)}}else ll()}function ll(){he(Ce,Ce.current),he(xt,xt.current)}function Ht(e){Se(xt),At===e&&(At=null),Se(Ce)}var Ce=ve(0);function bs(e){for(var t=e;t!==null;){if(t.tag===13){var l=t.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||au(l)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function hi(e,t,l,a){t=e.memoizedState,l=l(a,t),l=l==null?t:T({},t,l),e.memoizedState=l,e.lanes===0&&(e.updateQueue.baseState=l)}var gi={enqueueSetState:function(e,t,l){e=e._reactInternals;var a=ut(),n=It(a);n.payload=t,l!=null&&(n.callback=l),t=Pt(e,n,a),t!==null&&(rt(t,e,a),Wa(t,e,a))},enqueueReplaceState:function(e,t,l){e=e._reactInternals;var a=ut(),n=It(a);n.tag=1,n.payload=t,l!=null&&(n.callback=l),t=Pt(e,n,a),t!==null&&(rt(t,e,a),Wa(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var l=ut(),a=It(l);a.tag=2,t!=null&&(a.callback=t),t=Pt(e,a,l),t!==null&&(rt(t,e,l),Wa(t,e,l))}};function Oo(e,t,l,a,n,s,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(a,s,i):t.prototype&&t.prototype.isPureReactComponent?!Ga(l,a)||!Ga(n,s):!0}function Eo(e,t,l,a){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(l,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(l,a),t.state!==e&&gi.enqueueReplaceState(t,t.state,null)}function Cl(e,t){var l=t;if("ref"in t){l={};for(var a in t)a!=="ref"&&(l[a]=t[a])}if(e=e.defaultProps){l===t&&(l=T({},l));for(var n in e)l[n]===void 0&&(l[n]=e[n])}return l}var ys=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Uo(e){ys(e)}function Co(e){console.error(e)}function Bo(e){ys(e)}function ps(e,t){try{var l=e.onUncaughtError;l(t.value,{componentStack:t.stack})}catch(a){setTimeout(function(){throw a})}}function Ro(e,t,l){try{var a=e.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function xi(e,t,l){return l=It(l),l.tag=3,l.payload={element:null},l.callback=function(){ps(e,t)},l}function qo(e){return e=It(e),e.tag=3,e}function Ho(e,t,l,a){var n=l.type.getDerivedStateFromError;if(typeof n=="function"){var s=a.value;e.payload=function(){return n(s)},e.callback=function(){Ro(t,l,a)}}var i=l.stateNode;i!==null&&typeof i.componentDidCatch=="function"&&(e.callback=function(){Ro(t,l,a),typeof n!="function"&&(ul===null?ul=new Set([this]):ul.add(this));var u=a.stack;this.componentDidCatch(a.value,{componentStack:u!==null?u:""})})}function om(e,t,l,a,n){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(t=l.alternate,t!==null&&Va(t,l,n,!0),l=xt.current,l!==null){switch(l.tag){case 13:return At===null?Gi():l.alternate===null&&Te===0&&(Te=3),l.flags&=-257,l.flags|=65536,l.lanes=n,a===Vc?l.flags|=16384:(t=l.updateQueue,t===null?l.updateQueue=new Set([a]):t.add(a),Qi(e,a,n)),!1;case 22:return l.flags|=65536,a===Vc?l.flags|=16384:(t=l.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=t):(l=t.retryQueue,l===null?t.retryQueue=new Set([a]):l.add(a)),Qi(e,a,n)),!1}throw Error(o(435,l.tag))}return Qi(e,a,n),Gi(),!1}if(oe)return t=xt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=n,a!==Hc&&(e=Error(o(422),{cause:a}),La(ft(e,l)))):(a!==Hc&&(t=Error(o(423),{cause:a}),La(ft(t,l))),e=e.current.alternate,e.flags|=65536,n&=-n,e.lanes|=n,a=ft(a,l),n=xi(e.stateNode,a,n),Jc(e,n),Te!==4&&(Te=2)),!1;var s=Error(o(520),{cause:a});if(s=ft(s,l),fn===null?fn=[s]:fn.push(s),Te!==4&&(Te=2),t===null)return!0;a=ft(a,l),l=t;do{switch(l.tag){case 3:return l.flags|=65536,e=n&-n,l.lanes|=e,e=xi(l.stateNode,a,e),Jc(l,e),!1;case 1:if(t=l.type,s=l.stateNode,(l.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||s!==null&&typeof s.componentDidCatch=="function"&&(ul===null||!ul.has(s))))return l.flags|=65536,n&=-n,l.lanes|=n,n=qo(n),Ho(n,e,l,a),Jc(l,n),!1}l=l.return}while(l!==null);return!1}var _o=Error(o(461)),qe=!1;function Qe(e,t,l,a){t.child=e===null?Do(t,null,l,a):fa(t,e.child,l,a)}function Go(e,t,l,a,n){l=l.render;var s=t.ref;if("ref"in a){var i={};for(var u in a)u!=="ref"&&(i[u]=a[u])}else i=a;return Ol(t),a=Pc(e,t,l,i,s,n),u=ei(),e!==null&&!qe?(ti(e,t,n),_t(e,t,n)):(oe&&u&&Rc(t),t.flags|=1,Qe(e,t,a,n),t.child)}function Yo(e,t,l,a,n){if(e===null){var s=l.type;return typeof s=="function"&&!Uc(s)&&s.defaultProps===void 0&&l.compare===null?(t.tag=15,t.type=s,Qo(e,t,s,a,n)):(e=In(l.type,null,a,t,t.mode,n),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!wi(e,n)){var i=s.memoizedProps;if(l=l.compare,l=l!==null?l:Ga,l(i,a)&&e.ref===t.ref)return _t(e,t,n)}return t.flags|=1,e=Et(s,a),e.ref=t.ref,e.return=t,t.child=e}function Qo(e,t,l,a,n){if(e!==null){var s=e.memoizedProps;if(Ga(s,a)&&e.ref===t.ref)if(qe=!1,t.pendingProps=a=s,wi(e,n))(e.flags&131072)!==0&&(qe=!0);else return t.lanes=e.lanes,_t(e,t,n)}return bi(e,t,l,a,n)}function Xo(e,t,l){var a=t.pendingProps,n=a.children,s=e!==null?e.memoizedState:null;if(a.mode==="hidden"){if((t.flags&128)!==0){if(a=s!==null?s.baseLanes|l:l,e!==null){for(n=t.child=e.child,s=0;n!==null;)s=s|n.lanes|n.childLanes,n=n.sibling;t.childLanes=s&~a}else t.childLanes=0,t.child=null;return Lo(e,t,a,l)}if((l&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&as(t,s!==null?s.cachePool:null),s!==null?Yr(t,s):Wc(),Mo(t);else return t.lanes=t.childLanes=536870912,Lo(e,t,s!==null?s.baseLanes|l:l,l)}else s!==null?(as(t,s.cachePool),Yr(t,s),ll(),t.memoizedState=null):(e!==null&&as(t,null),Wc(),ll());return Qe(e,t,n,l),t.child}function Lo(e,t,l,a){var n=Lc();return n=n===null?null:{parent:Ue._currentValue,pool:n},t.memoizedState={baseLanes:l,cachePool:n},e!==null&&as(t,null),Wc(),Mo(t),e!==null&&Va(e,t,a,!0),null}function vs(e,t){var l=t.ref;if(l===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(o(284));(e===null||e.ref!==l)&&(t.flags|=4194816)}}function bi(e,t,l,a,n){return Ol(t),l=Pc(e,t,l,a,void 0,n),a=ei(),e!==null&&!qe?(ti(e,t,n),_t(e,t,n)):(oe&&a&&Rc(t),t.flags|=1,Qe(e,t,l,n),t.child)}function Vo(e,t,l,a,n,s){return Ol(t),t.updateQueue=null,l=Xr(t,a,l,n),Qr(e),a=ei(),e!==null&&!qe?(ti(e,t,s),_t(e,t,s)):(oe&&a&&Rc(t),t.flags|=1,Qe(e,t,l,s),t.child)}function Zo(e,t,l,a,n){if(Ol(t),t.stateNode===null){var s=la,i=l.contextType;typeof i=="object"&&i!==null&&(s=Ze(i)),s=new l(a,s),t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,s.updater=gi,t.stateNode=s,s._reactInternals=t,s=t.stateNode,s.props=a,s.state=t.memoizedState,s.refs={},Zc(t),i=l.contextType,s.context=typeof i=="object"&&i!==null?Ze(i):la,s.state=t.memoizedState,i=l.getDerivedStateFromProps,typeof i=="function"&&(hi(t,l,i,a),s.state=t.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(i=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),i!==s.state&&gi.enqueueReplaceState(s,s.state,null),Ia(t,a,s,n),Fa(),s.state=t.memoizedState),typeof s.componentDidMount=="function"&&(t.flags|=4194308),a=!0}else if(e===null){s=t.stateNode;var u=t.memoizedProps,d=Cl(l,u);s.props=d;var b=s.context,N=l.contextType;i=la,typeof N=="object"&&N!==null&&(i=Ze(N));var A=l.getDerivedStateFromProps;N=typeof A=="function"||typeof s.getSnapshotBeforeUpdate=="function",u=t.pendingProps!==u,N||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(u||b!==i)&&Eo(t,s,a,i),Ft=!1;var y=t.memoizedState;s.state=y,Ia(t,a,s,n),Fa(),b=t.memoizedState,u||y!==b||Ft?(typeof A=="function"&&(hi(t,l,A,a),b=t.memoizedState),(d=Ft||Oo(t,l,d,a,y,b,i))?(N||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=a,t.memoizedState=b),s.props=a,s.state=b,s.context=i,a=d):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),a=!1)}else{s=t.stateNode,Kc(e,t),i=t.memoizedProps,N=Cl(l,i),s.props=N,A=t.pendingProps,y=s.context,b=l.contextType,d=la,typeof b=="object"&&b!==null&&(d=Ze(b)),u=l.getDerivedStateFromProps,(b=typeof u=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(i!==A||y!==d)&&Eo(t,s,a,d),Ft=!1,y=t.memoizedState,s.state=y,Ia(t,a,s,n),Fa();var p=t.memoizedState;i!==A||y!==p||Ft||e!==null&&e.dependencies!==null&&ts(e.dependencies)?(typeof u=="function"&&(hi(t,l,u,a),p=t.memoizedState),(N=Ft||Oo(t,l,N,a,y,p,d)||e!==null&&e.dependencies!==null&&ts(e.dependencies))?(b||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(a,p,d),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(a,p,d)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||i===e.memoizedProps&&y===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&y===e.memoizedState||(t.flags|=1024),t.memoizedProps=a,t.memoizedState=p),s.props=a,s.state=p,s.context=d,a=N):(typeof s.componentDidUpdate!="function"||i===e.memoizedProps&&y===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&y===e.memoizedState||(t.flags|=1024),a=!1)}return s=a,vs(e,t),a=(t.flags&128)!==0,s||a?(s=t.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:s.render(),t.flags|=1,e!==null&&a?(t.child=fa(t,e.child,null,n),t.child=fa(t,null,l,n)):Qe(e,t,l,n),t.memoizedState=s.state,e=t.child):e=_t(e,t,n),e}function Ko(e,t,l,a){return Xa(),t.flags|=256,Qe(e,t,l,a),t.child}var yi={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function pi(e){return{baseLanes:e,cachePool:Ur()}}function vi(e,t,l){return e=e!==null?e.childLanes&~l:0,t&&(e|=bt),e}function Jo(e,t,l){var a=t.pendingProps,n=!1,s=(t.flags&128)!==0,i;if((i=s)||(i=e!==null&&e.memoizedState===null?!1:(Ce.current&2)!==0),i&&(n=!0,t.flags&=-129),i=(t.flags&32)!==0,t.flags&=-33,e===null){if(oe){if(n?tl(t):ll(),oe){var u=ze,d;if(d=u){e:{for(d=u,u=wt;d.nodeType!==8;){if(!u){u=null;break e}if(d=jt(d.nextSibling),d===null){u=null;break e}}u=d}u!==null?(t.memoizedState={dehydrated:u,treeContext:zl!==null?{id:Ut,overflow:Ct}:null,retryLane:536870912,hydrationErrors:null},d=nt(18,null,null,0),d.stateNode=u,d.return=t,t.child=d,Je=t,ze=null,d=!0):d=!1}d||Dl(t)}if(u=t.memoizedState,u!==null&&(u=u.dehydrated,u!==null))return au(u)?t.lanes=32:t.lanes=536870912,null;Ht(t)}return u=a.children,a=a.fallback,n?(ll(),n=t.mode,u=js({mode:"hidden",children:u},n),a=Al(a,n,l,null),u.return=t,a.return=t,u.sibling=a,t.child=u,n=t.child,n.memoizedState=pi(l),n.childLanes=vi(e,i,l),t.memoizedState=yi,a):(tl(t),ji(t,u))}if(d=e.memoizedState,d!==null&&(u=d.dehydrated,u!==null)){if(s)t.flags&256?(tl(t),t.flags&=-257,t=Ni(e,t,l)):t.memoizedState!==null?(ll(),t.child=e.child,t.flags|=128,t=null):(ll(),n=a.fallback,u=t.mode,a=js({mode:"visible",children:a.children},u),n=Al(n,u,l,null),n.flags|=2,a.return=t,n.return=t,a.sibling=n,t.child=a,fa(t,e.child,null,l),a=t.child,a.memoizedState=pi(l),a.childLanes=vi(e,i,l),t.memoizedState=yi,t=n);else if(tl(t),au(u)){if(i=u.nextSibling&&u.nextSibling.dataset,i)var b=i.dgst;i=b,a=Error(o(419)),a.stack="",a.digest=i,La({value:a,source:null,stack:null}),t=Ni(e,t,l)}else if(qe||Va(e,t,l,!1),i=(l&e.childLanes)!==0,qe||i){if(i=pe,i!==null&&(a=l&-l,a=(a&42)!==0?1:nc(a),a=(a&(i.suspendedLanes|l))!==0?0:a,a!==0&&a!==d.retryLane))throw d.retryLane=a,ta(e,a),rt(i,e,a),_o;u.data==="$?"||Gi(),t=Ni(e,t,l)}else u.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=d.treeContext,ze=jt(u.nextSibling),Je=t,oe=!0,kl=null,wt=!1,e!==null&&(ht[gt++]=Ut,ht[gt++]=Ct,ht[gt++]=zl,Ut=e.id,Ct=e.overflow,zl=t),t=ji(t,a.children),t.flags|=4096);return t}return n?(ll(),n=a.fallback,u=t.mode,d=e.child,b=d.sibling,a=Et(d,{mode:"hidden",children:a.children}),a.subtreeFlags=d.subtreeFlags&65011712,b!==null?n=Et(b,n):(n=Al(n,u,l,null),n.flags|=2),n.return=t,a.return=t,a.sibling=n,t.child=a,a=n,n=t.child,u=e.child.memoizedState,u===null?u=pi(l):(d=u.cachePool,d!==null?(b=Ue._currentValue,d=d.parent!==b?{parent:b,pool:b}:d):d=Ur(),u={baseLanes:u.baseLanes|l,cachePool:d}),n.memoizedState=u,n.childLanes=vi(e,i,l),t.memoizedState=yi,a):(tl(t),l=e.child,e=l.sibling,l=Et(l,{mode:"visible",children:a.children}),l.return=t,l.sibling=null,e!==null&&(i=t.deletions,i===null?(t.deletions=[e],t.flags|=16):i.push(e)),t.child=l,t.memoizedState=null,l)}function ji(e,t){return t=js({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function js(e,t){return e=nt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Ni(e,t,l){return fa(t,e.child,null,l),e=ji(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function $o(e,t,l){e.lanes|=t;var a=e.alternate;a!==null&&(a.lanes|=t),Gc(e.return,t,l)}function Si(e,t,l,a,n){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:n}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=a,s.tail=l,s.tailMode=n)}function Wo(e,t,l){var a=t.pendingProps,n=a.revealOrder,s=a.tail;if(Qe(e,t,a.children,l),a=Ce.current,(a&2)!==0)a=a&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&$o(e,l,t);else if(e.tag===19)$o(e,l,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=1}switch(he(Ce,a),n){case"forwards":for(l=t.child,n=null;l!==null;)e=l.alternate,e!==null&&bs(e)===null&&(n=l),l=l.sibling;l=n,l===null?(n=t.child,t.child=null):(n=l.sibling,l.sibling=null),Si(t,!1,n,l,s);break;case"backwards":for(l=null,n=t.child,t.child=null;n!==null;){if(e=n.alternate,e!==null&&bs(e)===null){t.child=n;break}e=n.sibling,n.sibling=l,l=n,n=e}Si(t,!0,l,null,s);break;case"together":Si(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function _t(e,t,l){if(e!==null&&(t.dependencies=e.dependencies),il|=t.lanes,(l&t.childLanes)===0)if(e!==null){if(Va(e,t,l,!1),(l&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(o(153));if(t.child!==null){for(e=t.child,l=Et(e,e.pendingProps),t.child=l,l.return=t;e.sibling!==null;)e=e.sibling,l=l.sibling=Et(e,e.pendingProps),l.return=t;l.sibling=null}return t.child}function wi(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&ts(e)))}function dm(e,t,l){switch(t.tag){case 3:En(t,t.stateNode.containerInfo),Wt(t,Ue,e.memoizedState.cache),Xa();break;case 27:case 5:Ps(t);break;case 4:En(t,t.stateNode.containerInfo);break;case 10:Wt(t,t.type,t.memoizedProps.value);break;case 13:var a=t.memoizedState;if(a!==null)return a.dehydrated!==null?(tl(t),t.flags|=128,null):(l&t.child.childLanes)!==0?Jo(e,t,l):(tl(t),e=_t(e,t,l),e!==null?e.sibling:null);tl(t);break;case 19:var n=(e.flags&128)!==0;if(a=(l&t.childLanes)!==0,a||(Va(e,t,l,!1),a=(l&t.childLanes)!==0),n){if(a)return Wo(e,t,l);t.flags|=128}if(n=t.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),he(Ce,Ce.current),a)break;return null;case 22:case 23:return t.lanes=0,Xo(e,t,l);case 24:Wt(t,Ue,e.memoizedState.cache)}return _t(e,t,l)}function Fo(e,t,l){if(e!==null)if(e.memoizedProps!==t.pendingProps)qe=!0;else{if(!wi(e,l)&&(t.flags&128)===0)return qe=!1,dm(e,t,l);qe=(e.flags&131072)!==0}else qe=!1,oe&&(t.flags&1048576)!==0&&zr(t,es,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var a=t.elementType,n=a._init;if(a=n(a._payload),t.type=a,typeof a=="function")Uc(a)?(e=Cl(a,e),t.tag=1,t=Zo(null,t,a,e,l)):(t.tag=0,t=bi(null,t,a,e,l));else{if(a!=null){if(n=a.$$typeof,n===K){t.tag=11,t=Go(null,t,a,e,l);break e}else if(n===ne){t.tag=14,t=Yo(null,t,a,e,l);break e}}throw t=Ae(a)||a,Error(o(306,t,""))}}return t;case 0:return bi(e,t,t.type,t.pendingProps,l);case 1:return a=t.type,n=Cl(a,t.pendingProps),Zo(e,t,a,n,l);case 3:e:{if(En(t,t.stateNode.containerInfo),e===null)throw Error(o(387));a=t.pendingProps;var s=t.memoizedState;n=s.element,Kc(e,t),Ia(t,a,null,l);var i=t.memoizedState;if(a=i.cache,Wt(t,Ue,a),a!==s.cache&&Yc(t,[Ue],l,!0),Fa(),a=i.element,s.isDehydrated)if(s={element:a,isDehydrated:!1,cache:i.cache},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){t=Ko(e,t,a,l);break e}else if(a!==n){n=ft(Error(o(424)),t),La(n),t=Ko(e,t,a,l);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(ze=jt(e.firstChild),Je=t,oe=!0,kl=null,wt=!0,l=Do(t,null,a,l),t.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(Xa(),a===n){t=_t(e,t,l);break e}Qe(e,t,a,l)}t=t.child}return t;case 26:return vs(e,t),e===null?(l=tf(t.type,null,t.pendingProps,null))?t.memoizedState=l:oe||(l=t.type,e=t.pendingProps,a=Bs(Zt.current).createElement(l),a[Ve]=t,a[$e]=e,Le(a,l,e),Re(a),t.stateNode=a):t.memoizedState=tf(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Ps(t),e===null&&oe&&(a=t.stateNode=Id(t.type,t.pendingProps,Zt.current),Je=t,wt=!0,n=ze,dl(t.type)?(nu=n,ze=jt(a.firstChild)):ze=n),Qe(e,t,t.pendingProps.children,l),vs(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&oe&&((n=a=ze)&&(a=_m(a,t.type,t.pendingProps,wt),a!==null?(t.stateNode=a,Je=t,ze=jt(a.firstChild),wt=!1,n=!0):n=!1),n||Dl(t)),Ps(t),n=t.type,s=t.pendingProps,i=e!==null?e.memoizedProps:null,a=s.children,eu(n,s)?a=null:i!==null&&eu(n,i)&&(t.flags|=32),t.memoizedState!==null&&(n=Pc(e,t,am,null,null,l),jn._currentValue=n),vs(e,t),Qe(e,t,a,l),t.child;case 6:return e===null&&oe&&((e=l=ze)&&(l=Gm(l,t.pendingProps,wt),l!==null?(t.stateNode=l,Je=t,ze=null,e=!0):e=!1),e||Dl(t)),null;case 13:return Jo(e,t,l);case 4:return En(t,t.stateNode.containerInfo),a=t.pendingProps,e===null?t.child=fa(t,null,a,l):Qe(e,t,a,l),t.child;case 11:return Go(e,t,t.type,t.pendingProps,l);case 7:return Qe(e,t,t.pendingProps,l),t.child;case 8:return Qe(e,t,t.pendingProps.children,l),t.child;case 12:return Qe(e,t,t.pendingProps.children,l),t.child;case 10:return a=t.pendingProps,Wt(t,t.type,a.value),Qe(e,t,a.children,l),t.child;case 9:return n=t.type._context,a=t.pendingProps.children,Ol(t),n=Ze(n),a=a(n),t.flags|=1,Qe(e,t,a,l),t.child;case 14:return Yo(e,t,t.type,t.pendingProps,l);case 15:return Qo(e,t,t.type,t.pendingProps,l);case 19:return Wo(e,t,l);case 31:return a=t.pendingProps,l=t.mode,a={mode:a.mode,children:a.children},e===null?(l=js(a,l),l.ref=t.ref,t.child=l,l.return=t,t=l):(l=Et(e.child,a),l.ref=t.ref,t.child=l,l.return=t,t=l),t;case 22:return Xo(e,t,l);case 24:return Ol(t),a=Ze(Ue),e===null?(n=Lc(),n===null&&(n=pe,s=Qc(),n.pooledCache=s,s.refCount++,s!==null&&(n.pooledCacheLanes|=l),n=s),t.memoizedState={parent:a,cache:n},Zc(t),Wt(t,Ue,n)):((e.lanes&l)!==0&&(Kc(e,t),Ia(t,null,null,l),Fa()),n=e.memoizedState,s=t.memoizedState,n.parent!==a?(n={parent:a,cache:a},t.memoizedState=n,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=n),Wt(t,Ue,a)):(a=s.cache,Wt(t,Ue,a),a!==n.cache&&Yc(t,[Ue],l,!0))),Qe(e,t,t.pendingProps.children,l),t.child;case 29:throw t.pendingProps}throw Error(o(156,t.tag))}function Gt(e){e.flags|=4}function Io(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!cf(t)){if(t=xt.current,t!==null&&((se&4194048)===se?At!==null:(se&62914560)!==se&&(se&536870912)===0||t!==At))throw $a=Vc,Cr;e.flags|=8192}}function Ns(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Du():536870912,e.lanes|=t,xa|=t)}function sn(e,t){if(!oe)switch(e.tailMode){case"hidden":t=e.tail;for(var l=null;t!==null;)t.alternate!==null&&(l=t),t=t.sibling;l===null?e.tail=null:l.sibling=null;break;case"collapsed":l=e.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:a.sibling=null}}function we(e){var t=e.alternate!==null&&e.alternate.child===e.child,l=0,a=0;if(t)for(var n=e.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags&65011712,a|=n.flags&65011712,n.return=e,n=n.sibling;else for(n=e.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags,a|=n.flags,n.return=e,n=n.sibling;return e.subtreeFlags|=a,e.childLanes=l,t}function fm(e,t,l){var a=t.pendingProps;switch(qc(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return we(t),null;case 1:return we(t),null;case 3:return l=t.stateNode,a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),Rt(Ue),Gl(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(e===null||e.child===null)&&(Qa(t)?Gt(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Dr())),we(t),null;case 26:return l=t.memoizedState,e===null?(Gt(t),l!==null?(we(t),Io(t,l)):(we(t),t.flags&=-16777217)):l?l!==e.memoizedState?(Gt(t),we(t),Io(t,l)):(we(t),t.flags&=-16777217):(e.memoizedProps!==a&&Gt(t),we(t),t.flags&=-16777217),null;case 27:Un(t),l=Zt.current;var n=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==a&&Gt(t);else{if(!a){if(t.stateNode===null)throw Error(o(166));return we(t),null}e=Be.current,Qa(t)?Tr(t):(e=Id(n,a,l),t.stateNode=e,Gt(t))}return we(t),null;case 5:if(Un(t),l=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==a&&Gt(t);else{if(!a){if(t.stateNode===null)throw Error(o(166));return we(t),null}if(e=Be.current,Qa(t))Tr(t);else{switch(n=Bs(Zt.current),e){case 1:e=n.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:e=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":e=n.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":e=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":e=n.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof a.is=="string"?n.createElement("select",{is:a.is}):n.createElement("select"),a.multiple?e.multiple=!0:a.size&&(e.size=a.size);break;default:e=typeof a.is=="string"?n.createElement(l,{is:a.is}):n.createElement(l)}}e[Ve]=t,e[$e]=a;e:for(n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break e;for(;n.sibling===null;){if(n.return===null||n.return===t)break e;n=n.return}n.sibling.return=n.return,n=n.sibling}t.stateNode=e;e:switch(Le(e,l,a),l){case"button":case"input":case"select":case"textarea":e=!!a.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Gt(t)}}return we(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==a&&Gt(t);else{if(typeof a!="string"&&t.stateNode===null)throw Error(o(166));if(e=Zt.current,Qa(t)){if(e=t.stateNode,l=t.memoizedProps,a=null,n=Je,n!==null)switch(n.tag){case 27:case 5:a=n.memoizedProps}e[Ve]=t,e=!!(e.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||Vd(e.nodeValue,l)),e||Dl(t)}else e=Bs(e).createTextNode(a),e[Ve]=t,t.stateNode=e}return we(t),null;case 13:if(a=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(n=Qa(t),a!==null&&a.dehydrated!==null){if(e===null){if(!n)throw Error(o(318));if(n=t.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(o(317));n[Ve]=t}else Xa(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;we(t),n=!1}else n=Dr(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=n),n=!0;if(!n)return t.flags&256?(Ht(t),t):(Ht(t),null)}if(Ht(t),(t.flags&128)!==0)return t.lanes=l,t;if(l=a!==null,e=e!==null&&e.memoizedState!==null,l){a=t.child,n=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(n=a.alternate.memoizedState.cachePool.pool);var s=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(s=a.memoizedState.cachePool.pool),s!==n&&(a.flags|=2048)}return l!==e&&l&&(t.child.flags|=8192),Ns(t,t.updateQueue),we(t),null;case 4:return Gl(),e===null&&$i(t.stateNode.containerInfo),we(t),null;case 10:return Rt(t.type),we(t),null;case 19:if(Se(Ce),n=t.memoizedState,n===null)return we(t),null;if(a=(t.flags&128)!==0,s=n.rendering,s===null)if(a)sn(n,!1);else{if(Te!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(s=bs(e),s!==null){for(t.flags|=128,sn(n,!1),e=s.updateQueue,t.updateQueue=e,Ns(t,e),t.subtreeFlags=0,e=l,l=t.child;l!==null;)Ar(l,e),l=l.sibling;return he(Ce,Ce.current&1|2),t.child}e=e.sibling}n.tail!==null&&St()>As&&(t.flags|=128,a=!0,sn(n,!1),t.lanes=4194304)}else{if(!a)if(e=bs(s),e!==null){if(t.flags|=128,a=!0,e=e.updateQueue,t.updateQueue=e,Ns(t,e),sn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!s.alternate&&!oe)return we(t),null}else 2*St()-n.renderingStartTime>As&&l!==536870912&&(t.flags|=128,a=!0,sn(n,!1),t.lanes=4194304);n.isBackwards?(s.sibling=t.child,t.child=s):(e=n.last,e!==null?e.sibling=s:t.child=s,n.last=s)}return n.tail!==null?(t=n.tail,n.rendering=t,n.tail=t.sibling,n.renderingStartTime=St(),t.sibling=null,e=Ce.current,he(Ce,a?e&1|2:e&1),t):(we(t),null);case 22:case 23:return Ht(t),Fc(),a=t.memoizedState!==null,e!==null?e.memoizedState!==null!==a&&(t.flags|=8192):a&&(t.flags|=8192),a?(l&536870912)!==0&&(t.flags&128)===0&&(we(t),t.subtreeFlags&6&&(t.flags|=8192)):we(t),l=t.updateQueue,l!==null&&Ns(t,l.retryQueue),l=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),a=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),a!==l&&(t.flags|=2048),e!==null&&Se(El),null;case 24:return l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),Rt(Ue),we(t),null;case 25:return null;case 30:return null}throw Error(o(156,t.tag))}function mm(e,t){switch(qc(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Rt(Ue),Gl(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return Un(t),null;case 13:if(Ht(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(o(340));Xa()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Se(Ce),null;case 4:return Gl(),null;case 10:return Rt(t.type),null;case 22:case 23:return Ht(t),Fc(),e!==null&&Se(El),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return Rt(Ue),null;case 25:return null;default:return null}}function Po(e,t){switch(qc(t),t.tag){case 3:Rt(Ue),Gl();break;case 26:case 27:case 5:Un(t);break;case 4:Gl();break;case 13:Ht(t);break;case 19:Se(Ce);break;case 10:Rt(t.type);break;case 22:case 23:Ht(t),Fc(),e!==null&&Se(El);break;case 24:Rt(Ue)}}function cn(e,t){try{var l=t.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var n=a.next;l=n;do{if((l.tag&e)===e){a=void 0;var s=l.create,i=l.inst;a=s(),i.destroy=a}l=l.next}while(l!==n)}}catch(u){ye(t,t.return,u)}}function al(e,t,l){try{var a=t.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var s=n.next;a=s;do{if((a.tag&e)===e){var i=a.inst,u=i.destroy;if(u!==void 0){i.destroy=void 0,n=t;var d=l,b=u;try{b()}catch(N){ye(n,d,N)}}}a=a.next}while(a!==s)}}catch(N){ye(t,t.return,N)}}function ed(e){var t=e.updateQueue;if(t!==null){var l=e.stateNode;try{Gr(t,l)}catch(a){ye(e,e.return,a)}}}function td(e,t,l){l.props=Cl(e.type,e.memoizedProps),l.state=e.memoizedState;try{l.componentWillUnmount()}catch(a){ye(e,t,a)}}function un(e,t){try{var l=e.ref;if(l!==null){switch(e.tag){case 26:case 27:case 5:var a=e.stateNode;break;case 30:a=e.stateNode;break;default:a=e.stateNode}typeof l=="function"?e.refCleanup=l(a):l.current=a}}catch(n){ye(e,t,n)}}function zt(e,t){var l=e.ref,a=e.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(n){ye(e,t,n)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(n){ye(e,t,n)}else l.current=null}function ld(e){var t=e.type,l=e.memoizedProps,a=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break e;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(n){ye(e,e.return,n)}}function Ai(e,t,l){try{var a=e.stateNode;Cm(a,e.type,l,t),a[$e]=t}catch(n){ye(e,e.return,n)}}function ad(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&dl(e.type)||e.tag===4}function zi(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||ad(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&dl(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ti(e,t,l){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(e,t):(t=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,t.appendChild(e),l=l._reactRootContainer,l!=null||t.onclick!==null||(t.onclick=Cs));else if(a!==4&&(a===27&&dl(e.type)&&(l=e.stateNode,t=null),e=e.child,e!==null))for(Ti(e,t,l),e=e.sibling;e!==null;)Ti(e,t,l),e=e.sibling}function Ss(e,t,l){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?l.insertBefore(e,t):l.appendChild(e);else if(a!==4&&(a===27&&dl(e.type)&&(l=e.stateNode),e=e.child,e!==null))for(Ss(e,t,l),e=e.sibling;e!==null;)Ss(e,t,l),e=e.sibling}function nd(e){var t=e.stateNode,l=e.memoizedProps;try{for(var a=e.type,n=t.attributes;n.length;)t.removeAttributeNode(n[0]);Le(t,a,l),t[Ve]=e,t[$e]=l}catch(s){ye(e,e.return,s)}}var Yt=!1,Me=!1,ki=!1,sd=typeof WeakSet=="function"?WeakSet:Set,He=null;function hm(e,t){if(e=e.containerInfo,Ii=Ys,e=gr(e),zc(e)){if("selectionStart"in e)var l={start:e.selectionStart,end:e.selectionEnd};else e:{l=(l=e.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var n=a.anchorOffset,s=a.focusNode;a=a.focusOffset;try{l.nodeType,s.nodeType}catch{l=null;break e}var i=0,u=-1,d=-1,b=0,N=0,A=e,y=null;t:for(;;){for(var p;A!==l||n!==0&&A.nodeType!==3||(u=i+n),A!==s||a!==0&&A.nodeType!==3||(d=i+a),A.nodeType===3&&(i+=A.nodeValue.length),(p=A.firstChild)!==null;)y=A,A=p;for(;;){if(A===e)break t;if(y===l&&++b===n&&(u=i),y===s&&++N===a&&(d=i),(p=A.nextSibling)!==null)break;A=y,y=A.parentNode}A=p}l=u===-1||d===-1?null:{start:u,end:d}}else l=null}l=l||{start:0,end:0}}else l=null;for(Pi={focusedElem:e,selectionRange:l},Ys=!1,He=t;He!==null;)if(t=He,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,He=e;else for(;He!==null;){switch(t=He,s=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&s!==null){e=void 0,l=t,n=s.memoizedProps,s=s.memoizedState,a=l.stateNode;try{var L=Cl(l.type,n,l.elementType===l.type);e=a.getSnapshotBeforeUpdate(L,s),a.__reactInternalSnapshotBeforeUpdate=e}catch(Q){ye(l,l.return,Q)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,l=e.nodeType,l===9)lu(e);else if(l===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":lu(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(o(163))}if(e=t.sibling,e!==null){e.return=t.return,He=e;break}He=t.return}}function cd(e,t,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:nl(e,l),a&4&&cn(5,l);break;case 1:if(nl(e,l),a&4)if(e=l.stateNode,t===null)try{e.componentDidMount()}catch(i){ye(l,l.return,i)}else{var n=Cl(l.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(n,t,e.__reactInternalSnapshotBeforeUpdate)}catch(i){ye(l,l.return,i)}}a&64&&ed(l),a&512&&un(l,l.return);break;case 3:if(nl(e,l),a&64&&(e=l.updateQueue,e!==null)){if(t=null,l.child!==null)switch(l.child.tag){case 27:case 5:t=l.child.stateNode;break;case 1:t=l.child.stateNode}try{Gr(e,t)}catch(i){ye(l,l.return,i)}}break;case 27:t===null&&a&4&&nd(l);case 26:case 5:nl(e,l),t===null&&a&4&&ld(l),a&512&&un(l,l.return);break;case 12:nl(e,l);break;case 13:nl(e,l),a&4&&rd(e,l),a&64&&(e=l.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(l=Sm.bind(null,l),Ym(e,l))));break;case 22:if(a=l.memoizedState!==null||Yt,!a){t=t!==null&&t.memoizedState!==null||Me,n=Yt;var s=Me;Yt=a,(Me=t)&&!s?sl(e,l,(l.subtreeFlags&8772)!==0):nl(e,l),Yt=n,Me=s}break;case 30:break;default:nl(e,l)}}function id(e){var t=e.alternate;t!==null&&(e.alternate=null,id(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&ic(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Ne=null,Ie=!1;function Qt(e,t,l){for(l=l.child;l!==null;)ud(e,t,l),l=l.sibling}function ud(e,t,l){if(tt&&typeof tt.onCommitFiberUnmount=="function")try{tt.onCommitFiberUnmount(ka,l)}catch{}switch(l.tag){case 26:Me||zt(l,t),Qt(e,t,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:Me||zt(l,t);var a=Ne,n=Ie;dl(l.type)&&(Ne=l.stateNode,Ie=!1),Qt(e,t,l),bn(l.stateNode),Ne=a,Ie=n;break;case 5:Me||zt(l,t);case 6:if(a=Ne,n=Ie,Ne=null,Qt(e,t,l),Ne=a,Ie=n,Ne!==null)if(Ie)try{(Ne.nodeType===9?Ne.body:Ne.nodeName==="HTML"?Ne.ownerDocument.body:Ne).removeChild(l.stateNode)}catch(s){ye(l,t,s)}else try{Ne.removeChild(l.stateNode)}catch(s){ye(l,t,s)}break;case 18:Ne!==null&&(Ie?(e=Ne,Wd(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,l.stateNode),An(e)):Wd(Ne,l.stateNode));break;case 4:a=Ne,n=Ie,Ne=l.stateNode.containerInfo,Ie=!0,Qt(e,t,l),Ne=a,Ie=n;break;case 0:case 11:case 14:case 15:Me||al(2,l,t),Me||al(4,l,t),Qt(e,t,l);break;case 1:Me||(zt(l,t),a=l.stateNode,typeof a.componentWillUnmount=="function"&&td(l,t,a)),Qt(e,t,l);break;case 21:Qt(e,t,l);break;case 22:Me=(a=Me)||l.memoizedState!==null,Qt(e,t,l),Me=a;break;default:Qt(e,t,l)}}function rd(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{An(e)}catch(l){ye(t,t.return,l)}}function gm(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new sd),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new sd),t;default:throw Error(o(435,e.tag))}}function Di(e,t){var l=gm(e);t.forEach(function(a){var n=wm.bind(null,e,a);l.has(a)||(l.add(a),a.then(n,n))})}function st(e,t){var l=t.deletions;if(l!==null)for(var a=0;a<l.length;a++){var n=l[a],s=e,i=t,u=i;e:for(;u!==null;){switch(u.tag){case 27:if(dl(u.type)){Ne=u.stateNode,Ie=!1;break e}break;case 5:Ne=u.stateNode,Ie=!1;break e;case 3:case 4:Ne=u.stateNode.containerInfo,Ie=!0;break e}u=u.return}if(Ne===null)throw Error(o(160));ud(s,i,n),Ne=null,Ie=!1,s=n.alternate,s!==null&&(s.return=null),n.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)od(t,e),t=t.sibling}var vt=null;function od(e,t){var l=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:st(t,e),ct(e),a&4&&(al(3,e,e.return),cn(3,e),al(5,e,e.return));break;case 1:st(t,e),ct(e),a&512&&(Me||l===null||zt(l,l.return)),a&64&&Yt&&(e=e.updateQueue,e!==null&&(a=e.callbacks,a!==null&&(l=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var n=vt;if(st(t,e),ct(e),a&512&&(Me||l===null||zt(l,l.return)),a&4){var s=l!==null?l.memoizedState:null;if(a=e.memoizedState,l===null)if(a===null)if(e.stateNode===null){e:{a=e.type,l=e.memoizedProps,n=n.ownerDocument||n;t:switch(a){case"title":s=n.getElementsByTagName("title")[0],(!s||s[Oa]||s[Ve]||s.namespaceURI==="http://www.w3.org/2000/svg"||s.hasAttribute("itemprop"))&&(s=n.createElement(a),n.head.insertBefore(s,n.querySelector("head > title"))),Le(s,a,l),s[Ve]=e,Re(s),a=s;break e;case"link":var i=nf("link","href",n).get(a+(l.href||""));if(i){for(var u=0;u<i.length;u++)if(s=i[u],s.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&s.getAttribute("rel")===(l.rel==null?null:l.rel)&&s.getAttribute("title")===(l.title==null?null:l.title)&&s.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){i.splice(u,1);break t}}s=n.createElement(a),Le(s,a,l),n.head.appendChild(s);break;case"meta":if(i=nf("meta","content",n).get(a+(l.content||""))){for(u=0;u<i.length;u++)if(s=i[u],s.getAttribute("content")===(l.content==null?null:""+l.content)&&s.getAttribute("name")===(l.name==null?null:l.name)&&s.getAttribute("property")===(l.property==null?null:l.property)&&s.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&s.getAttribute("charset")===(l.charSet==null?null:l.charSet)){i.splice(u,1);break t}}s=n.createElement(a),Le(s,a,l),n.head.appendChild(s);break;default:throw Error(o(468,a))}s[Ve]=e,Re(s),a=s}e.stateNode=a}else sf(n,e.type,e.stateNode);else e.stateNode=af(n,a,e.memoizedProps);else s!==a?(s===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):s.count--,a===null?sf(n,e.type,e.stateNode):af(n,a,e.memoizedProps)):a===null&&e.stateNode!==null&&Ai(e,e.memoizedProps,l.memoizedProps)}break;case 27:st(t,e),ct(e),a&512&&(Me||l===null||zt(l,l.return)),l!==null&&a&4&&Ai(e,e.memoizedProps,l.memoizedProps);break;case 5:if(st(t,e),ct(e),a&512&&(Me||l===null||zt(l,l.return)),e.flags&32){n=e.stateNode;try{Jl(n,"")}catch(p){ye(e,e.return,p)}}a&4&&e.stateNode!=null&&(n=e.memoizedProps,Ai(e,n,l!==null?l.memoizedProps:n)),a&1024&&(ki=!0);break;case 6:if(st(t,e),ct(e),a&4){if(e.stateNode===null)throw Error(o(162));a=e.memoizedProps,l=e.stateNode;try{l.nodeValue=a}catch(p){ye(e,e.return,p)}}break;case 3:if(Hs=null,n=vt,vt=Rs(t.containerInfo),st(t,e),vt=n,ct(e),a&4&&l!==null&&l.memoizedState.isDehydrated)try{An(t.containerInfo)}catch(p){ye(e,e.return,p)}ki&&(ki=!1,dd(e));break;case 4:a=vt,vt=Rs(e.stateNode.containerInfo),st(t,e),ct(e),vt=a;break;case 12:st(t,e),ct(e);break;case 13:st(t,e),ct(e),e.child.flags&8192&&e.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(Bi=St()),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,Di(e,a)));break;case 22:n=e.memoizedState!==null;var d=l!==null&&l.memoizedState!==null,b=Yt,N=Me;if(Yt=b||n,Me=N||d,st(t,e),Me=N,Yt=b,ct(e),a&8192)e:for(t=e.stateNode,t._visibility=n?t._visibility&-2:t._visibility|1,n&&(l===null||d||Yt||Me||Bl(e)),l=null,t=e;;){if(t.tag===5||t.tag===26){if(l===null){d=l=t;try{if(s=d.stateNode,n)i=s.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none";else{u=d.stateNode;var A=d.memoizedProps.style,y=A!=null&&A.hasOwnProperty("display")?A.display:null;u.style.display=y==null||typeof y=="boolean"?"":(""+y).trim()}}catch(p){ye(d,d.return,p)}}}else if(t.tag===6){if(l===null){d=t;try{d.stateNode.nodeValue=n?"":d.memoizedProps}catch(p){ye(d,d.return,p)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;l===t&&(l=null),t=t.return}l===t&&(l=null),t.sibling.return=t.return,t=t.sibling}a&4&&(a=e.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,Di(e,l))));break;case 19:st(t,e),ct(e),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,Di(e,a)));break;case 30:break;case 21:break;default:st(t,e),ct(e)}}function ct(e){var t=e.flags;if(t&2){try{for(var l,a=e.return;a!==null;){if(ad(a)){l=a;break}a=a.return}if(l==null)throw Error(o(160));switch(l.tag){case 27:var n=l.stateNode,s=zi(e);Ss(e,s,n);break;case 5:var i=l.stateNode;l.flags&32&&(Jl(i,""),l.flags&=-33);var u=zi(e);Ss(e,u,i);break;case 3:case 4:var d=l.stateNode.containerInfo,b=zi(e);Ti(e,b,d);break;default:throw Error(o(161))}}catch(N){ye(e,e.return,N)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function dd(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;dd(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function nl(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)cd(e,t.alternate,t),t=t.sibling}function Bl(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:al(4,t,t.return),Bl(t);break;case 1:zt(t,t.return);var l=t.stateNode;typeof l.componentWillUnmount=="function"&&td(t,t.return,l),Bl(t);break;case 27:bn(t.stateNode);case 26:case 5:zt(t,t.return),Bl(t);break;case 22:t.memoizedState===null&&Bl(t);break;case 30:Bl(t);break;default:Bl(t)}e=e.sibling}}function sl(e,t,l){for(l=l&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var a=t.alternate,n=e,s=t,i=s.flags;switch(s.tag){case 0:case 11:case 15:sl(n,s,l),cn(4,s);break;case 1:if(sl(n,s,l),a=s,n=a.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(b){ye(a,a.return,b)}if(a=s,n=a.updateQueue,n!==null){var u=a.stateNode;try{var d=n.shared.hiddenCallbacks;if(d!==null)for(n.shared.hiddenCallbacks=null,n=0;n<d.length;n++)_r(d[n],u)}catch(b){ye(a,a.return,b)}}l&&i&64&&ed(s),un(s,s.return);break;case 27:nd(s);case 26:case 5:sl(n,s,l),l&&a===null&&i&4&&ld(s),un(s,s.return);break;case 12:sl(n,s,l);break;case 13:sl(n,s,l),l&&i&4&&rd(n,s);break;case 22:s.memoizedState===null&&sl(n,s,l),un(s,s.return);break;case 30:break;default:sl(n,s,l)}t=t.sibling}}function Mi(e,t){var l=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==l&&(e!=null&&e.refCount++,l!=null&&Za(l))}function Oi(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Za(e))}function Tt(e,t,l,a){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)fd(e,t,l,a),t=t.sibling}function fd(e,t,l,a){var n=t.flags;switch(t.tag){case 0:case 11:case 15:Tt(e,t,l,a),n&2048&&cn(9,t);break;case 1:Tt(e,t,l,a);break;case 3:Tt(e,t,l,a),n&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Za(e)));break;case 12:if(n&2048){Tt(e,t,l,a),e=t.stateNode;try{var s=t.memoizedProps,i=s.id,u=s.onPostCommit;typeof u=="function"&&u(i,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(d){ye(t,t.return,d)}}else Tt(e,t,l,a);break;case 13:Tt(e,t,l,a);break;case 23:break;case 22:s=t.stateNode,i=t.alternate,t.memoizedState!==null?s._visibility&2?Tt(e,t,l,a):rn(e,t):s._visibility&2?Tt(e,t,l,a):(s._visibility|=2,ma(e,t,l,a,(t.subtreeFlags&10256)!==0)),n&2048&&Mi(i,t);break;case 24:Tt(e,t,l,a),n&2048&&Oi(t.alternate,t);break;default:Tt(e,t,l,a)}}function ma(e,t,l,a,n){for(n=n&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var s=e,i=t,u=l,d=a,b=i.flags;switch(i.tag){case 0:case 11:case 15:ma(s,i,u,d,n),cn(8,i);break;case 23:break;case 22:var N=i.stateNode;i.memoizedState!==null?N._visibility&2?ma(s,i,u,d,n):rn(s,i):(N._visibility|=2,ma(s,i,u,d,n)),n&&b&2048&&Mi(i.alternate,i);break;case 24:ma(s,i,u,d,n),n&&b&2048&&Oi(i.alternate,i);break;default:ma(s,i,u,d,n)}t=t.sibling}}function rn(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var l=e,a=t,n=a.flags;switch(a.tag){case 22:rn(l,a),n&2048&&Mi(a.alternate,a);break;case 24:rn(l,a),n&2048&&Oi(a.alternate,a);break;default:rn(l,a)}t=t.sibling}}var on=8192;function ha(e){if(e.subtreeFlags&on)for(e=e.child;e!==null;)md(e),e=e.sibling}function md(e){switch(e.tag){case 26:ha(e),e.flags&on&&e.memoizedState!==null&&eh(vt,e.memoizedState,e.memoizedProps);break;case 5:ha(e);break;case 3:case 4:var t=vt;vt=Rs(e.stateNode.containerInfo),ha(e),vt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=on,on=16777216,ha(e),on=t):ha(e));break;default:ha(e)}}function hd(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function dn(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var l=0;l<t.length;l++){var a=t[l];He=a,xd(a,e)}hd(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)gd(e),e=e.sibling}function gd(e){switch(e.tag){case 0:case 11:case 15:dn(e),e.flags&2048&&al(9,e,e.return);break;case 3:dn(e);break;case 12:dn(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,ws(e)):dn(e);break;default:dn(e)}}function ws(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var l=0;l<t.length;l++){var a=t[l];He=a,xd(a,e)}hd(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:al(8,t,t.return),ws(t);break;case 22:l=t.stateNode,l._visibility&2&&(l._visibility&=-3,ws(t));break;default:ws(t)}e=e.sibling}}function xd(e,t){for(;He!==null;){var l=He;switch(l.tag){case 0:case 11:case 15:al(8,l,t);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:Za(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,He=a;else e:for(l=e;He!==null;){a=He;var n=a.sibling,s=a.return;if(id(a),a===l){He=null;break e}if(n!==null){n.return=s,He=n;break e}He=s}}}var xm={getCacheForType:function(e){var t=Ze(Ue),l=t.data.get(e);return l===void 0&&(l=e(),t.data.set(e,l)),l}},bm=typeof WeakMap=="function"?WeakMap:Map,fe=0,pe=null,te=null,se=0,me=0,it=null,cl=!1,ga=!1,Ei=!1,Xt=0,Te=0,il=0,Rl=0,Ui=0,bt=0,xa=0,fn=null,Pe=null,Ci=!1,Bi=0,As=1/0,zs=null,ul=null,Xe=0,rl=null,ba=null,ya=0,Ri=0,qi=null,bd=null,mn=0,Hi=null;function ut(){if((fe&2)!==0&&se!==0)return se&-se;if(f.T!==null){var e=sa;return e!==0?e:Vi()}return Eu()}function yd(){bt===0&&(bt=(se&536870912)===0||oe?ku():536870912);var e=xt.current;return e!==null&&(e.flags|=32),bt}function rt(e,t,l){(e===pe&&(me===2||me===9)||e.cancelPendingCommit!==null)&&(pa(e,0),ol(e,se,bt,!1)),Ma(e,l),((fe&2)===0||e!==pe)&&(e===pe&&((fe&2)===0&&(Rl|=l),Te===4&&ol(e,se,bt,!1)),kt(e))}function pd(e,t,l){if((fe&6)!==0)throw Error(o(327));var a=!l&&(t&124)===0&&(t&e.expiredLanes)===0||Da(e,t),n=a?vm(e,t):Yi(e,t,!0),s=a;do{if(n===0){ga&&!a&&ol(e,t,0,!1);break}else{if(l=e.current.alternate,s&&!ym(l)){n=Yi(e,t,!1),s=!1;continue}if(n===2){if(s=t,e.errorRecoveryDisabledLanes&s)var i=0;else i=e.pendingLanes&-536870913,i=i!==0?i:i&536870912?536870912:0;if(i!==0){t=i;e:{var u=e;n=fn;var d=u.current.memoizedState.isDehydrated;if(d&&(pa(u,i).flags|=256),i=Yi(u,i,!1),i!==2){if(Ei&&!d){u.errorRecoveryDisabledLanes|=s,Rl|=s,n=4;break e}s=Pe,Pe=n,s!==null&&(Pe===null?Pe=s:Pe.push.apply(Pe,s))}n=i}if(s=!1,n!==2)continue}}if(n===1){pa(e,0),ol(e,t,0,!0);break}e:{switch(a=e,s=n,s){case 0:case 1:throw Error(o(345));case 4:if((t&4194048)!==t)break;case 6:ol(a,t,bt,!cl);break e;case 2:Pe=null;break;case 3:case 5:break;default:throw Error(o(329))}if((t&62914560)===t&&(n=Bi+300-St(),10<n)){if(ol(a,t,bt,!cl),qn(a,0,!0)!==0)break e;a.timeoutHandle=Jd(vd.bind(null,a,l,Pe,zs,Ci,t,bt,Rl,xa,cl,s,2,-0,0),n);break e}vd(a,l,Pe,zs,Ci,t,bt,Rl,xa,cl,s,0,-0,0)}}break}while(!0);kt(e)}function vd(e,t,l,a,n,s,i,u,d,b,N,A,y,p){if(e.timeoutHandle=-1,A=t.subtreeFlags,(A&8192||(A&16785408)===16785408)&&(vn={stylesheets:null,count:0,unsuspend:Pm},md(t),A=th(),A!==null)){e.cancelPendingCommit=A(Td.bind(null,e,t,s,l,a,n,i,u,d,N,1,y,p)),ol(e,s,i,!b);return}Td(e,t,s,l,a,n,i,u,d)}function ym(e){for(var t=e;;){var l=t.tag;if((l===0||l===11||l===15)&&t.flags&16384&&(l=t.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var n=l[a],s=n.getSnapshot;n=n.value;try{if(!at(s(),n))return!1}catch{return!1}}if(l=t.child,t.subtreeFlags&16384&&l!==null)l.return=t,t=l;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function ol(e,t,l,a){t&=~Ui,t&=~Rl,e.suspendedLanes|=t,e.pingedLanes&=~t,a&&(e.warmLanes|=t),a=e.expirationTimes;for(var n=t;0<n;){var s=31-lt(n),i=1<<s;a[s]=-1,n&=~i}l!==0&&Mu(e,l,t)}function Ts(){return(fe&6)===0?(hn(0),!1):!0}function _i(){if(te!==null){if(me===0)var e=te.return;else e=te,Bt=Ml=null,li(e),da=null,an=0,e=te;for(;e!==null;)Po(e.alternate,e),e=e.return;te=null}}function pa(e,t){var l=e.timeoutHandle;l!==-1&&(e.timeoutHandle=-1,Rm(l)),l=e.cancelPendingCommit,l!==null&&(e.cancelPendingCommit=null,l()),_i(),pe=e,te=l=Et(e.current,null),se=t,me=0,it=null,cl=!1,ga=Da(e,t),Ei=!1,xa=bt=Ui=Rl=il=Te=0,Pe=fn=null,Ci=!1,(t&8)!==0&&(t|=t&32);var a=e.entangledLanes;if(a!==0)for(e=e.entanglements,a&=t;0<a;){var n=31-lt(a),s=1<<n;t|=e[n],a&=~s}return Xt=t,$n(),l}function jd(e,t){W=null,f.H=hs,t===Ja||t===ns?(t=qr(),me=3):t===Cr?(t=qr(),me=4):me=t===_o?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,it=t,te===null&&(Te=1,ps(e,ft(t,e.current)))}function Nd(){var e=f.H;return f.H=hs,e===null?hs:e}function Sd(){var e=f.A;return f.A=xm,e}function Gi(){Te=4,cl||(se&4194048)!==se&&xt.current!==null||(ga=!0),(il&134217727)===0&&(Rl&134217727)===0||pe===null||ol(pe,se,bt,!1)}function Yi(e,t,l){var a=fe;fe|=2;var n=Nd(),s=Sd();(pe!==e||se!==t)&&(zs=null,pa(e,t)),t=!1;var i=Te;e:do try{if(me!==0&&te!==null){var u=te,d=it;switch(me){case 8:_i(),i=6;break e;case 3:case 2:case 9:case 6:xt.current===null&&(t=!0);var b=me;if(me=0,it=null,va(e,u,d,b),l&&ga){i=0;break e}break;default:b=me,me=0,it=null,va(e,u,d,b)}}pm(),i=Te;break}catch(N){jd(e,N)}while(!0);return t&&e.shellSuspendCounter++,Bt=Ml=null,fe=a,f.H=n,f.A=s,te===null&&(pe=null,se=0,$n()),i}function pm(){for(;te!==null;)wd(te)}function vm(e,t){var l=fe;fe|=2;var a=Nd(),n=Sd();pe!==e||se!==t?(zs=null,As=St()+500,pa(e,t)):ga=Da(e,t);e:do try{if(me!==0&&te!==null){t=te;var s=it;t:switch(me){case 1:me=0,it=null,va(e,t,s,1);break;case 2:case 9:if(Br(s)){me=0,it=null,Ad(t);break}t=function(){me!==2&&me!==9||pe!==e||(me=7),kt(e)},s.then(t,t);break e;case 3:me=7;break e;case 4:me=5;break e;case 7:Br(s)?(me=0,it=null,Ad(t)):(me=0,it=null,va(e,t,s,7));break;case 5:var i=null;switch(te.tag){case 26:i=te.memoizedState;case 5:case 27:var u=te;if(!i||cf(i)){me=0,it=null;var d=u.sibling;if(d!==null)te=d;else{var b=u.return;b!==null?(te=b,ks(b)):te=null}break t}}me=0,it=null,va(e,t,s,5);break;case 6:me=0,it=null,va(e,t,s,6);break;case 8:_i(),Te=6;break e;default:throw Error(o(462))}}jm();break}catch(N){jd(e,N)}while(!0);return Bt=Ml=null,f.H=a,f.A=n,fe=l,te!==null?0:(pe=null,se=0,$n(),Te)}function jm(){for(;te!==null&&!Xf();)wd(te)}function wd(e){var t=Fo(e.alternate,e,Xt);e.memoizedProps=e.pendingProps,t===null?ks(e):te=t}function Ad(e){var t=e,l=t.alternate;switch(t.tag){case 15:case 0:t=Vo(l,t,t.pendingProps,t.type,void 0,se);break;case 11:t=Vo(l,t,t.pendingProps,t.type.render,t.ref,se);break;case 5:li(t);default:Po(l,t),t=te=Ar(t,Xt),t=Fo(l,t,Xt)}e.memoizedProps=e.pendingProps,t===null?ks(e):te=t}function va(e,t,l,a){Bt=Ml=null,li(t),da=null,an=0;var n=t.return;try{if(om(e,n,t,l,se)){Te=1,ps(e,ft(l,e.current)),te=null;return}}catch(s){if(n!==null)throw te=n,s;Te=1,ps(e,ft(l,e.current)),te=null;return}t.flags&32768?(oe||a===1?e=!0:ga||(se&536870912)!==0?e=!1:(cl=e=!0,(a===2||a===9||a===3||a===6)&&(a=xt.current,a!==null&&a.tag===13&&(a.flags|=16384))),zd(t,e)):ks(t)}function ks(e){var t=e;do{if((t.flags&32768)!==0){zd(t,cl);return}e=t.return;var l=fm(t.alternate,t,Xt);if(l!==null){te=l;return}if(t=t.sibling,t!==null){te=t;return}te=t=e}while(t!==null);Te===0&&(Te=5)}function zd(e,t){do{var l=mm(e.alternate,e);if(l!==null){l.flags&=32767,te=l;return}if(l=e.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!t&&(e=e.sibling,e!==null)){te=e;return}te=e=l}while(e!==null);Te=6,te=null}function Td(e,t,l,a,n,s,i,u,d){e.cancelPendingCommit=null;do Ds();while(Xe!==0);if((fe&6)!==0)throw Error(o(327));if(t!==null){if(t===e.current)throw Error(o(177));if(s=t.lanes|t.childLanes,s|=Oc,Pf(e,l,s,i,u,d),e===pe&&(te=pe=null,se=0),ba=t,rl=e,ya=l,Ri=s,qi=n,bd=a,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,Am(Cn,function(){return Ed(),null})):(e.callbackNode=null,e.callbackPriority=0),a=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||a){a=f.T,f.T=null,n=z.p,z.p=2,i=fe,fe|=4;try{hm(e,t,l)}finally{fe=i,z.p=n,f.T=a}}Xe=1,kd(),Dd(),Md()}}function kd(){if(Xe===1){Xe=0;var e=rl,t=ba,l=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||l){l=f.T,f.T=null;var a=z.p;z.p=2;var n=fe;fe|=4;try{od(t,e);var s=Pi,i=gr(e.containerInfo),u=s.focusedElem,d=s.selectionRange;if(i!==u&&u&&u.ownerDocument&&hr(u.ownerDocument.documentElement,u)){if(d!==null&&zc(u)){var b=d.start,N=d.end;if(N===void 0&&(N=b),"selectionStart"in u)u.selectionStart=b,u.selectionEnd=Math.min(N,u.value.length);else{var A=u.ownerDocument||document,y=A&&A.defaultView||window;if(y.getSelection){var p=y.getSelection(),L=u.textContent.length,Q=Math.min(d.start,L),be=d.end===void 0?Q:Math.min(d.end,L);!p.extend&&Q>be&&(i=be,be=Q,Q=i);var h=mr(u,Q),m=mr(u,be);if(h&&m&&(p.rangeCount!==1||p.anchorNode!==h.node||p.anchorOffset!==h.offset||p.focusNode!==m.node||p.focusOffset!==m.offset)){var x=A.createRange();x.setStart(h.node,h.offset),p.removeAllRanges(),Q>be?(p.addRange(x),p.extend(m.node,m.offset)):(x.setEnd(m.node,m.offset),p.addRange(x))}}}}for(A=[],p=u;p=p.parentNode;)p.nodeType===1&&A.push({element:p,left:p.scrollLeft,top:p.scrollTop});for(typeof u.focus=="function"&&u.focus(),u=0;u<A.length;u++){var S=A[u];S.element.scrollLeft=S.left,S.element.scrollTop=S.top}}Ys=!!Ii,Pi=Ii=null}finally{fe=n,z.p=a,f.T=l}}e.current=t,Xe=2}}function Dd(){if(Xe===2){Xe=0;var e=rl,t=ba,l=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||l){l=f.T,f.T=null;var a=z.p;z.p=2;var n=fe;fe|=4;try{cd(e,t.alternate,t)}finally{fe=n,z.p=a,f.T=l}}Xe=3}}function Md(){if(Xe===4||Xe===3){Xe=0,Lf();var e=rl,t=ba,l=ya,a=bd;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Xe=5:(Xe=0,ba=rl=null,Od(e,e.pendingLanes));var n=e.pendingLanes;if(n===0&&(ul=null),sc(l),t=t.stateNode,tt&&typeof tt.onCommitFiberRoot=="function")try{tt.onCommitFiberRoot(ka,t,void 0,(t.current.flags&128)===128)}catch{}if(a!==null){t=f.T,n=z.p,z.p=2,f.T=null;try{for(var s=e.onRecoverableError,i=0;i<a.length;i++){var u=a[i];s(u.value,{componentStack:u.stack})}}finally{f.T=t,z.p=n}}(ya&3)!==0&&Ds(),kt(e),n=e.pendingLanes,(l&4194090)!==0&&(n&42)!==0?e===Hi?mn++:(mn=0,Hi=e):mn=0,hn(0)}}function Od(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Za(t)))}function Ds(e){return kd(),Dd(),Md(),Ed()}function Ed(){if(Xe!==5)return!1;var e=rl,t=Ri;Ri=0;var l=sc(ya),a=f.T,n=z.p;try{z.p=32>l?32:l,f.T=null,l=qi,qi=null;var s=rl,i=ya;if(Xe=0,ba=rl=null,ya=0,(fe&6)!==0)throw Error(o(331));var u=fe;if(fe|=4,gd(s.current),fd(s,s.current,i,l),fe=u,hn(0,!1),tt&&typeof tt.onPostCommitFiberRoot=="function")try{tt.onPostCommitFiberRoot(ka,s)}catch{}return!0}finally{z.p=n,f.T=a,Od(e,t)}}function Ud(e,t,l){t=ft(l,t),t=xi(e.stateNode,t,2),e=Pt(e,t,2),e!==null&&(Ma(e,2),kt(e))}function ye(e,t,l){if(e.tag===3)Ud(e,e,l);else for(;t!==null;){if(t.tag===3){Ud(t,e,l);break}else if(t.tag===1){var a=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(ul===null||!ul.has(a))){e=ft(l,e),l=qo(2),a=Pt(t,l,2),a!==null&&(Ho(l,a,t,e),Ma(a,2),kt(a));break}}t=t.return}}function Qi(e,t,l){var a=e.pingCache;if(a===null){a=e.pingCache=new bm;var n=new Set;a.set(t,n)}else n=a.get(t),n===void 0&&(n=new Set,a.set(t,n));n.has(l)||(Ei=!0,n.add(l),e=Nm.bind(null,e,t,l),t.then(e,e))}function Nm(e,t,l){var a=e.pingCache;a!==null&&a.delete(t),e.pingedLanes|=e.suspendedLanes&l,e.warmLanes&=~l,pe===e&&(se&l)===l&&(Te===4||Te===3&&(se&62914560)===se&&300>St()-Bi?(fe&2)===0&&pa(e,0):Ui|=l,xa===se&&(xa=0)),kt(e)}function Cd(e,t){t===0&&(t=Du()),e=ta(e,t),e!==null&&(Ma(e,t),kt(e))}function Sm(e){var t=e.memoizedState,l=0;t!==null&&(l=t.retryLane),Cd(e,l)}function wm(e,t){var l=0;switch(e.tag){case 13:var a=e.stateNode,n=e.memoizedState;n!==null&&(l=n.retryLane);break;case 19:a=e.stateNode;break;case 22:a=e.stateNode._retryCache;break;default:throw Error(o(314))}a!==null&&a.delete(t),Cd(e,l)}function Am(e,t){return tc(e,t)}var Ms=null,ja=null,Xi=!1,Os=!1,Li=!1,ql=0;function kt(e){e!==ja&&e.next===null&&(ja===null?Ms=ja=e:ja=ja.next=e),Os=!0,Xi||(Xi=!0,Tm())}function hn(e,t){if(!Li&&Os){Li=!0;do for(var l=!1,a=Ms;a!==null;){if(e!==0){var n=a.pendingLanes;if(n===0)var s=0;else{var i=a.suspendedLanes,u=a.pingedLanes;s=(1<<31-lt(42|e)+1)-1,s&=n&~(i&~u),s=s&201326741?s&201326741|1:s?s|2:0}s!==0&&(l=!0,Hd(a,s))}else s=se,s=qn(a,a===pe?s:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(s&3)===0||Da(a,s)||(l=!0,Hd(a,s));a=a.next}while(l);Li=!1}}function zm(){Bd()}function Bd(){Os=Xi=!1;var e=0;ql!==0&&(Bm()&&(e=ql),ql=0);for(var t=St(),l=null,a=Ms;a!==null;){var n=a.next,s=Rd(a,t);s===0?(a.next=null,l===null?Ms=n:l.next=n,n===null&&(ja=l)):(l=a,(e!==0||(s&3)!==0)&&(Os=!0)),a=n}hn(e)}function Rd(e,t){for(var l=e.suspendedLanes,a=e.pingedLanes,n=e.expirationTimes,s=e.pendingLanes&-62914561;0<s;){var i=31-lt(s),u=1<<i,d=n[i];d===-1?((u&l)===0||(u&a)!==0)&&(n[i]=If(u,t)):d<=t&&(e.expiredLanes|=u),s&=~u}if(t=pe,l=se,l=qn(e,e===t?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a=e.callbackNode,l===0||e===t&&(me===2||me===9)||e.cancelPendingCommit!==null)return a!==null&&a!==null&&lc(a),e.callbackNode=null,e.callbackPriority=0;if((l&3)===0||Da(e,l)){if(t=l&-l,t===e.callbackPriority)return t;switch(a!==null&&lc(a),sc(l)){case 2:case 8:l=zu;break;case 32:l=Cn;break;case 268435456:l=Tu;break;default:l=Cn}return a=qd.bind(null,e),l=tc(l,a),e.callbackPriority=t,e.callbackNode=l,t}return a!==null&&a!==null&&lc(a),e.callbackPriority=2,e.callbackNode=null,2}function qd(e,t){if(Xe!==0&&Xe!==5)return e.callbackNode=null,e.callbackPriority=0,null;var l=e.callbackNode;if(Ds()&&e.callbackNode!==l)return null;var a=se;return a=qn(e,e===pe?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a===0?null:(pd(e,a,t),Rd(e,St()),e.callbackNode!=null&&e.callbackNode===l?qd.bind(null,e):null)}function Hd(e,t){if(Ds())return null;pd(e,t,!0)}function Tm(){qm(function(){(fe&6)!==0?tc(Au,zm):Bd()})}function Vi(){return ql===0&&(ql=ku()),ql}function _d(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Qn(""+e)}function Gd(e,t){var l=t.ownerDocument.createElement("input");return l.name=t.name,l.value=t.value,e.id&&l.setAttribute("form",e.id),t.parentNode.insertBefore(l,t),e=new FormData(e),l.parentNode.removeChild(l),e}function km(e,t,l,a,n){if(t==="submit"&&l&&l.stateNode===n){var s=_d((n[$e]||null).action),i=a.submitter;i&&(t=(t=i[$e]||null)?_d(t.formAction):i.getAttribute("formAction"),t!==null&&(s=t,i=null));var u=new Zn("action","action",null,a,n);e.push({event:u,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(ql!==0){var d=i?Gd(n,i):new FormData(n);di(l,{pending:!0,data:d,method:n.method,action:s},null,d)}}else typeof s=="function"&&(u.preventDefault(),d=i?Gd(n,i):new FormData(n),di(l,{pending:!0,data:d,method:n.method,action:s},s,d))},currentTarget:n}]})}}for(var Zi=0;Zi<Mc.length;Zi++){var Ki=Mc[Zi],Dm=Ki.toLowerCase(),Mm=Ki[0].toUpperCase()+Ki.slice(1);pt(Dm,"on"+Mm)}pt(yr,"onAnimationEnd"),pt(pr,"onAnimationIteration"),pt(vr,"onAnimationStart"),pt("dblclick","onDoubleClick"),pt("focusin","onFocus"),pt("focusout","onBlur"),pt(K0,"onTransitionRun"),pt(J0,"onTransitionStart"),pt($0,"onTransitionCancel"),pt(jr,"onTransitionEnd"),Vl("onMouseEnter",["mouseout","mouseover"]),Vl("onMouseLeave",["mouseout","mouseover"]),Vl("onPointerEnter",["pointerout","pointerover"]),Vl("onPointerLeave",["pointerout","pointerover"]),jl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),jl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),jl("onBeforeInput",["compositionend","keypress","textInput","paste"]),jl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),jl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),jl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var gn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Om=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(gn));function Yd(e,t){t=(t&4)!==0;for(var l=0;l<e.length;l++){var a=e[l],n=a.event;a=a.listeners;e:{var s=void 0;if(t)for(var i=a.length-1;0<=i;i--){var u=a[i],d=u.instance,b=u.currentTarget;if(u=u.listener,d!==s&&n.isPropagationStopped())break e;s=u,n.currentTarget=b;try{s(n)}catch(N){ys(N)}n.currentTarget=null,s=d}else for(i=0;i<a.length;i++){if(u=a[i],d=u.instance,b=u.currentTarget,u=u.listener,d!==s&&n.isPropagationStopped())break e;s=u,n.currentTarget=b;try{s(n)}catch(N){ys(N)}n.currentTarget=null,s=d}}}}function le(e,t){var l=t[cc];l===void 0&&(l=t[cc]=new Set);var a=e+"__bubble";l.has(a)||(Qd(t,e,2,!1),l.add(a))}function Ji(e,t,l){var a=0;t&&(a|=4),Qd(l,e,a,t)}var Es="_reactListening"+Math.random().toString(36).slice(2);function $i(e){if(!e[Es]){e[Es]=!0,Cu.forEach(function(l){l!=="selectionchange"&&(Om.has(l)||Ji(l,!1,e),Ji(l,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Es]||(t[Es]=!0,Ji("selectionchange",!1,t))}}function Qd(e,t,l,a){switch(mf(t)){case 2:var n=nh;break;case 8:n=sh;break;default:n=ru}l=n.bind(null,t,l,e),n=void 0,!bc||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(n=!0),a?n!==void 0?e.addEventListener(t,l,{capture:!0,passive:n}):e.addEventListener(t,l,!0):n!==void 0?e.addEventListener(t,l,{passive:n}):e.addEventListener(t,l,!1)}function Wi(e,t,l,a,n){var s=a;if((t&1)===0&&(t&2)===0&&a!==null)e:for(;;){if(a===null)return;var i=a.tag;if(i===3||i===4){var u=a.stateNode.containerInfo;if(u===n)break;if(i===4)for(i=a.return;i!==null;){var d=i.tag;if((d===3||d===4)&&i.stateNode.containerInfo===n)return;i=i.return}for(;u!==null;){if(i=Ql(u),i===null)return;if(d=i.tag,d===5||d===6||d===26||d===27){a=s=i;continue e}u=u.parentNode}}a=a.return}Ju(function(){var b=s,N=gc(l),A=[];e:{var y=Nr.get(e);if(y!==void 0){var p=Zn,L=e;switch(e){case"keypress":if(Ln(l)===0)break e;case"keydown":case"keyup":p=A0;break;case"focusin":L="focus",p=jc;break;case"focusout":L="blur",p=jc;break;case"beforeblur":case"afterblur":p=jc;break;case"click":if(l.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":p=Fu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":p=m0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":p=k0;break;case yr:case pr:case vr:p=x0;break;case jr:p=M0;break;case"scroll":case"scrollend":p=d0;break;case"wheel":p=E0;break;case"copy":case"cut":case"paste":p=y0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":p=Pu;break;case"toggle":case"beforetoggle":p=C0}var Q=(t&4)!==0,be=!Q&&(e==="scroll"||e==="scrollend"),h=Q?y!==null?y+"Capture":null:y;Q=[];for(var m=b,x;m!==null;){var S=m;if(x=S.stateNode,S=S.tag,S!==5&&S!==26&&S!==27||x===null||h===null||(S=Ua(m,h),S!=null&&Q.push(xn(m,S,x))),be)break;m=m.return}0<Q.length&&(y=new p(y,L,null,l,N),A.push({event:y,listeners:Q}))}}if((t&7)===0){e:{if(y=e==="mouseover"||e==="pointerover",p=e==="mouseout"||e==="pointerout",y&&l!==hc&&(L=l.relatedTarget||l.fromElement)&&(Ql(L)||L[Yl]))break e;if((p||y)&&(y=N.window===N?N:(y=N.ownerDocument)?y.defaultView||y.parentWindow:window,p?(L=l.relatedTarget||l.toElement,p=b,L=L?Ql(L):null,L!==null&&(be=v(L),Q=L.tag,L!==be||Q!==5&&Q!==27&&Q!==6)&&(L=null)):(p=null,L=b),p!==L)){if(Q=Fu,S="onMouseLeave",h="onMouseEnter",m="mouse",(e==="pointerout"||e==="pointerover")&&(Q=Pu,S="onPointerLeave",h="onPointerEnter",m="pointer"),be=p==null?y:Ea(p),x=L==null?y:Ea(L),y=new Q(S,m+"leave",p,l,N),y.target=be,y.relatedTarget=x,S=null,Ql(N)===b&&(Q=new Q(h,m+"enter",L,l,N),Q.target=x,Q.relatedTarget=be,S=Q),be=S,p&&L)t:{for(Q=p,h=L,m=0,x=Q;x;x=Na(x))m++;for(x=0,S=h;S;S=Na(S))x++;for(;0<m-x;)Q=Na(Q),m--;for(;0<x-m;)h=Na(h),x--;for(;m--;){if(Q===h||h!==null&&Q===h.alternate)break t;Q=Na(Q),h=Na(h)}Q=null}else Q=null;p!==null&&Xd(A,y,p,Q,!1),L!==null&&be!==null&&Xd(A,be,L,Q,!0)}}e:{if(y=b?Ea(b):window,p=y.nodeName&&y.nodeName.toLowerCase(),p==="select"||p==="input"&&y.type==="file")var H=ir;else if(sr(y))if(ur)H=L0;else{H=Q0;var I=Y0}else p=y.nodeName,!p||p.toLowerCase()!=="input"||y.type!=="checkbox"&&y.type!=="radio"?b&&mc(b.elementType)&&(H=ir):H=X0;if(H&&(H=H(e,b))){cr(A,H,l,N);break e}I&&I(e,y,b),e==="focusout"&&b&&y.type==="number"&&b.memoizedProps.value!=null&&fc(y,"number",y.value)}switch(I=b?Ea(b):window,e){case"focusin":(sr(I)||I.contentEditable==="true")&&(Il=I,Tc=b,Ya=null);break;case"focusout":Ya=Tc=Il=null;break;case"mousedown":kc=!0;break;case"contextmenu":case"mouseup":case"dragend":kc=!1,xr(A,l,N);break;case"selectionchange":if(Z0)break;case"keydown":case"keyup":xr(A,l,N)}var _;if(Sc)e:{switch(e){case"compositionstart":var X="onCompositionStart";break e;case"compositionend":X="onCompositionEnd";break e;case"compositionupdate":X="onCompositionUpdate";break e}X=void 0}else Fl?ar(e,l)&&(X="onCompositionEnd"):e==="keydown"&&l.keyCode===229&&(X="onCompositionStart");X&&(er&&l.locale!=="ko"&&(Fl||X!=="onCompositionStart"?X==="onCompositionEnd"&&Fl&&(_=$u()):($t=N,yc="value"in $t?$t.value:$t.textContent,Fl=!0)),I=Us(b,X),0<I.length&&(X=new Iu(X,e,null,l,N),A.push({event:X,listeners:I}),_?X.data=_:(_=nr(l),_!==null&&(X.data=_)))),(_=R0?q0(e,l):H0(e,l))&&(X=Us(b,"onBeforeInput"),0<X.length&&(I=new Iu("onBeforeInput","beforeinput",null,l,N),A.push({event:I,listeners:X}),I.data=_)),km(A,e,b,l,N)}Yd(A,t)})}function xn(e,t,l){return{instance:e,listener:t,currentTarget:l}}function Us(e,t){for(var l=t+"Capture",a=[];e!==null;){var n=e,s=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||s===null||(n=Ua(e,l),n!=null&&a.unshift(xn(e,n,s)),n=Ua(e,t),n!=null&&a.push(xn(e,n,s))),e.tag===3)return a;e=e.return}return[]}function Na(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function Xd(e,t,l,a,n){for(var s=t._reactName,i=[];l!==null&&l!==a;){var u=l,d=u.alternate,b=u.stateNode;if(u=u.tag,d!==null&&d===a)break;u!==5&&u!==26&&u!==27||b===null||(d=b,n?(b=Ua(l,s),b!=null&&i.unshift(xn(l,b,d))):n||(b=Ua(l,s),b!=null&&i.push(xn(l,b,d)))),l=l.return}i.length!==0&&e.push({event:t,listeners:i})}var Em=/\r\n?/g,Um=/\u0000|\uFFFD/g;function Ld(e){return(typeof e=="string"?e:""+e).replace(Em,`
`).replace(Um,"")}function Vd(e,t){return t=Ld(t),Ld(e)===t}function Cs(){}function xe(e,t,l,a,n,s){switch(l){case"children":typeof a=="string"?t==="body"||t==="textarea"&&a===""||Jl(e,a):(typeof a=="number"||typeof a=="bigint")&&t!=="body"&&Jl(e,""+a);break;case"className":_n(e,"class",a);break;case"tabIndex":_n(e,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":_n(e,l,a);break;case"style":Zu(e,a,s);break;case"data":if(t!=="object"){_n(e,"data",a);break}case"src":case"href":if(a===""&&(t!=="a"||l!=="href")){e.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(l);break}a=Qn(""+a),e.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){e.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof s=="function"&&(l==="formAction"?(t!=="input"&&xe(e,t,"name",n.name,n,null),xe(e,t,"formEncType",n.formEncType,n,null),xe(e,t,"formMethod",n.formMethod,n,null),xe(e,t,"formTarget",n.formTarget,n,null)):(xe(e,t,"encType",n.encType,n,null),xe(e,t,"method",n.method,n,null),xe(e,t,"target",n.target,n,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(l);break}a=Qn(""+a),e.setAttribute(l,a);break;case"onClick":a!=null&&(e.onclick=Cs);break;case"onScroll":a!=null&&le("scroll",e);break;case"onScrollEnd":a!=null&&le("scrollend",e);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(o(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(o(60));e.innerHTML=l}}break;case"multiple":e.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":e.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){e.removeAttribute("xlink:href");break}l=Qn(""+a),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,""+a):e.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,""):e.removeAttribute(l);break;case"capture":case"download":a===!0?e.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,a):e.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?e.setAttribute(l,a):e.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?e.removeAttribute(l):e.setAttribute(l,a);break;case"popover":le("beforetoggle",e),le("toggle",e),Hn(e,"popover",a);break;case"xlinkActuate":Mt(e,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":Mt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":Mt(e,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":Mt(e,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":Mt(e,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":Mt(e,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":Mt(e,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":Mt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":Mt(e,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Hn(e,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=r0.get(l)||l,Hn(e,l,a))}}function Fi(e,t,l,a,n,s){switch(l){case"style":Zu(e,a,s);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(o(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(o(60));e.innerHTML=l}}break;case"children":typeof a=="string"?Jl(e,a):(typeof a=="number"||typeof a=="bigint")&&Jl(e,""+a);break;case"onScroll":a!=null&&le("scroll",e);break;case"onScrollEnd":a!=null&&le("scrollend",e);break;case"onClick":a!=null&&(e.onclick=Cs);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Bu.hasOwnProperty(l))e:{if(l[0]==="o"&&l[1]==="n"&&(n=l.endsWith("Capture"),t=l.slice(2,n?l.length-7:void 0),s=e[$e]||null,s=s!=null?s[l]:null,typeof s=="function"&&e.removeEventListener(t,s,n),typeof a=="function")){typeof s!="function"&&s!==null&&(l in e?e[l]=null:e.hasAttribute(l)&&e.removeAttribute(l)),e.addEventListener(t,a,n);break e}l in e?e[l]=a:a===!0?e.setAttribute(l,""):Hn(e,l,a)}}}function Le(e,t,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":le("error",e),le("load",e);var a=!1,n=!1,s;for(s in l)if(l.hasOwnProperty(s)){var i=l[s];if(i!=null)switch(s){case"src":a=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:xe(e,t,s,i,l,null)}}n&&xe(e,t,"srcSet",l.srcSet,l,null),a&&xe(e,t,"src",l.src,l,null);return;case"input":le("invalid",e);var u=s=i=n=null,d=null,b=null;for(a in l)if(l.hasOwnProperty(a)){var N=l[a];if(N!=null)switch(a){case"name":n=N;break;case"type":i=N;break;case"checked":d=N;break;case"defaultChecked":b=N;break;case"value":s=N;break;case"defaultValue":u=N;break;case"children":case"dangerouslySetInnerHTML":if(N!=null)throw Error(o(137,t));break;default:xe(e,t,a,N,l,null)}}Qu(e,s,u,d,b,i,n,!1),Gn(e);return;case"select":le("invalid",e),a=i=s=null;for(n in l)if(l.hasOwnProperty(n)&&(u=l[n],u!=null))switch(n){case"value":s=u;break;case"defaultValue":i=u;break;case"multiple":a=u;default:xe(e,t,n,u,l,null)}t=s,l=i,e.multiple=!!a,t!=null?Kl(e,!!a,t,!1):l!=null&&Kl(e,!!a,l,!0);return;case"textarea":le("invalid",e),s=n=a=null;for(i in l)if(l.hasOwnProperty(i)&&(u=l[i],u!=null))switch(i){case"value":a=u;break;case"defaultValue":n=u;break;case"children":s=u;break;case"dangerouslySetInnerHTML":if(u!=null)throw Error(o(91));break;default:xe(e,t,i,u,l,null)}Lu(e,a,n,s),Gn(e);return;case"option":for(d in l)if(l.hasOwnProperty(d)&&(a=l[d],a!=null))switch(d){case"selected":e.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:xe(e,t,d,a,l,null)}return;case"dialog":le("beforetoggle",e),le("toggle",e),le("cancel",e),le("close",e);break;case"iframe":case"object":le("load",e);break;case"video":case"audio":for(a=0;a<gn.length;a++)le(gn[a],e);break;case"image":le("error",e),le("load",e);break;case"details":le("toggle",e);break;case"embed":case"source":case"link":le("error",e),le("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(b in l)if(l.hasOwnProperty(b)&&(a=l[b],a!=null))switch(b){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:xe(e,t,b,a,l,null)}return;default:if(mc(t)){for(N in l)l.hasOwnProperty(N)&&(a=l[N],a!==void 0&&Fi(e,t,N,a,l,void 0));return}}for(u in l)l.hasOwnProperty(u)&&(a=l[u],a!=null&&xe(e,t,u,a,l,null))}function Cm(e,t,l,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,s=null,i=null,u=null,d=null,b=null,N=null;for(p in l){var A=l[p];if(l.hasOwnProperty(p)&&A!=null)switch(p){case"checked":break;case"value":break;case"defaultValue":d=A;default:a.hasOwnProperty(p)||xe(e,t,p,null,a,A)}}for(var y in a){var p=a[y];if(A=l[y],a.hasOwnProperty(y)&&(p!=null||A!=null))switch(y){case"type":s=p;break;case"name":n=p;break;case"checked":b=p;break;case"defaultChecked":N=p;break;case"value":i=p;break;case"defaultValue":u=p;break;case"children":case"dangerouslySetInnerHTML":if(p!=null)throw Error(o(137,t));break;default:p!==A&&xe(e,t,y,p,a,A)}}dc(e,i,u,d,b,N,s,n);return;case"select":p=i=u=y=null;for(s in l)if(d=l[s],l.hasOwnProperty(s)&&d!=null)switch(s){case"value":break;case"multiple":p=d;default:a.hasOwnProperty(s)||xe(e,t,s,null,a,d)}for(n in a)if(s=a[n],d=l[n],a.hasOwnProperty(n)&&(s!=null||d!=null))switch(n){case"value":y=s;break;case"defaultValue":u=s;break;case"multiple":i=s;default:s!==d&&xe(e,t,n,s,a,d)}t=u,l=i,a=p,y!=null?Kl(e,!!l,y,!1):!!a!=!!l&&(t!=null?Kl(e,!!l,t,!0):Kl(e,!!l,l?[]:"",!1));return;case"textarea":p=y=null;for(u in l)if(n=l[u],l.hasOwnProperty(u)&&n!=null&&!a.hasOwnProperty(u))switch(u){case"value":break;case"children":break;default:xe(e,t,u,null,a,n)}for(i in a)if(n=a[i],s=l[i],a.hasOwnProperty(i)&&(n!=null||s!=null))switch(i){case"value":y=n;break;case"defaultValue":p=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(o(91));break;default:n!==s&&xe(e,t,i,n,a,s)}Xu(e,y,p);return;case"option":for(var L in l)if(y=l[L],l.hasOwnProperty(L)&&y!=null&&!a.hasOwnProperty(L))switch(L){case"selected":e.selected=!1;break;default:xe(e,t,L,null,a,y)}for(d in a)if(y=a[d],p=l[d],a.hasOwnProperty(d)&&y!==p&&(y!=null||p!=null))switch(d){case"selected":e.selected=y&&typeof y!="function"&&typeof y!="symbol";break;default:xe(e,t,d,y,a,p)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var Q in l)y=l[Q],l.hasOwnProperty(Q)&&y!=null&&!a.hasOwnProperty(Q)&&xe(e,t,Q,null,a,y);for(b in a)if(y=a[b],p=l[b],a.hasOwnProperty(b)&&y!==p&&(y!=null||p!=null))switch(b){case"children":case"dangerouslySetInnerHTML":if(y!=null)throw Error(o(137,t));break;default:xe(e,t,b,y,a,p)}return;default:if(mc(t)){for(var be in l)y=l[be],l.hasOwnProperty(be)&&y!==void 0&&!a.hasOwnProperty(be)&&Fi(e,t,be,void 0,a,y);for(N in a)y=a[N],p=l[N],!a.hasOwnProperty(N)||y===p||y===void 0&&p===void 0||Fi(e,t,N,y,a,p);return}}for(var h in l)y=l[h],l.hasOwnProperty(h)&&y!=null&&!a.hasOwnProperty(h)&&xe(e,t,h,null,a,y);for(A in a)y=a[A],p=l[A],!a.hasOwnProperty(A)||y===p||y==null&&p==null||xe(e,t,A,y,a,p)}var Ii=null,Pi=null;function Bs(e){return e.nodeType===9?e:e.ownerDocument}function Zd(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Kd(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function eu(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var tu=null;function Bm(){var e=window.event;return e&&e.type==="popstate"?e===tu?!1:(tu=e,!0):(tu=null,!1)}var Jd=typeof setTimeout=="function"?setTimeout:void 0,Rm=typeof clearTimeout=="function"?clearTimeout:void 0,$d=typeof Promise=="function"?Promise:void 0,qm=typeof queueMicrotask=="function"?queueMicrotask:typeof $d<"u"?function(e){return $d.resolve(null).then(e).catch(Hm)}:Jd;function Hm(e){setTimeout(function(){throw e})}function dl(e){return e==="head"}function Wd(e,t){var l=t,a=0,n=0;do{var s=l.nextSibling;if(e.removeChild(l),s&&s.nodeType===8)if(l=s.data,l==="/$"){if(0<a&&8>a){l=a;var i=e.ownerDocument;if(l&1&&bn(i.documentElement),l&2&&bn(i.body),l&4)for(l=i.head,bn(l),i=l.firstChild;i;){var u=i.nextSibling,d=i.nodeName;i[Oa]||d==="SCRIPT"||d==="STYLE"||d==="LINK"&&i.rel.toLowerCase()==="stylesheet"||l.removeChild(i),i=u}}if(n===0){e.removeChild(s),An(t);return}n--}else l==="$"||l==="$?"||l==="$!"?n++:a=l.charCodeAt(0)-48;else a=0;l=s}while(l);An(t)}function lu(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var l=t;switch(t=t.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":lu(l),ic(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}e.removeChild(l)}}function _m(e,t,l,a){for(;e.nodeType===1;){var n=l;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!a&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(a){if(!e[Oa])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(s=e.getAttribute("rel"),s==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(s!==n.rel||e.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||e.getAttribute("title")!==(n.title==null?null:n.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(s=e.getAttribute("src"),(s!==(n.src==null?null:n.src)||e.getAttribute("type")!==(n.type==null?null:n.type)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&s&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var s=n.name==null?null:""+n.name;if(n.type==="hidden"&&e.getAttribute("name")===s)return e}else return e;if(e=jt(e.nextSibling),e===null)break}return null}function Gm(e,t,l){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!l||(e=jt(e.nextSibling),e===null))return null;return e}function au(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function Ym(e,t){var l=e.ownerDocument;if(e.data!=="$?"||l.readyState==="complete")t();else{var a=function(){t(),l.removeEventListener("DOMContentLoaded",a)};l.addEventListener("DOMContentLoaded",a),e._reactRetry=a}}function jt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var nu=null;function Fd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var l=e.data;if(l==="$"||l==="$!"||l==="$?"){if(t===0)return e;t--}else l==="/$"&&t++}e=e.previousSibling}return null}function Id(e,t,l){switch(t=Bs(l),e){case"html":if(e=t.documentElement,!e)throw Error(o(452));return e;case"head":if(e=t.head,!e)throw Error(o(453));return e;case"body":if(e=t.body,!e)throw Error(o(454));return e;default:throw Error(o(451))}}function bn(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);ic(e)}var yt=new Map,Pd=new Set;function Rs(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var Lt=z.d;z.d={f:Qm,r:Xm,D:Lm,C:Vm,L:Zm,m:Km,X:$m,S:Jm,M:Wm};function Qm(){var e=Lt.f(),t=Ts();return e||t}function Xm(e){var t=Xl(e);t!==null&&t.tag===5&&t.type==="form"?po(t):Lt.r(e)}var Sa=typeof document>"u"?null:document;function ef(e,t,l){var a=Sa;if(a&&typeof t=="string"&&t){var n=dt(t);n='link[rel="'+e+'"][href="'+n+'"]',typeof l=="string"&&(n+='[crossorigin="'+l+'"]'),Pd.has(n)||(Pd.add(n),e={rel:e,crossOrigin:l,href:t},a.querySelector(n)===null&&(t=a.createElement("link"),Le(t,"link",e),Re(t),a.head.appendChild(t)))}}function Lm(e){Lt.D(e),ef("dns-prefetch",e,null)}function Vm(e,t){Lt.C(e,t),ef("preconnect",e,t)}function Zm(e,t,l){Lt.L(e,t,l);var a=Sa;if(a&&e&&t){var n='link[rel="preload"][as="'+dt(t)+'"]';t==="image"&&l&&l.imageSrcSet?(n+='[imagesrcset="'+dt(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(n+='[imagesizes="'+dt(l.imageSizes)+'"]')):n+='[href="'+dt(e)+'"]';var s=n;switch(t){case"style":s=wa(e);break;case"script":s=Aa(e)}yt.has(s)||(e=T({rel:"preload",href:t==="image"&&l&&l.imageSrcSet?void 0:e,as:t},l),yt.set(s,e),a.querySelector(n)!==null||t==="style"&&a.querySelector(yn(s))||t==="script"&&a.querySelector(pn(s))||(t=a.createElement("link"),Le(t,"link",e),Re(t),a.head.appendChild(t)))}}function Km(e,t){Lt.m(e,t);var l=Sa;if(l&&e){var a=t&&typeof t.as=="string"?t.as:"script",n='link[rel="modulepreload"][as="'+dt(a)+'"][href="'+dt(e)+'"]',s=n;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":s=Aa(e)}if(!yt.has(s)&&(e=T({rel:"modulepreload",href:e},t),yt.set(s,e),l.querySelector(n)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(pn(s)))return}a=l.createElement("link"),Le(a,"link",e),Re(a),l.head.appendChild(a)}}}function Jm(e,t,l){Lt.S(e,t,l);var a=Sa;if(a&&e){var n=Ll(a).hoistableStyles,s=wa(e);t=t||"default";var i=n.get(s);if(!i){var u={loading:0,preload:null};if(i=a.querySelector(yn(s)))u.loading=5;else{e=T({rel:"stylesheet",href:e,"data-precedence":t},l),(l=yt.get(s))&&su(e,l);var d=i=a.createElement("link");Re(d),Le(d,"link",e),d._p=new Promise(function(b,N){d.onload=b,d.onerror=N}),d.addEventListener("load",function(){u.loading|=1}),d.addEventListener("error",function(){u.loading|=2}),u.loading|=4,qs(i,t,a)}i={type:"stylesheet",instance:i,count:1,state:u},n.set(s,i)}}}function $m(e,t){Lt.X(e,t);var l=Sa;if(l&&e){var a=Ll(l).hoistableScripts,n=Aa(e),s=a.get(n);s||(s=l.querySelector(pn(n)),s||(e=T({src:e,async:!0},t),(t=yt.get(n))&&cu(e,t),s=l.createElement("script"),Re(s),Le(s,"link",e),l.head.appendChild(s)),s={type:"script",instance:s,count:1,state:null},a.set(n,s))}}function Wm(e,t){Lt.M(e,t);var l=Sa;if(l&&e){var a=Ll(l).hoistableScripts,n=Aa(e),s=a.get(n);s||(s=l.querySelector(pn(n)),s||(e=T({src:e,async:!0,type:"module"},t),(t=yt.get(n))&&cu(e,t),s=l.createElement("script"),Re(s),Le(s,"link",e),l.head.appendChild(s)),s={type:"script",instance:s,count:1,state:null},a.set(n,s))}}function tf(e,t,l,a){var n=(n=Zt.current)?Rs(n):null;if(!n)throw Error(o(446));switch(e){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(t=wa(l.href),l=Ll(n).hoistableStyles,a=l.get(t),a||(a={type:"style",instance:null,count:0,state:null},l.set(t,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){e=wa(l.href);var s=Ll(n).hoistableStyles,i=s.get(e);if(i||(n=n.ownerDocument||n,i={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},s.set(e,i),(s=n.querySelector(yn(e)))&&!s._p&&(i.instance=s,i.state.loading=5),yt.has(e)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},yt.set(e,l),s||Fm(n,e,l,i.state))),t&&a===null)throw Error(o(528,""));return i}if(t&&a!==null)throw Error(o(529,""));return null;case"script":return t=l.async,l=l.src,typeof l=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Aa(l),l=Ll(n).hoistableScripts,a=l.get(t),a||(a={type:"script",instance:null,count:0,state:null},l.set(t,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,e))}}function wa(e){return'href="'+dt(e)+'"'}function yn(e){return'link[rel="stylesheet"]['+e+"]"}function lf(e){return T({},e,{"data-precedence":e.precedence,precedence:null})}function Fm(e,t,l,a){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?a.loading=1:(t=e.createElement("link"),a.preload=t,t.addEventListener("load",function(){return a.loading|=1}),t.addEventListener("error",function(){return a.loading|=2}),Le(t,"link",l),Re(t),e.head.appendChild(t))}function Aa(e){return'[src="'+dt(e)+'"]'}function pn(e){return"script[async]"+e}function af(e,t,l){if(t.count++,t.instance===null)switch(t.type){case"style":var a=e.querySelector('style[data-href~="'+dt(l.href)+'"]');if(a)return t.instance=a,Re(a),a;var n=T({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(e.ownerDocument||e).createElement("style"),Re(a),Le(a,"style",n),qs(a,l.precedence,e),t.instance=a;case"stylesheet":n=wa(l.href);var s=e.querySelector(yn(n));if(s)return t.state.loading|=4,t.instance=s,Re(s),s;a=lf(l),(n=yt.get(n))&&su(a,n),s=(e.ownerDocument||e).createElement("link"),Re(s);var i=s;return i._p=new Promise(function(u,d){i.onload=u,i.onerror=d}),Le(s,"link",a),t.state.loading|=4,qs(s,l.precedence,e),t.instance=s;case"script":return s=Aa(l.src),(n=e.querySelector(pn(s)))?(t.instance=n,Re(n),n):(a=l,(n=yt.get(s))&&(a=T({},l),cu(a,n)),e=e.ownerDocument||e,n=e.createElement("script"),Re(n),Le(n,"link",a),e.head.appendChild(n),t.instance=n);case"void":return null;default:throw Error(o(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(a=t.instance,t.state.loading|=4,qs(a,l.precedence,e));return t.instance}function qs(e,t,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=a.length?a[a.length-1]:null,s=n,i=0;i<a.length;i++){var u=a[i];if(u.dataset.precedence===t)s=u;else if(s!==n)break}s?s.parentNode.insertBefore(e,s.nextSibling):(t=l.nodeType===9?l.head:l,t.insertBefore(e,t.firstChild))}function su(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function cu(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Hs=null;function nf(e,t,l){if(Hs===null){var a=new Map,n=Hs=new Map;n.set(l,a)}else n=Hs,a=n.get(l),a||(a=new Map,n.set(l,a));if(a.has(e))return a;for(a.set(e,null),l=l.getElementsByTagName(e),n=0;n<l.length;n++){var s=l[n];if(!(s[Oa]||s[Ve]||e==="link"&&s.getAttribute("rel")==="stylesheet")&&s.namespaceURI!=="http://www.w3.org/2000/svg"){var i=s.getAttribute(t)||"";i=e+i;var u=a.get(i);u?u.push(s):a.set(i,[s])}}return a}function sf(e,t,l){e=e.ownerDocument||e,e.head.insertBefore(l,t==="title"?e.querySelector("head > title"):null)}function Im(e,t,l){if(l===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function cf(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var vn=null;function Pm(){}function eh(e,t,l){if(vn===null)throw Error(o(475));var a=vn;if(t.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var n=wa(l.href),s=e.querySelector(yn(n));if(s){e=s._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(a.count++,a=_s.bind(a),e.then(a,a)),t.state.loading|=4,t.instance=s,Re(s);return}s=e.ownerDocument||e,l=lf(l),(n=yt.get(n))&&su(l,n),s=s.createElement("link"),Re(s);var i=s;i._p=new Promise(function(u,d){i.onload=u,i.onerror=d}),Le(s,"link",l),t.instance=s}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(a.count++,t=_s.bind(a),e.addEventListener("load",t),e.addEventListener("error",t))}}function th(){if(vn===null)throw Error(o(475));var e=vn;return e.stylesheets&&e.count===0&&iu(e,e.stylesheets),0<e.count?function(t){var l=setTimeout(function(){if(e.stylesheets&&iu(e,e.stylesheets),e.unsuspend){var a=e.unsuspend;e.unsuspend=null,a()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(l)}}:null}function _s(){if(this.count--,this.count===0){if(this.stylesheets)iu(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Gs=null;function iu(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Gs=new Map,t.forEach(lh,e),Gs=null,_s.call(e))}function lh(e,t){if(!(t.state.loading&4)){var l=Gs.get(e);if(l)var a=l.get(null);else{l=new Map,Gs.set(e,l);for(var n=e.querySelectorAll("link[data-precedence],style[data-precedence]"),s=0;s<n.length;s++){var i=n[s];(i.nodeName==="LINK"||i.getAttribute("media")!=="not all")&&(l.set(i.dataset.precedence,i),a=i)}a&&l.set(null,a)}n=t.instance,i=n.getAttribute("data-precedence"),s=l.get(i)||a,s===a&&l.set(null,n),l.set(i,n),this.count++,a=_s.bind(this),n.addEventListener("load",a),n.addEventListener("error",a),s?s.parentNode.insertBefore(n,s.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(n,e.firstChild)),t.state.loading|=4}}var jn={$$typeof:F,Provider:null,Consumer:null,_currentValue:E,_currentValue2:E,_threadCount:0};function ah(e,t,l,a,n,s,i,u){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=ac(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ac(0),this.hiddenUpdates=ac(null),this.identifierPrefix=a,this.onUncaughtError=n,this.onCaughtError=s,this.onRecoverableError=i,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=u,this.incompleteTransitions=new Map}function uf(e,t,l,a,n,s,i,u,d,b,N,A){return e=new ah(e,t,l,i,u,d,b,A),t=1,s===!0&&(t|=24),s=nt(3,null,null,t),e.current=s,s.stateNode=e,t=Qc(),t.refCount++,e.pooledCache=t,t.refCount++,s.memoizedState={element:a,isDehydrated:l,cache:t},Zc(s),e}function rf(e){return e?(e=la,e):la}function of(e,t,l,a,n,s){n=rf(n),a.context===null?a.context=n:a.pendingContext=n,a=It(t),a.payload={element:l},s=s===void 0?null:s,s!==null&&(a.callback=s),l=Pt(e,a,t),l!==null&&(rt(l,e,t),Wa(l,e,t))}function df(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var l=e.retryLane;e.retryLane=l!==0&&l<t?l:t}}function uu(e,t){df(e,t),(e=e.alternate)&&df(e,t)}function ff(e){if(e.tag===13){var t=ta(e,67108864);t!==null&&rt(t,e,67108864),uu(e,67108864)}}var Ys=!0;function nh(e,t,l,a){var n=f.T;f.T=null;var s=z.p;try{z.p=2,ru(e,t,l,a)}finally{z.p=s,f.T=n}}function sh(e,t,l,a){var n=f.T;f.T=null;var s=z.p;try{z.p=8,ru(e,t,l,a)}finally{z.p=s,f.T=n}}function ru(e,t,l,a){if(Ys){var n=ou(a);if(n===null)Wi(e,t,a,Qs,l),hf(e,a);else if(ih(n,e,t,l,a))a.stopPropagation();else if(hf(e,a),t&4&&-1<ch.indexOf(e)){for(;n!==null;){var s=Xl(n);if(s!==null)switch(s.tag){case 3:if(s=s.stateNode,s.current.memoizedState.isDehydrated){var i=vl(s.pendingLanes);if(i!==0){var u=s;for(u.pendingLanes|=2,u.entangledLanes|=2;i;){var d=1<<31-lt(i);u.entanglements[1]|=d,i&=~d}kt(s),(fe&6)===0&&(As=St()+500,hn(0))}}break;case 13:u=ta(s,2),u!==null&&rt(u,s,2),Ts(),uu(s,2)}if(s=ou(a),s===null&&Wi(e,t,a,Qs,l),s===n)break;n=s}n!==null&&a.stopPropagation()}else Wi(e,t,a,null,l)}}function ou(e){return e=gc(e),du(e)}var Qs=null;function du(e){if(Qs=null,e=Ql(e),e!==null){var t=v(e);if(t===null)e=null;else{var l=t.tag;if(l===13){if(e=O(t),e!==null)return e;e=null}else if(l===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Qs=e,null}function mf(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Vf()){case Au:return 2;case zu:return 8;case Cn:case Zf:return 32;case Tu:return 268435456;default:return 32}default:return 32}}var fu=!1,fl=null,ml=null,hl=null,Nn=new Map,Sn=new Map,gl=[],ch="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function hf(e,t){switch(e){case"focusin":case"focusout":fl=null;break;case"dragenter":case"dragleave":ml=null;break;case"mouseover":case"mouseout":hl=null;break;case"pointerover":case"pointerout":Nn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Sn.delete(t.pointerId)}}function wn(e,t,l,a,n,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:l,eventSystemFlags:a,nativeEvent:s,targetContainers:[n]},t!==null&&(t=Xl(t),t!==null&&ff(t)),e):(e.eventSystemFlags|=a,t=e.targetContainers,n!==null&&t.indexOf(n)===-1&&t.push(n),e)}function ih(e,t,l,a,n){switch(t){case"focusin":return fl=wn(fl,e,t,l,a,n),!0;case"dragenter":return ml=wn(ml,e,t,l,a,n),!0;case"mouseover":return hl=wn(hl,e,t,l,a,n),!0;case"pointerover":var s=n.pointerId;return Nn.set(s,wn(Nn.get(s)||null,e,t,l,a,n)),!0;case"gotpointercapture":return s=n.pointerId,Sn.set(s,wn(Sn.get(s)||null,e,t,l,a,n)),!0}return!1}function gf(e){var t=Ql(e.target);if(t!==null){var l=v(t);if(l!==null){if(t=l.tag,t===13){if(t=O(l),t!==null){e.blockedOn=t,e0(e.priority,function(){if(l.tag===13){var a=ut();a=nc(a);var n=ta(l,a);n!==null&&rt(n,l,a),uu(l,a)}});return}}else if(t===3&&l.stateNode.current.memoizedState.isDehydrated){e.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Xs(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var l=ou(e.nativeEvent);if(l===null){l=e.nativeEvent;var a=new l.constructor(l.type,l);hc=a,l.target.dispatchEvent(a),hc=null}else return t=Xl(l),t!==null&&ff(t),e.blockedOn=l,!1;t.shift()}return!0}function xf(e,t,l){Xs(e)&&l.delete(t)}function uh(){fu=!1,fl!==null&&Xs(fl)&&(fl=null),ml!==null&&Xs(ml)&&(ml=null),hl!==null&&Xs(hl)&&(hl=null),Nn.forEach(xf),Sn.forEach(xf)}function Ls(e,t){e.blockedOn===t&&(e.blockedOn=null,fu||(fu=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,uh)))}var Vs=null;function bf(e){Vs!==e&&(Vs=e,r.unstable_scheduleCallback(r.unstable_NormalPriority,function(){Vs===e&&(Vs=null);for(var t=0;t<e.length;t+=3){var l=e[t],a=e[t+1],n=e[t+2];if(typeof a!="function"){if(du(a||l)===null)continue;break}var s=Xl(l);s!==null&&(e.splice(t,3),t-=3,di(s,{pending:!0,data:n,method:l.method,action:a},a,n))}}))}function An(e){function t(d){return Ls(d,e)}fl!==null&&Ls(fl,e),ml!==null&&Ls(ml,e),hl!==null&&Ls(hl,e),Nn.forEach(t),Sn.forEach(t);for(var l=0;l<gl.length;l++){var a=gl[l];a.blockedOn===e&&(a.blockedOn=null)}for(;0<gl.length&&(l=gl[0],l.blockedOn===null);)gf(l),l.blockedOn===null&&gl.shift();if(l=(e.ownerDocument||e).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var n=l[a],s=l[a+1],i=n[$e]||null;if(typeof s=="function")i||bf(l);else if(i){var u=null;if(s&&s.hasAttribute("formAction")){if(n=s,i=s[$e]||null)u=i.formAction;else if(du(n)!==null)continue}else u=i.action;typeof u=="function"?l[a+1]=u:(l.splice(a,3),a-=3),bf(l)}}}function mu(e){this._internalRoot=e}Zs.prototype.render=mu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(o(409));var l=t.current,a=ut();of(l,a,e,t,null,null)},Zs.prototype.unmount=mu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;of(e.current,2,null,e,null,null),Ts(),t[Yl]=null}};function Zs(e){this._internalRoot=e}Zs.prototype.unstable_scheduleHydration=function(e){if(e){var t=Eu();e={blockedOn:null,target:e,priority:t};for(var l=0;l<gl.length&&t!==0&&t<gl[l].priority;l++);gl.splice(l,0,e),l===0&&gf(e)}};var yf=j.version;if(yf!=="19.1.0")throw Error(o(527,yf,"19.1.0"));z.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(o(188)):(e=Object.keys(e).join(","),Error(o(268,e)));return e=R(t),e=e!==null?G(e):null,e=e===null?null:e.stateNode,e};var rh={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:f,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ks=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ks.isDisabled&&Ks.supportsFiber)try{ka=Ks.inject(rh),tt=Ks}catch{}}return zn.createRoot=function(e,t){if(!w(e))throw Error(o(299));var l=!1,a="",n=Uo,s=Co,i=Bo,u=null;return t!=null&&(t.unstable_strictMode===!0&&(l=!0),t.identifierPrefix!==void 0&&(a=t.identifierPrefix),t.onUncaughtError!==void 0&&(n=t.onUncaughtError),t.onCaughtError!==void 0&&(s=t.onCaughtError),t.onRecoverableError!==void 0&&(i=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(u=t.unstable_transitionCallbacks)),t=uf(e,1,!1,null,null,l,a,n,s,i,u,null),e[Yl]=t.current,$i(e),new mu(t)},zn.hydrateRoot=function(e,t,l){if(!w(e))throw Error(o(299));var a=!1,n="",s=Uo,i=Co,u=Bo,d=null,b=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(n=l.identifierPrefix),l.onUncaughtError!==void 0&&(s=l.onUncaughtError),l.onCaughtError!==void 0&&(i=l.onCaughtError),l.onRecoverableError!==void 0&&(u=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(d=l.unstable_transitionCallbacks),l.formState!==void 0&&(b=l.formState)),t=uf(e,1,!0,t,l??null,a,n,s,i,u,d,b),t.context=rf(null),l=t.current,a=ut(),a=nc(a),n=It(a),n.callback=null,Pt(l,n,a),l=a,t.current.lanes=l,Ma(t,l),kt(t),e[Yl]=t.current,$i(e),new Zs(t)},zn.version="19.1.0",zn}var zf;function qh(){if(zf)return hu.exports;zf=1;function r(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(r)}catch(j){console.error(j)}}return r(),hu.exports=Rh(),hu.exports}var Hh=qh();const _l=Uh((r,j)=>({theme:"light",systemInfo:null,appVersion:"1.0.0",isOperationRunning:!1,currentOperation:null,operationProgress:0,operationLogs:[],autoCleanEnabled:!1,autoCleanInterval:24,showNotifications:!0,minimizeToTray:!0,activeTab:"dashboard",sidebarCollapsed:!1,setTheme:g=>{r({theme:g}),typeof window<"u"&&window.themeAPI&&window.themeAPI.setTheme(g)},toggleTheme:()=>{const o=j().theme==="light"?"dark":"light";j().setTheme(o)},setSystemInfo:g=>r({systemInfo:g}),setAppVersion:g=>r({appVersion:g}),startOperation:g=>r({isOperationRunning:!0,currentOperation:g,operationProgress:0}),finishOperation:(g,o,w)=>{const{currentOperation:v}=j();r({isOperationRunning:!1,currentOperation:null,operationProgress:100}),v&&j().addOperationLog({operation:v,status:g?"success":"error",message:o,details:w})},updateOperationProgress:g=>r({operationProgress:g}),addOperationLog:g=>{const o={...g,id:Date.now().toString(),timestamp:new Date};r(w=>({operationLogs:[o,...w.operationLogs].slice(0,100)}))},clearOperationLogs:()=>r({operationLogs:[]}),updateSettings:g=>r(g),setActiveTab:g=>r({activeTab:g}),toggleSidebar:()=>r(g=>({sidebarCollapsed:!g.sidebarCollapsed}))})),_h=({children:r})=>{const{theme:j,activeTab:g,sidebarCollapsed:o,setActiveTab:w,toggleSidebar:v,toggleTheme:O,setSystemInfo:D,setAppVersion:R}=_l();V.useEffect(()=>{(async()=>{if(window.electronAPI)try{const C=await window.electronAPI.getSystemInfo();D(C);const q=await window.electronAPI.getAppVersion();R(q)}catch(C){console.error("Failed to initialize app:",C)}})()},[D,R]);const G=[{id:"dashboard",label:"Dashboard",icon:fh},{id:"operations",label:"Operations",icon:Dt},{id:"backups",label:"Backups",icon:Mf},{id:"settings",label:"Settings",icon:Dn}];return c.jsxs("div",{className:`flex h-screen ${j==="dark"?"dark":""} bg-gray-50 dark:bg-slate-900`,children:[c.jsxs("div",{className:`
        bg-white dark:bg-slate-800 border-r border-gray-200 dark:border-slate-700
        transition-all duration-300 ease-in-out shadow-lg relative
        ${o?"w-16":"w-64"}
      `,children:[c.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-slate-700 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-slate-800 dark:to-slate-700",children:[!o&&c.jsxs("div",{className:"flex items-center space-x-3 animate-slide-in-left",children:[c.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg",children:c.jsx(Dt,{className:"w-6 h-6 text-white"})}),c.jsxs("div",{children:[c.jsx("span",{className:"font-bold text-lg bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"Augment VIP"}),c.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Desktop"})]})]}),c.jsx("button",{onClick:v,className:"p-2.5 rounded-xl hover:bg-white/50 dark:hover:bg-slate-600 transition-all duration-200 hover:shadow-md group",children:c.jsx(dh,{className:"w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors"})})]}),c.jsx("nav",{className:"p-4 space-y-1",children:G.map((T,C)=>{const q=T.icon,Z=g===T.id;return c.jsxs("button",{onClick:()=>w(T.id),className:`
                  w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200
                  group relative overflow-hidden
                  ${Z?"bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg transform scale-105":"text-gray-600 dark:text-gray-400 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 dark:hover:from-slate-700 dark:hover:to-slate-600 hover:text-blue-600 dark:hover:text-blue-400 hover:shadow-md"}
                `,style:{animationDelay:`${C*100}ms`},children:[c.jsx(q,{className:`w-5 h-5 flex-shrink-0 transition-transform duration-200 ${Z?"scale-110":"group-hover:scale-110"}`}),!o&&c.jsx("span",{className:"font-medium transition-all duration-200",children:T.label}),Z&&c.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-600/20 rounded-xl animate-pulse"})]},T.id)})}),c.jsx("div",{className:"absolute bottom-4 left-4 right-4",children:c.jsxs("button",{onClick:O,className:`w-full flex items-center justify-center space-x-2 px-4 py-3 rounded-xl\r
                     bg-gradient-to-r from-gray-100 to-gray-200 dark:from-dark-700 dark:to-dark-600\r
                     hover:from-blue-100 hover:to-purple-100 dark:hover:from-blue-900/30 dark:hover:to-purple-900/30\r
                     transition-all duration-200 shadow-soft hover:shadow-medium group`,children:[j==="light"?c.jsx(Of,{className:"w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors"}):c.jsx(Ef,{className:"w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-500 dark:group-hover:text-yellow-400 transition-colors"}),!o&&c.jsx("span",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors",children:j==="light"?"Dark Mode":"Light Mode"})]})})]}),c.jsxs("div",{className:"flex-1 flex flex-col bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800",children:[c.jsxs("div",{className:`bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl border-b border-gray-200/50 dark:border-slate-700/50\r
                      px-6 py-4 flex items-center justify-between shadow-lg`,children:[c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx("div",{className:"w-2 h-8 bg-gradient-to-b from-blue-500 to-purple-600 rounded-full"}),c.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 capitalize",children:g})]}),c.jsxs("div",{className:"flex items-center space-x-2",children:[c.jsx("button",{className:"p-2.5 rounded-xl hover:bg-gray-100/50 dark:hover:bg-slate-700/50 transition-all duration-200 hover:shadow-md group",children:c.jsx(mh,{className:"w-4 h-4 text-gray-600 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors"})}),c.jsx("button",{className:"p-2.5 rounded-xl hover:bg-red-100/50 dark:hover:bg-red-900/50 transition-all duration-200 hover:shadow-soft group",children:c.jsx(Nu,{className:"w-4 h-4 text-gray-600 dark:text-gray-400 group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors"})})]})]}),c.jsx("div",{className:"flex-1 overflow-auto",children:r})]})]})},Rf=({isOpen:r,onClose:j})=>{const{isOperationRunning:g,currentOperation:o,operationProgress:w,operationLogs:v}=_l(),[O,D]=V.useState([]);V.useEffect(()=>{if(window.electronAPI){const C=q=>{D(Z=>[...Z,{type:q.type,text:q.data,timestamp:new Date}].slice(-100))};return window.electronAPI.onCommandOutput(C),()=>{window.electronAPI.removeAllListeners("command-output")}}},[]),V.useEffect(()=>{r||D([])},[r]);const R=()=>{switch(o){case"clean":return"Cleaning VS Code Databases";case"modify-ids":return"Modifying Telemetry IDs";case"all":return"Running All Operations";default:return"Processing..."}},G=()=>{if(!g&&w===100){const C=v[0];if(C?.status==="success")return"bg-green-500";if(C?.status==="error")return"bg-red-500"}return"bg-blue-500"},T=()=>{if(g)return c.jsx(pu,{className:"w-6 h-6 text-blue-500 animate-spin"});if(w===100){const C=v[0];if(C?.status==="success")return c.jsx(Hl,{className:"w-6 h-6 text-green-500"});if(C?.status==="error")return c.jsx(Mn,{className:"w-6 h-6 text-red-500"})}return c.jsx(pu,{className:"w-6 h-6 text-gray-400"})};return c.jsx(hh,{open:r,onOpenChange:j,children:c.jsxs(gh,{children:[c.jsx(xh,{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50"}),c.jsxs(bh,{className:`fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 \r
                                 bg-white dark:bg-dark-800 rounded-lg shadow-xl border border-gray-200 \r
                                 dark:border-dark-700 w-full max-w-2xl max-h-[80vh] z-50`,children:[c.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-dark-700",children:[c.jsxs("div",{className:"flex items-center space-x-3",children:[T(),c.jsxs("div",{children:[c.jsx(yh,{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:R()}),c.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:g?"Operation in progress...":"Operation completed"})]})]}),c.jsx(pf,{asChild:!0,children:c.jsx("button",{className:"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-700 transition-colors",children:c.jsx(Nu,{className:"w-5 h-5 text-gray-500 dark:text-gray-400"})})})]}),c.jsxs("div",{className:"p-6 space-y-6",children:[c.jsxs("div",{className:"space-y-2",children:[c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Progress"}),c.jsxs("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:[Math.round(w),"%"]})]}),c.jsx(ph,{className:"relative overflow-hidden bg-gray-200 dark:bg-dark-700 rounded-full w-full h-2",children:c.jsx(vh,{className:`h-full transition-transform duration-300 ease-out ${G()}`,style:{transform:`translateX(-${100-w}%)`}})})]}),c.jsxs("div",{className:"space-y-2",children:[c.jsx("h3",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Console Output"}),c.jsx("div",{className:"bg-gray-900 dark:bg-black rounded-lg p-4 h-64 overflow-y-auto font-mono text-sm",children:O.length>0?O.map((C,q)=>c.jsxs("div",{className:`mb-1 ${C.type==="stderr"?"text-red-400":"text-green-400"}`,children:[c.jsx("span",{className:"text-gray-500 text-xs mr-2",children:C.timestamp.toLocaleTimeString()}),C.text]},q)):c.jsx("div",{className:"text-gray-500 italic",children:"Waiting for output..."})})]}),!g&&v.length>0&&c.jsxs("div",{className:"space-y-2",children:[c.jsx("h3",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Summary"}),c.jsxs("div",{className:`p-4 rounded-lg border ${v[0]?.status==="success"?"bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800":"bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800"}`,children:[c.jsx("p",{className:`text-sm ${v[0]?.status==="success"?"text-green-800 dark:text-green-200":"text-red-800 dark:text-red-200"}`,children:v[0]?.message}),v[0]?.details&&c.jsx("p",{className:"text-xs mt-2 text-gray-600 dark:text-gray-400",children:v[0].details})]})]})]}),c.jsx("div",{className:"flex items-center justify-end p-6 border-t border-gray-200 dark:border-dark-700 space-x-3",children:!g&&c.jsx(pf,{asChild:!0,children:c.jsx("button",{className:`px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 \r
                                 transition-colors font-medium`,children:"Close"})})})]})]})})},Gh=({isOpen:r,onClose:j})=>{const[g,o]=V.useState(null),[w,v]=V.useState(!1),[O,D]=V.useState(0),[R,G]=V.useState(""),[T,C]=V.useState(new Set),[q,Z]=V.useState(!1),[ae,M]=V.useState(0),[Y,$]=V.useState(""),[P,F]=V.useState([]);V.useEffect(()=>{if(window.electronAPI&&r)return window.electronAPI.onScanProgress(B=>{D(B.progress),G(B.step)}),window.electronAPI.onCleanProgress(B=>{M(B.progress),$(B.step),F(U=>[...U,`${B.step} (${B.progress}%)`])}),window.electronAPI.onBackupProgress(B=>{M(B.progress),$(B.step),F(U=>[...U,`${B.step} (${B.progress}%)`])}),()=>{window.electronAPI.removeAllListeners("scan-progress"),window.electronAPI.removeAllListeners("clean-progress"),window.electronAPI.removeAllListeners("backup-progress")}},[r]);const K=async()=>{if(window.electronAPI){v(!0),D(0),G("Initializing scan..."),F([]);try{const B=await window.electronAPI.scanDatabases();if(B.success&&B.data){const U={sqlite:B.data.sqlite?.map(re=>({...re,type:"sqlite"}))||[],postgresql:B.data.postgresql?.map(re=>({...re,type:"postgresql"}))||[],mysql:B.data.mysql?.map(re=>({...re,type:"mysql"}))||[],totalSize:B.data.totalSize||0,scanTime:B.data.scanTime||0};o(U),G("Scan completed successfully!"),D(100)}else throw new Error(B.error||"Scan failed")}catch(B){console.error("Database scan failed:",B),G(`Scan failed: ${B}`)}finally{v(!1)}}},ce=async()=>{if(!(!window.electronAPI||!g||T.size===0)){Z(!0),M(0),$("Starting cleanup..."),F(["Starting database cleanup operation..."]);try{const U=[...g.sqlite,...g.postgresql,...g.mysql].filter(re=>T.has(k(re)));for(let re=0;re<U.length;re++){const Ae=U[re];$(`Cleaning ${Ae.name}...`);const Ye=await window.electronAPI.cleanDatabase(Ae);Ye.success?F(f=>[...f,`✓ Successfully cleaned ${Ae.name}`]):F(f=>[...f,`✗ Failed to clean ${Ae.name}: ${Ye.error}`]),M(Math.round((re+1)/U.length*100))}$("Cleanup completed!"),await K()}catch(B){console.error("Database cleanup failed:",B),$(`Cleanup failed: ${B}`),F(U=>[...U,`✗ Cleanup failed: ${B}`])}finally{Z(!1)}}},k=B=>`${B.type}-${B.name}-${B.path||""}`,ne=B=>{if(B===0)return"0 B";const U=1024,re=["B","KB","MB","GB"],Ae=Math.floor(Math.log(B)/Math.log(U));return parseFloat((B/Math.pow(U,Ae)).toFixed(2))+" "+re[Ae]},ie=B=>{const U=k(B),re=new Set(T);re.has(U)?re.delete(U):re.add(U),C(re)},ke=()=>{if(!g)return;const U=[...g.sqlite,...g.postgresql,...g.mysql].filter(Ae=>Ae.canClean),re=new Set(U.map(k));C(re)},Ke=()=>{C(new Set)};return r?c.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 backdrop-blur-sm",children:c.jsxs("div",{className:"bg-white dark:bg-slate-800 rounded-2xl w-full max-w-4xl max-h-[90vh] mx-4 shadow-2xl border border-gray-200 dark:border-slate-700 flex flex-col",children:[c.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-slate-700",children:[c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx(Dt,{className:"w-6 h-6 text-blue-600 dark:text-blue-400"}),c.jsx("h2",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"Database Manager"})]}),c.jsx("button",{onClick:j,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors",children:c.jsx(Nu,{className:"w-5 h-5"})})]}),c.jsx("div",{className:"flex-1 overflow-hidden flex flex-col",children:g?c.jsxs("div",{className:"flex-1 flex flex-col overflow-hidden",children:[c.jsx("div",{className:"p-6 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-slate-700 dark:to-slate-600 border-b border-gray-200 dark:border-slate-600",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Scan Results"}),c.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Found"," ",g.sqlite.length+g.postgresql.length+g.mysql.length," ","databases • Total size: ",ne(g.totalSize)]})]}),c.jsxs("div",{className:"flex space-x-2",children:[c.jsx("button",{onClick:ke,className:"px-4 py-2 text-sm bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors",children:"Select All Cleanable"}),c.jsx("button",{onClick:Ke,className:"px-4 py-2 text-sm bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-slate-600 transition-colors",children:"Clear Selection"}),c.jsx("button",{onClick:K,className:"px-4 py-2 text-sm bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 rounded-lg hover:bg-green-200 dark:hover:bg-green-800 transition-colors",children:"Rescan"})]})]})}),c.jsx("div",{className:"flex-1 overflow-y-auto p-6",children:c.jsxs("div",{className:"space-y-6",children:[g.sqlite.length>0&&c.jsxs("div",{children:[c.jsxs("h4",{className:"text-md font-semibold text-gray-900 dark:text-white mb-3 flex items-center",children:[c.jsx(Uf,{className:"w-4 h-4 mr-2"}),"SQLite Databases (",g.sqlite.length,")"]}),c.jsx("div",{className:"space-y-2",children:g.sqlite.map((B,U)=>c.jsx(bu,{database:B,isSelected:T.has(k(B)),onToggle:()=>ie(B)},`sqlite-${U}`))})]}),g.postgresql.length>0&&c.jsxs("div",{children:[c.jsxs("h4",{className:"text-md font-semibold text-gray-900 dark:text-white mb-3 flex items-center",children:[c.jsx(Dt,{className:"w-4 h-4 mr-2"}),"PostgreSQL Databases (",g.postgresql.length,")"]}),c.jsx("div",{className:"space-y-2",children:g.postgresql.map((B,U)=>c.jsx(bu,{database:B,isSelected:T.has(k(B)),onToggle:()=>ie(B)},`postgresql-${U}`))})]}),g.mysql.length>0&&c.jsxs("div",{children:[c.jsxs("h4",{className:"text-md font-semibold text-gray-900 dark:text-white mb-3 flex items-center",children:[c.jsx(Dt,{className:"w-4 h-4 mr-2"}),"MySQL Databases (",g.mysql.length,")"]}),c.jsx("div",{className:"space-y-2",children:g.mysql.map((B,U)=>c.jsx(bu,{database:B,isSelected:T.has(k(B)),onToggle:()=>ie(B)},`mysql-${U}`))})]})]})}),q&&c.jsxs("div",{className:"p-4 bg-gray-50 dark:bg-slate-700 border-t border-gray-200 dark:border-slate-600",children:[c.jsxs("div",{className:"flex items-center space-x-3 mb-2",children:[c.jsx(vf,{className:"w-4 h-4 text-blue-600 animate-spin"}),c.jsx("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:Y})]}),c.jsx("div",{className:"w-full bg-gray-200 dark:bg-slate-600 rounded-full h-2 mb-2",children:c.jsx("div",{className:"h-2 bg-gradient-to-r from-red-500 to-red-600 rounded-full transition-all duration-300",style:{width:`${ae}%`}})}),P.length>0&&c.jsx("div",{className:"max-h-20 overflow-y-auto bg-gray-900 rounded p-2",children:c.jsx("div",{className:"text-xs font-mono space-y-1",children:P.slice(-5).map((B,U)=>c.jsx("div",{className:"text-green-400",children:B},U))})})]}),c.jsx("div",{className:"p-6 border-t border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-700",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:[T.size," database(s) selected"]}),c.jsx("div",{className:"flex space-x-3",children:c.jsxs("button",{onClick:ce,disabled:T.size===0||q,className:"px-6 py-2 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg hover:from-red-600 hover:to-red-700 transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center space-x-2",children:[c.jsx(Fs,{className:"w-4 h-4"}),c.jsx("span",{children:"Clean Selected"})]})})]})})]}):c.jsx("div",{className:"flex-1 flex items-center justify-center p-8",children:c.jsxs("div",{className:"text-center max-w-md",children:[c.jsx(Dt,{className:"w-16 h-16 text-blue-600 dark:text-blue-400 mx-auto mb-4"}),c.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"Scan for Databases"}),c.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"Discover SQLite, PostgreSQL, and MySQL databases on your system that can be safely cleaned."}),w?c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[c.jsx(vf,{className:"w-5 h-5 text-blue-600 animate-spin"}),c.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:R})]}),c.jsx("div",{className:"w-full bg-gray-200 dark:bg-slate-700 rounded-full h-2",children:c.jsx("div",{className:"h-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full transition-all duration-300",style:{width:`${O}%`}})}),c.jsxs("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:[O,"% complete"]})]}):c.jsx("button",{onClick:K,className:"px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:scale-105",children:"Start Database Scan"})]})})})]})}):null},bu=({database:r,isSelected:j,onToggle:g})=>{const o=w=>{if(w===0)return"0 B";const v=1024,O=["B","KB","MB","GB"],D=Math.floor(Math.log(w)/Math.log(v));return parseFloat((w/Math.pow(v,D)).toFixed(2))+" "+O[D]};return c.jsx("div",{className:`p-4 rounded-lg border transition-all duration-200 cursor-pointer ${j?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-slate-600 hover:border-gray-300 dark:hover:border-slate-500"} ${r.canClean?"":"opacity-50"}`,onClick:r.canClean?g:void 0,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx("div",{className:`w-4 h-4 rounded border-2 flex items-center justify-center ${j?"border-blue-500 bg-blue-500":"border-gray-300 dark:border-slate-500"}`,children:j&&c.jsx(Hl,{className:"w-3 h-3 text-white"})}),c.jsxs("div",{children:[c.jsx("div",{className:"font-medium text-gray-900 dark:text-white",children:r.name}),r.path&&c.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400 truncate max-w-md",children:r.path})]})]}),c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:o(r.size)}),c.jsx("div",{className:`px-2 py-1 rounded text-xs font-medium ${r.type==="sqlite"?"bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300":r.type==="postgresql"?"bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300":"bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-300"}`,children:r.type.toUpperCase()}),!r.canClean&&c.jsxs("div",{className:"flex items-center space-x-1 text-yellow-600 dark:text-yellow-400",children:[c.jsx(Cf,{className:"w-4 h-4"}),c.jsx("span",{className:"text-xs",children:"Protected"})]})]})]})})},Tf=()=>{const{systemInfo:r,appVersion:j,operationLogs:g,isOperationRunning:o,currentOperation:w,operationProgress:v,startOperation:O,finishOperation:D,updateOperationProgress:R,addOperationLog:G}=_l(),[T,C]=V.useState(null),[q,Z]=V.useState("unknown"),[ae,M]=V.useState(!1),[Y,$]=V.useState(!1);V.useEffect(()=>{r&&(async()=>{if(window.electronAPI)try{const ce=await window.electronAPI.checkVsCodeInstallation();Z(ce.found?"found":"not-found")}catch(ce){console.error("Failed to check VS Code status:",ce),Z("not-found")}})()},[r]);const P=async K=>{if(!o)try{O(K),M(!0);let ce=0;const k=setInterval(()=>{ce+=Math.random()*15,ce>85&&(ce=85),R(ce)},800),ne=await window.electronAPI.executePythonCommand(K);clearInterval(k),R(100),setTimeout(()=>{D(ne.success,ne.success?`${K} completed successfully`:`${K} failed`,ne.stderr||ne.stdout),ne.success&&(K==="clean"||K==="all")&&C(new Date)},500)}catch(ce){D(!1,`Failed to execute ${K}`,ce instanceof Error?ce.message:"Unknown error")}},F=g.slice(0,5);return c.jsxs("div",{className:"p-6 space-y-8",children:[c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[c.jsx("div",{className:"bg-white dark:bg-slate-800 rounded-2xl p-6 border border-gray-200 dark:border-slate-700 shadow-lg hover:shadow-xl transition-all duration-300 group",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex-1",children:[c.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 mb-1",children:"VS Code Status"}),c.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:q==="found"?"Found":q==="not-found"?"Not Found":"Checking..."})]}),c.jsx("div",{className:`p-4 rounded-2xl shadow-md group-hover:shadow-lg transition-all duration-300 ${q==="found"?"bg-gradient-to-r from-green-500 to-emerald-500":q==="not-found"?"bg-gradient-to-r from-red-500 to-rose-500":"bg-gray-100 dark:bg-gray-700"}`,children:q==="found"?c.jsx(Hl,{className:"w-7 h-7 text-white"}):q==="not-found"?c.jsx(Mn,{className:"w-7 h-7 text-white"}):c.jsx(jf,{className:"w-7 h-7 text-gray-600 dark:text-gray-400 animate-spin"})})]})}),c.jsx("div",{className:"bg-white dark:bg-slate-800 rounded-2xl p-6 border border-gray-200 dark:border-slate-700 shadow-lg hover:shadow-xl transition-all duration-300 group",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex-1",children:[c.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 mb-1",children:"Last Clean"}),c.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:T?T.toLocaleDateString():"Never"})]}),c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-r from-blue-500 to-purple-500 shadow-md group-hover:shadow-lg transition-all duration-300",children:c.jsx(Su,{className:"w-7 h-7 text-white"})})]})}),c.jsx("div",{className:"bg-white dark:bg-dark-800 rounded-lg p-6 border border-gray-200 dark:border-dark-700",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Total Operations"}),c.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:g.length})]}),c.jsx("div",{className:"p-3 rounded-full bg-purple-100 dark:bg-purple-900",children:c.jsx(Dt,{className:"w-6 h-6 text-purple-600 dark:text-purple-400"})})]})}),c.jsx("div",{className:"bg-white dark:bg-dark-800 rounded-lg p-6 border border-gray-200 dark:border-dark-700",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Platform"}),c.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 capitalize",children:r?.platform||"Unknown"})]}),c.jsx("div",{className:"p-3 rounded-full bg-gray-100 dark:bg-gray-700",children:c.jsx(jh,{className:"w-6 h-6 text-gray-600 dark:text-gray-400"})})]})})]}),c.jsxs("div",{className:"bg-white dark:bg-slate-800 rounded-2xl p-8 border border-gray-200 dark:border-slate-700 shadow-lg",children:[c.jsxs("div",{className:"flex items-center space-x-3 mb-6",children:[c.jsx("div",{className:"w-3 h-8 bg-gradient-to-b from-blue-500 to-purple-600 rounded-full"}),c.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:"Quick Actions"})]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[c.jsxs("button",{onClick:()=>$(!0),disabled:o,className:`group relative overflow-hidden flex flex-col items-center justify-center space-y-2 p-6\r
                     bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700\r
                     rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300\r
                     disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105`,children:[c.jsx(Uf,{className:"w-8 h-8 text-white group-hover:scale-110 transition-transform duration-200"}),c.jsxs("div",{className:"text-center",children:[c.jsx("div",{className:"font-semibold text-white",children:"Database Manager"}),c.jsx("div",{className:"text-xs text-blue-100",children:"Scan & Clean Databases"})]}),c.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]}),c.jsxs("button",{onClick:()=>P("clean"),disabled:o,className:`group relative overflow-hidden flex flex-col items-center justify-center space-y-2 p-6\r
                     bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700\r
                     rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300\r
                     disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105`,children:[c.jsx(Fs,{className:"w-8 h-8 text-white group-hover:scale-110 transition-transform duration-200"}),c.jsxs("div",{className:"text-center",children:[c.jsx("div",{className:"font-semibold text-white",children:"Quick Clean"}),c.jsx("div",{className:"text-xs text-green-100",children:"VS Code Cleanup"})]}),c.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]}),c.jsxs("button",{onClick:()=>P("modify-ids"),disabled:o,className:`group relative overflow-hidden flex flex-col items-center justify-center space-y-2 p-6\r
                     bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700\r
                     rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300\r
                     disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105`,children:[c.jsx(Is,{className:"w-8 h-8 text-white group-hover:scale-110 transition-transform duration-200"}),c.jsxs("div",{className:"text-center",children:[c.jsx("div",{className:"font-semibold text-white",children:"Modify IDs"}),c.jsx("div",{className:"text-xs text-purple-100",children:"Privacy Protection"})]}),c.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]}),c.jsxs("button",{onClick:()=>P("all"),disabled:o,className:`group relative overflow-hidden flex flex-col items-center justify-center space-y-2 p-6\r
                     bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700\r
                     rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300\r
                     disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105`,children:[c.jsx(Ws,{className:"w-8 h-8 text-white group-hover:scale-110 transition-transform duration-200"}),c.jsxs("div",{className:"text-center",children:[c.jsx("div",{className:"font-semibold text-white",children:"Run All"}),c.jsx("div",{className:"text-xs text-orange-100",children:"Complete Cleanup"})]}),c.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]})]})]}),c.jsxs("div",{className:"bg-white dark:bg-dark-800 rounded-lg p-6 border border-gray-200 dark:border-dark-700",children:[c.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4",children:"Recent Activity"}),F.length>0?c.jsx("div",{className:"space-y-3",children:F.map(K=>c.jsxs("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-dark-700 rounded-lg",children:[c.jsx("div",{className:`p-2 rounded-full ${K.status==="success"?"bg-green-100 dark:bg-green-900":K.status==="error"?"bg-red-100 dark:bg-red-900":"bg-yellow-100 dark:bg-yellow-900"}`,children:K.status==="success"?c.jsx(Hl,{className:"w-4 h-4 text-green-600 dark:text-green-400"}):K.status==="error"?c.jsx(Mn,{className:"w-4 h-4 text-red-600 dark:text-red-400"}):c.jsx(jf,{className:"w-4 h-4 text-yellow-600 dark:text-yellow-400"})}),c.jsxs("div",{className:"flex-1",children:[c.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:K.message}),c.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:[K.timestamp.toLocaleString()," • ",K.operation]})]})]},K.id))}):c.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-center py-8",children:"No recent activity. Run an operation to get started."})]}),c.jsx(Rf,{isOpen:ae,onClose:()=>M(!1)}),c.jsx(Gh,{isOpen:Y,onClose:()=>$(!1)})]})},Yh=()=>{const{isOperationRunning:r,operationLogs:j,startOperation:g,finishOperation:o,updateOperationProgress:w,clearOperationLogs:v}=_l(),[O,D]=V.useState(!1),[R,G]=V.useState("clean"),[T,C]=V.useState([]);V.useEffect(()=>{(async()=>{if(window.electronAPI)try{const Y=await window.electronAPI.findDatabaseFiles();C(Y)}catch(Y){console.error("Failed to load database files:",Y)}})()},[]);const q=async M=>{if(!r)try{g(M),D(!0);let Y=0;const $=setInterval(()=>{Y+=Math.random()*15,Y>85&&(Y=85),w(Y)},800),P=await window.electronAPI.executePythonCommand(M);clearInterval($),w(100),setTimeout(()=>{o(P.success,P.success?`${M} completed successfully`:`${M} failed`,P.stderr||P.stdout)},500)}catch(Y){o(!1,`Failed to execute ${M}`,Y instanceof Error?Y.message:"Unknown error")}},Z=M=>{if(M===0)return"0 Bytes";const Y=1024,$=["Bytes","KB","MB","GB"],P=Math.floor(Math.log(M)/Math.log(Y));return parseFloat((M/Math.pow(Y,P)).toFixed(2))+" "+$[P]},ae=[{id:"clean",title:"Clean Databases",description:"Remove Augment-related entries from VS Code databases",icon:Dt,color:"blue",details:'This operation will scan VS Code database files and remove any entries containing "augment". Backups will be created automatically.'},{id:"modify-ids",title:"Modify Telemetry IDs",description:"Generate new random telemetry IDs for enhanced privacy",icon:Is,color:"green",details:"This will generate new random machineId and devDeviceId values in VS Code's storage.json file."},{id:"all",title:"Run All Operations",description:"Execute both database cleaning and ID modification",icon:Ws,color:"purple",details:"This will run both the database cleaning and telemetry ID modification operations in sequence."}];return c.jsxs("div",{className:"p-6 space-y-6",children:[c.jsxs("div",{className:"bg-white dark:bg-dark-800 rounded-lg p-6 border border-gray-200 dark:border-dark-700",children:[c.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4",children:"Available Operations"}),c.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:ae.map(M=>{const Y=M.icon,$=R===M.id;return c.jsxs("div",{className:`p-4 rounded-lg border-2 cursor-pointer transition-all ${$?`border-${M.color}-500 bg-${M.color}-50 dark:bg-${M.color}-900/20`:"border-gray-200 dark:border-dark-700 hover:border-gray-300 dark:hover:border-dark-600"}`,onClick:()=>G(M.id),children:[c.jsxs("div",{className:"flex items-center space-x-3 mb-3",children:[c.jsx("div",{className:`p-2 rounded-lg bg-${M.color}-100 dark:bg-${M.color}-900`,children:c.jsx(Y,{className:`w-5 h-5 text-${M.color}-600 dark:text-${M.color}-400`})}),c.jsx("h3",{className:"font-medium text-gray-900 dark:text-gray-100",children:M.title})]}),c.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-3",children:M.description}),c.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-500",children:M.details})]},M.id)})}),c.jsxs("div",{className:"mt-6 flex items-center justify-between",children:[c.jsxs("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Selected: ",c.jsx("span",{className:"font-medium",children:ae.find(M=>M.id===R)?.title})]}),c.jsxs("button",{onClick:()=>q(R),disabled:r,className:`px-6 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 \r
                     transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed\r
                     flex items-center space-x-2`,children:[c.jsx(Ws,{className:"w-4 h-4"}),c.jsx("span",{children:"Run Operation"})]})]})]}),c.jsxs("div",{className:"bg-white dark:bg-dark-800 rounded-lg p-6 border border-gray-200 dark:border-dark-700",children:[c.jsxs("div",{className:"flex items-center justify-between mb-4",children:[c.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:"Detected Database Files"}),c.jsx("button",{onClick:()=>window.electronAPI?.findDatabaseFiles().then(C),className:`px-3 py-1 text-sm bg-gray-100 dark:bg-dark-700 text-gray-700 dark:text-gray-300 \r
                     rounded-lg hover:bg-gray-200 dark:hover:bg-dark-600 transition-colors`,children:"Refresh"})]}),T.length>0?c.jsx("div",{className:"space-y-2",children:T.map((M,Y)=>c.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-700 rounded-lg",children:[c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx(Nh,{className:"w-4 h-4 text-gray-500 dark:text-gray-400"}),c.jsxs("div",{children:[c.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:M.name}),c.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:M.path})]})]}),c.jsxs("div",{className:"text-right",children:[c.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:Z(M.size)}),c.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-500",children:new Date(M.modified).toLocaleDateString()})]})]},Y))}):c.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-center py-8",children:"No database files detected. Make sure VS Code is installed and has been used."})]}),c.jsxs("div",{className:"bg-white dark:bg-dark-800 rounded-lg p-6 border border-gray-200 dark:border-dark-700",children:[c.jsxs("div",{className:"flex items-center justify-between mb-4",children:[c.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:"Operation History"}),j.length>0&&c.jsxs("button",{onClick:v,className:`px-3 py-1 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 \r
                       dark:hover:bg-red-900/20 rounded-lg transition-colors flex items-center space-x-1`,children:[c.jsx(Fs,{className:"w-3 h-3"}),c.jsx("span",{children:"Clear"})]})]}),j.length>0?c.jsx("div",{className:"space-y-3",children:j.slice(0,10).map(M=>c.jsxs("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-dark-700 rounded-lg",children:[c.jsx("div",{className:`p-2 rounded-full ${M.status==="success"?"bg-green-100 dark:bg-green-900":M.status==="error"?"bg-red-100 dark:bg-red-900":"bg-yellow-100 dark:bg-yellow-900"}`,children:M.status==="success"?c.jsx(Hl,{className:"w-4 h-4 text-green-600 dark:text-green-400"}):M.status==="error"?c.jsx(Mn,{className:"w-4 h-4 text-red-600 dark:text-red-400"}):c.jsx(Su,{className:"w-4 h-4 text-yellow-600 dark:text-yellow-400"})}),c.jsxs("div",{className:"flex-1",children:[c.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:M.message}),c.jsxs("div",{className:"flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400",children:[c.jsx("span",{children:M.timestamp.toLocaleString()}),c.jsx("span",{className:"capitalize",children:M.operation}),c.jsx("span",{className:`px-2 py-1 rounded-full ${M.status==="success"?"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200":M.status==="error"?"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200":"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200"}`,children:M.status})]})]})]},M.id))}):c.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-center py-8",children:"No operations have been performed yet."})]}),c.jsx(Rf,{isOpen:O,onClose:()=>D(!1)})]})};function qf(r){var j,g,o="";if(typeof r=="string"||typeof r=="number")o+=r;else if(typeof r=="object")if(Array.isArray(r)){var w=r.length;for(j=0;j<w;j++)r[j]&&(g=qf(r[j]))&&(o&&(o+=" "),o+=g)}else for(g in r)r[g]&&(o&&(o+=" "),o+=g);return o}function Qh(){for(var r,j,g=0,o="",w=arguments.length;g<w;g++)(r=arguments[g])&&(j=qf(r))&&(o&&(o+=" "),o+=j);return o}const wu="-",Xh=r=>{const j=Vh(r),{conflictingClassGroups:g,conflictingClassGroupModifiers:o}=r;return{getClassGroupId:O=>{const D=O.split(wu);return D[0]===""&&D.length!==1&&D.shift(),Hf(D,j)||Lh(O)},getConflictingClassGroupIds:(O,D)=>{const R=g[O]||[];return D&&o[O]?[...R,...o[O]]:R}}},Hf=(r,j)=>{if(r.length===0)return j.classGroupId;const g=r[0],o=j.nextPart.get(g),w=o?Hf(r.slice(1),o):void 0;if(w)return w;if(j.validators.length===0)return;const v=r.join(wu);return j.validators.find(({validator:O})=>O(v))?.classGroupId},kf=/^\[(.+)\]$/,Lh=r=>{if(kf.test(r)){const j=kf.exec(r)[1],g=j?.substring(0,j.indexOf(":"));if(g)return"arbitrary.."+g}},Vh=r=>{const{theme:j,prefix:g}=r,o={nextPart:new Map,validators:[]};return Kh(Object.entries(r.classGroups),g).forEach(([v,O])=>{ju(O,o,v,j)}),o},ju=(r,j,g,o)=>{r.forEach(w=>{if(typeof w=="string"){const v=w===""?j:Df(j,w);v.classGroupId=g;return}if(typeof w=="function"){if(Zh(w)){ju(w(o),j,g,o);return}j.validators.push({validator:w,classGroupId:g});return}Object.entries(w).forEach(([v,O])=>{ju(O,Df(j,v),g,o)})})},Df=(r,j)=>{let g=r;return j.split(wu).forEach(o=>{g.nextPart.has(o)||g.nextPart.set(o,{nextPart:new Map,validators:[]}),g=g.nextPart.get(o)}),g},Zh=r=>r.isThemeGetter,Kh=(r,j)=>j?r.map(([g,o])=>{const w=o.map(v=>typeof v=="string"?j+v:typeof v=="object"?Object.fromEntries(Object.entries(v).map(([O,D])=>[j+O,D])):v);return[g,w]}):r,Jh=r=>{if(r<1)return{get:()=>{},set:()=>{}};let j=0,g=new Map,o=new Map;const w=(v,O)=>{g.set(v,O),j++,j>r&&(j=0,o=g,g=new Map)};return{get(v){let O=g.get(v);if(O!==void 0)return O;if((O=o.get(v))!==void 0)return w(v,O),O},set(v,O){g.has(v)?g.set(v,O):w(v,O)}}},_f="!",$h=r=>{const{separator:j,experimentalParseClassName:g}=r,o=j.length===1,w=j[0],v=j.length,O=D=>{const R=[];let G=0,T=0,C;for(let Y=0;Y<D.length;Y++){let $=D[Y];if(G===0){if($===w&&(o||D.slice(Y,Y+v)===j)){R.push(D.slice(T,Y)),T=Y+v;continue}if($==="/"){C=Y;continue}}$==="["?G++:$==="]"&&G--}const q=R.length===0?D:D.substring(T),Z=q.startsWith(_f),ae=Z?q.substring(1):q,M=C&&C>T?C-T:void 0;return{modifiers:R,hasImportantModifier:Z,baseClassName:ae,maybePostfixModifierPosition:M}};return g?D=>g({className:D,parseClassName:O}):O},Wh=r=>{if(r.length<=1)return r;const j=[];let g=[];return r.forEach(o=>{o[0]==="["?(j.push(...g.sort(),o),g=[]):g.push(o)}),j.push(...g.sort()),j},Fh=r=>({cache:Jh(r.cacheSize),parseClassName:$h(r),...Xh(r)}),Ih=/\s+/,Ph=(r,j)=>{const{parseClassName:g,getClassGroupId:o,getConflictingClassGroupIds:w}=j,v=[],O=r.trim().split(Ih);let D="";for(let R=O.length-1;R>=0;R-=1){const G=O[R],{modifiers:T,hasImportantModifier:C,baseClassName:q,maybePostfixModifierPosition:Z}=g(G);let ae=!!Z,M=o(ae?q.substring(0,Z):q);if(!M){if(!ae){D=G+(D.length>0?" "+D:D);continue}if(M=o(q),!M){D=G+(D.length>0?" "+D:D);continue}ae=!1}const Y=Wh(T).join(":"),$=C?Y+_f:Y,P=$+M;if(v.includes(P))continue;v.push(P);const F=w(M,ae);for(let K=0;K<F.length;++K){const ce=F[K];v.push($+ce)}D=G+(D.length>0?" "+D:D)}return D};function eg(){let r=0,j,g,o="";for(;r<arguments.length;)(j=arguments[r++])&&(g=Gf(j))&&(o&&(o+=" "),o+=g);return o}const Gf=r=>{if(typeof r=="string")return r;let j,g="";for(let o=0;o<r.length;o++)r[o]&&(j=Gf(r[o]))&&(g&&(g+=" "),g+=j);return g};function tg(r,...j){let g,o,w,v=O;function O(R){const G=j.reduce((T,C)=>C(T),r());return g=Fh(G),o=g.cache.get,w=g.cache.set,v=D,D(R)}function D(R){const G=o(R);if(G)return G;const T=Ph(R,g);return w(R,T),T}return function(){return v(eg.apply(null,arguments))}}const je=r=>{const j=g=>g[r]||[];return j.isThemeGetter=!0,j},Yf=/^\[(?:([a-z-]+):)?(.+)\]$/i,lg=/^\d+\/\d+$/,ag=new Set(["px","full","screen"]),ng=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,sg=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,cg=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,ig=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,ug=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Vt=r=>za(r)||ag.has(r)||lg.test(r),bl=r=>Ta(r,"length",xg),za=r=>!!r&&!Number.isNaN(Number(r)),yu=r=>Ta(r,"number",za),Tn=r=>!!r&&Number.isInteger(Number(r)),rg=r=>r.endsWith("%")&&za(r.slice(0,-1)),J=r=>Yf.test(r),yl=r=>ng.test(r),og=new Set(["length","size","percentage"]),dg=r=>Ta(r,og,Qf),fg=r=>Ta(r,"position",Qf),mg=new Set(["image","url"]),hg=r=>Ta(r,mg,yg),gg=r=>Ta(r,"",bg),kn=()=>!0,Ta=(r,j,g)=>{const o=Yf.exec(r);return o?o[1]?typeof j=="string"?o[1]===j:j.has(o[1]):g(o[2]):!1},xg=r=>sg.test(r)&&!cg.test(r),Qf=()=>!1,bg=r=>ig.test(r),yg=r=>ug.test(r),pg=()=>{const r=je("colors"),j=je("spacing"),g=je("blur"),o=je("brightness"),w=je("borderColor"),v=je("borderRadius"),O=je("borderSpacing"),D=je("borderWidth"),R=je("contrast"),G=je("grayscale"),T=je("hueRotate"),C=je("invert"),q=je("gap"),Z=je("gradientColorStops"),ae=je("gradientColorStopPositions"),M=je("inset"),Y=je("margin"),$=je("opacity"),P=je("padding"),F=je("saturate"),K=je("scale"),ce=je("sepia"),k=je("skew"),ne=je("space"),ie=je("translate"),ke=()=>["auto","contain","none"],Ke=()=>["auto","hidden","clip","visible","scroll"],B=()=>["auto",J,j],U=()=>[J,j],re=()=>["",Vt,bl],Ae=()=>["auto",za,J],Ye=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],f=()=>["solid","dashed","dotted","double","none"],z=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],E=()=>["start","end","center","between","around","evenly","stretch"],ee=()=>["","0",J],de=()=>["auto","avoid","all","avoid-page","page","left","right","column"],ve=()=>[za,J];return{cacheSize:500,separator:":",theme:{colors:[kn],spacing:[Vt,bl],blur:["none","",yl,J],brightness:ve(),borderColor:[r],borderRadius:["none","","full",yl,J],borderSpacing:U(),borderWidth:re(),contrast:ve(),grayscale:ee(),hueRotate:ve(),invert:ee(),gap:U(),gradientColorStops:[r],gradientColorStopPositions:[rg,bl],inset:B(),margin:B(),opacity:ve(),padding:U(),saturate:ve(),scale:ve(),sepia:ee(),skew:ve(),space:U(),translate:U()},classGroups:{aspect:[{aspect:["auto","square","video",J]}],container:["container"],columns:[{columns:[yl]}],"break-after":[{"break-after":de()}],"break-before":[{"break-before":de()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...Ye(),J]}],overflow:[{overflow:Ke()}],"overflow-x":[{"overflow-x":Ke()}],"overflow-y":[{"overflow-y":Ke()}],overscroll:[{overscroll:ke()}],"overscroll-x":[{"overscroll-x":ke()}],"overscroll-y":[{"overscroll-y":ke()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[M]}],"inset-x":[{"inset-x":[M]}],"inset-y":[{"inset-y":[M]}],start:[{start:[M]}],end:[{end:[M]}],top:[{top:[M]}],right:[{right:[M]}],bottom:[{bottom:[M]}],left:[{left:[M]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Tn,J]}],basis:[{basis:B()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",J]}],grow:[{grow:ee()}],shrink:[{shrink:ee()}],order:[{order:["first","last","none",Tn,J]}],"grid-cols":[{"grid-cols":[kn]}],"col-start-end":[{col:["auto",{span:["full",Tn,J]},J]}],"col-start":[{"col-start":Ae()}],"col-end":[{"col-end":Ae()}],"grid-rows":[{"grid-rows":[kn]}],"row-start-end":[{row:["auto",{span:[Tn,J]},J]}],"row-start":[{"row-start":Ae()}],"row-end":[{"row-end":Ae()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",J]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",J]}],gap:[{gap:[q]}],"gap-x":[{"gap-x":[q]}],"gap-y":[{"gap-y":[q]}],"justify-content":[{justify:["normal",...E()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...E(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...E(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[P]}],px:[{px:[P]}],py:[{py:[P]}],ps:[{ps:[P]}],pe:[{pe:[P]}],pt:[{pt:[P]}],pr:[{pr:[P]}],pb:[{pb:[P]}],pl:[{pl:[P]}],m:[{m:[Y]}],mx:[{mx:[Y]}],my:[{my:[Y]}],ms:[{ms:[Y]}],me:[{me:[Y]}],mt:[{mt:[Y]}],mr:[{mr:[Y]}],mb:[{mb:[Y]}],ml:[{ml:[Y]}],"space-x":[{"space-x":[ne]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[ne]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",J,j]}],"min-w":[{"min-w":[J,j,"min","max","fit"]}],"max-w":[{"max-w":[J,j,"none","full","min","max","fit","prose",{screen:[yl]},yl]}],h:[{h:[J,j,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[J,j,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[J,j,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[J,j,"auto","min","max","fit"]}],"font-size":[{text:["base",yl,bl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",yu]}],"font-family":[{font:[kn]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",J]}],"line-clamp":[{"line-clamp":["none",za,yu]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Vt,J]}],"list-image":[{"list-image":["none",J]}],"list-style-type":[{list:["none","disc","decimal",J]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[r]}],"placeholder-opacity":[{"placeholder-opacity":[$]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[r]}],"text-opacity":[{"text-opacity":[$]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...f(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Vt,bl]}],"underline-offset":[{"underline-offset":["auto",Vt,J]}],"text-decoration-color":[{decoration:[r]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:U()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",J]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",J]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[$]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...Ye(),fg]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",dg]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},hg]}],"bg-color":[{bg:[r]}],"gradient-from-pos":[{from:[ae]}],"gradient-via-pos":[{via:[ae]}],"gradient-to-pos":[{to:[ae]}],"gradient-from":[{from:[Z]}],"gradient-via":[{via:[Z]}],"gradient-to":[{to:[Z]}],rounded:[{rounded:[v]}],"rounded-s":[{"rounded-s":[v]}],"rounded-e":[{"rounded-e":[v]}],"rounded-t":[{"rounded-t":[v]}],"rounded-r":[{"rounded-r":[v]}],"rounded-b":[{"rounded-b":[v]}],"rounded-l":[{"rounded-l":[v]}],"rounded-ss":[{"rounded-ss":[v]}],"rounded-se":[{"rounded-se":[v]}],"rounded-ee":[{"rounded-ee":[v]}],"rounded-es":[{"rounded-es":[v]}],"rounded-tl":[{"rounded-tl":[v]}],"rounded-tr":[{"rounded-tr":[v]}],"rounded-br":[{"rounded-br":[v]}],"rounded-bl":[{"rounded-bl":[v]}],"border-w":[{border:[D]}],"border-w-x":[{"border-x":[D]}],"border-w-y":[{"border-y":[D]}],"border-w-s":[{"border-s":[D]}],"border-w-e":[{"border-e":[D]}],"border-w-t":[{"border-t":[D]}],"border-w-r":[{"border-r":[D]}],"border-w-b":[{"border-b":[D]}],"border-w-l":[{"border-l":[D]}],"border-opacity":[{"border-opacity":[$]}],"border-style":[{border:[...f(),"hidden"]}],"divide-x":[{"divide-x":[D]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[D]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[$]}],"divide-style":[{divide:f()}],"border-color":[{border:[w]}],"border-color-x":[{"border-x":[w]}],"border-color-y":[{"border-y":[w]}],"border-color-s":[{"border-s":[w]}],"border-color-e":[{"border-e":[w]}],"border-color-t":[{"border-t":[w]}],"border-color-r":[{"border-r":[w]}],"border-color-b":[{"border-b":[w]}],"border-color-l":[{"border-l":[w]}],"divide-color":[{divide:[w]}],"outline-style":[{outline:["",...f()]}],"outline-offset":[{"outline-offset":[Vt,J]}],"outline-w":[{outline:[Vt,bl]}],"outline-color":[{outline:[r]}],"ring-w":[{ring:re()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[r]}],"ring-opacity":[{"ring-opacity":[$]}],"ring-offset-w":[{"ring-offset":[Vt,bl]}],"ring-offset-color":[{"ring-offset":[r]}],shadow:[{shadow:["","inner","none",yl,gg]}],"shadow-color":[{shadow:[kn]}],opacity:[{opacity:[$]}],"mix-blend":[{"mix-blend":[...z(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":z()}],filter:[{filter:["","none"]}],blur:[{blur:[g]}],brightness:[{brightness:[o]}],contrast:[{contrast:[R]}],"drop-shadow":[{"drop-shadow":["","none",yl,J]}],grayscale:[{grayscale:[G]}],"hue-rotate":[{"hue-rotate":[T]}],invert:[{invert:[C]}],saturate:[{saturate:[F]}],sepia:[{sepia:[ce]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[g]}],"backdrop-brightness":[{"backdrop-brightness":[o]}],"backdrop-contrast":[{"backdrop-contrast":[R]}],"backdrop-grayscale":[{"backdrop-grayscale":[G]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[T]}],"backdrop-invert":[{"backdrop-invert":[C]}],"backdrop-opacity":[{"backdrop-opacity":[$]}],"backdrop-saturate":[{"backdrop-saturate":[F]}],"backdrop-sepia":[{"backdrop-sepia":[ce]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[O]}],"border-spacing-x":[{"border-spacing-x":[O]}],"border-spacing-y":[{"border-spacing-y":[O]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",J]}],duration:[{duration:ve()}],ease:[{ease:["linear","in","out","in-out",J]}],delay:[{delay:ve()}],animate:[{animate:["none","spin","ping","pulse","bounce",J]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[K]}],"scale-x":[{"scale-x":[K]}],"scale-y":[{"scale-y":[K]}],rotate:[{rotate:[Tn,J]}],"translate-x":[{"translate-x":[ie]}],"translate-y":[{"translate-y":[ie]}],"skew-x":[{"skew-x":[k]}],"skew-y":[{"skew-y":[k]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",J]}],accent:[{accent:["auto",r]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",J]}],"caret-color":[{caret:[r]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":U()}],"scroll-mx":[{"scroll-mx":U()}],"scroll-my":[{"scroll-my":U()}],"scroll-ms":[{"scroll-ms":U()}],"scroll-me":[{"scroll-me":U()}],"scroll-mt":[{"scroll-mt":U()}],"scroll-mr":[{"scroll-mr":U()}],"scroll-mb":[{"scroll-mb":U()}],"scroll-ml":[{"scroll-ml":U()}],"scroll-p":[{"scroll-p":U()}],"scroll-px":[{"scroll-px":U()}],"scroll-py":[{"scroll-py":U()}],"scroll-ps":[{"scroll-ps":U()}],"scroll-pe":[{"scroll-pe":U()}],"scroll-pt":[{"scroll-pt":U()}],"scroll-pr":[{"scroll-pr":U()}],"scroll-pb":[{"scroll-pb":U()}],"scroll-pl":[{"scroll-pl":U()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",J]}],fill:[{fill:[r,"none"]}],"stroke-w":[{stroke:[Vt,bl,yu]}],stroke:[{stroke:[r,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},vg=tg(pg);function et(...r){return vg(Qh(r))}const Ge=V.forwardRef(({children:r,className:j,variant:g="primary",size:o="md",isLoading:w=!1,leftIcon:v,rightIcon:O,fullWidth:D=!1,disabled:R,...G},T)=>{const C={primary:"bg-brand-500 text-white hover:bg-brand-600 focus-visible:ring-brand-500/50 dark:bg-brand-600 dark:hover:bg-brand-700",secondary:"bg-neutral-100 text-neutral-900 hover:bg-neutral-200 focus-visible:ring-neutral-500/50 dark:bg-dark-700 dark:text-neutral-100 dark:hover:bg-dark-600",outline:"border border-neutral-300 bg-transparent text-neutral-900 hover:bg-neutral-100 focus-visible:ring-neutral-500/50 dark:border-dark-600 dark:text-neutral-100 dark:hover:bg-dark-800",ghost:"bg-transparent text-neutral-900 hover:bg-neutral-100 focus-visible:ring-neutral-500/50 dark:text-neutral-100 dark:hover:bg-dark-800",link:"bg-transparent text-brand-500 hover:underline focus-visible:ring-brand-500/50 p-0 h-auto dark:text-brand-400",danger:"bg-error-500 text-white hover:bg-error-600 focus-visible:ring-error-500/50 dark:bg-error-600 dark:hover:bg-error-700",warning:"bg-warning-500 text-white hover:bg-warning-600 focus-visible:ring-warning-500/50 dark:bg-warning-600 dark:hover:bg-warning-700"},q={xs:"text-xs px-2 py-1 h-6 gap-1",sm:"text-sm px-3 py-1.5 h-8 gap-1.5",md:"text-base px-4 py-2 h-10 gap-2",lg:"text-lg px-6 py-3 h-12 gap-2",xl:"text-xl px-8 py-4 h-14 gap-3"};return c.jsxs("button",{ref:T,className:et("inline-flex items-center justify-center rounded-md font-medium transition-colors","focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 dark:focus-visible:ring-offset-dark-900","disabled:opacity-50 disabled:pointer-events-none",C[g],q[o],D&&"w-full",j),disabled:w||R,...G,children:[w&&c.jsx(pu,{className:"mr-2 h-4 w-4 animate-spin","aria-hidden":"true"}),!w&&v&&c.jsx("span",{className:"inline-flex",children:v}),r,!w&&O&&c.jsx("span",{className:"inline-flex",children:O})]})});Ge.displayName="Button";const pl=V.forwardRef(({className:r,variant:j="default",interactive:g=!1,padding:o="md",...w},v)=>{const O={default:"bg-white dark:bg-dark-800 border border-neutral-200 dark:border-dark-700",outlined:"bg-transparent border-2 border-neutral-300 dark:border-dark-600",elevated:"bg-white dark:bg-dark-800 shadow-medium border-0",ghost:"bg-neutral-50 dark:bg-dark-900 border-0"},D={none:"",sm:"p-3",md:"p-4",lg:"p-6",xl:"p-8"};return c.jsx("div",{ref:v,className:et("rounded-lg transition-colors",O[j],D[o],g&&"cursor-pointer hover:shadow-soft dark:hover:bg-dark-700/50",r),...w})});pl.displayName="Card";const jg=V.forwardRef(({className:r,...j},g)=>c.jsx("div",{ref:g,className:et("flex flex-col space-y-1.5 pb-4",r),...j}));jg.displayName="CardHeader";const Ng=V.forwardRef(({className:r,...j},g)=>c.jsx("h3",{ref:g,className:et("text-lg font-semibold leading-none tracking-tight text-neutral-900 dark:text-neutral-100",r),...j}));Ng.displayName="CardTitle";const Sg=V.forwardRef(({className:r,...j},g)=>c.jsx("p",{ref:g,className:et("text-sm text-neutral-600 dark:text-neutral-400",r),...j}));Sg.displayName="CardDescription";const wg=V.forwardRef(({className:r,...j},g)=>c.jsx("div",{ref:g,className:et("",r),...j}));wg.displayName="CardContent";const Ag=V.forwardRef(({className:r,...j},g)=>c.jsx("div",{ref:g,className:et("flex items-center pt-4",r),...j}));Ag.displayName="CardFooter";const $s=V.forwardRef(({className:r,variant:j="default",size:g="md",dot:o=!1,children:w,...v},O)=>{const D={default:"bg-neutral-100 text-neutral-800 dark:bg-dark-700 dark:text-neutral-200",success:"bg-success-100 text-success-800 dark:bg-success-900/20 dark:text-success-400",warning:"bg-warning-100 text-warning-800 dark:bg-warning-900/20 dark:text-warning-400",error:"bg-error-100 text-error-800 dark:bg-error-900/20 dark:text-error-400",info:"bg-brand-100 text-brand-800 dark:bg-brand-900/20 dark:text-brand-400",outline:"border border-neutral-300 text-neutral-700 dark:border-dark-600 dark:text-neutral-300"},R={sm:"px-2 py-0.5 text-xs",md:"px-2.5 py-1 text-sm",lg:"px-3 py-1.5 text-base"},G={default:"bg-neutral-400 dark:bg-neutral-500",success:"bg-success-500",warning:"bg-warning-500",error:"bg-error-500",info:"bg-brand-500",outline:"bg-neutral-400 dark:bg-neutral-500"};return c.jsxs("div",{ref:O,className:et("inline-flex items-center rounded-full font-medium transition-colors",D[j],R[g],r),...v,children:[o&&c.jsx("div",{className:et("mr-1.5 h-2 w-2 rounded-full",G[j]),"aria-hidden":"true"}),w]})});$s.displayName="Badge";const zg=V.forwardRef(({className:r,value:j,max:g=100,variant:o="default",size:w="md",showLabel:v=!1,label:O,animated:D=!0,indeterminate:R=!1,...G},T)=>{const C=Math.min(Math.max(j/g*100,0),100),q={default:"bg-brand-500 dark:bg-brand-600",success:"bg-success-500 dark:bg-success-600",warning:"bg-warning-500 dark:bg-warning-600",error:"bg-error-500 dark:bg-error-600"},Z={sm:"h-1",md:"h-2",lg:"h-3"};return c.jsxs("div",{ref:T,className:et("w-full",r),...G,children:[(v||O)&&c.jsxs("div",{className:"mb-2 flex justify-between text-sm",children:[c.jsx("span",{className:"text-neutral-700 dark:text-neutral-300",children:O||"Progress"}),v&&c.jsxs("span",{className:"text-neutral-500 dark:text-neutral-400",children:[Math.round(C),"%"]})]}),c.jsx("div",{className:et("w-full overflow-hidden rounded-full bg-neutral-200 dark:bg-dark-700",Z[w]),role:"progressbar","aria-valuenow":j,"aria-valuemax":g,"aria-valuemin":0,children:c.jsx("div",{className:et("h-full rounded-full transition-all duration-300 ease-out",q[o],D&&"transition-transform",R&&"animate-pulse"),style:{width:R?"100%":`${C}%`,transform:R?"translateX(-100%)":"none",animation:R?"shimmer 2s linear infinite":void 0}})})]})});zg.displayName="Progress";const Tg=V.forwardRef(({className:r,value:j,max:g=100,variant:o="default",size:w=40,strokeWidth:v=4,showLabel:O=!1,...D},R)=>{const G=Math.min(Math.max(j/g*100,0),100),T=(w-v)/2,C=T*2*Math.PI,q=C,Z=C-G/100*C,ae={default:"stroke-brand-500",success:"stroke-success-500",warning:"stroke-warning-500",error:"stroke-error-500"};return c.jsxs("div",{ref:R,className:et("relative inline-flex items-center justify-center",r),...D,children:[c.jsxs("svg",{width:w,height:w,className:"transform -rotate-90","aria-hidden":"true",children:[c.jsx("circle",{cx:w/2,cy:w/2,r:T,stroke:"currentColor",strokeWidth:v,fill:"none",className:"text-neutral-200 dark:text-dark-700"}),c.jsx("circle",{cx:w/2,cy:w/2,r:T,strokeWidth:v,fill:"none",strokeDasharray:q,strokeDashoffset:Z,strokeLinecap:"round",className:et("transition-all duration-300 ease-out",ae[o])})]}),O&&c.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:c.jsxs("span",{className:"text-xs font-medium text-neutral-700 dark:text-neutral-300",children:[Math.round(G),"%"]})})]})});Tg.displayName="CircularProgress";const kg=()=>{const{isOperationRunning:r,currentOperation:j,operationProgress:g,startOperation:o,finishOperation:w,updateOperationProgress:v,addOperationLog:O}=_l(),[D,R]=V.useState("backups"),[G,T]=V.useState([]),[C,q]=V.useState([]),[Z,ae]=V.useState(null),[M,Y]=V.useState(!1),[$,P]=V.useState(!1),[F,K]=V.useState({defaultLocation:"/Users/<USER>/AugmentVIP/Backups",compression:!0,encryption:!1,maxBackups:10,autoCleanup:!0});V.useEffect(()=>{const f=[{id:"1",name:"System Full Backup",type:"full",size:25769803776e-1,createdAt:new Date(Date.now()-1728e5),status:"completed",location:"/Users/<USER>/AugmentVIP/Backups/system_full_20240706.avb",description:"Complete system state and application data backup",includedItems:["System Settings","Application Data","User Preferences","Database Files"]},{id:"2",name:"Database Backup",type:"incremental",size:163577856,createdAt:new Date(Date.now()-864e5),status:"completed",location:"/Users/<USER>/AugmentVIP/Backups/database_inc_20240707.avb",description:"Incremental backup of database changes",includedItems:["SQLite Databases","Configuration Files"]},{id:"3",name:"Quick Backup",type:"differential",size:47185920,createdAt:new Date(Date.now()-72e5),status:"completed",location:"/Users/<USER>/AugmentVIP/Backups/quick_diff_20240708.avb",description:"Quick differential backup of recent changes",includedItems:["Recent Changes","Temporary Files"]}],z=[{id:"1",name:"Daily System Backup",frequency:"daily",time:"02:00",enabled:!0,lastRun:new Date(Date.now()-1*24*60*60*1e3),nextRun:new Date(Date.now()+6*60*60*1e3),backupType:"incremental",includedPaths:["/System","/Applications/AugmentVIP"]},{id:"2",name:"Weekly Full Backup",frequency:"weekly",time:"01:00",enabled:!0,lastRun:new Date(Date.now()-7*24*60*60*1e3),nextRun:new Date(Date.now()+6*24*60*60*1e3),backupType:"full",includedPaths:["/System","/Applications","/Users/<USER>/AugmentVIP"]}];T(f),q(z)},[]);const ce=f=>{const z=["Bytes","KB","MB","GB","TB"];if(f===0)return"0 Bytes";const E=Math.floor(Math.log(f)/Math.log(1024));return Math.round(f/Math.pow(1024,E)*100)/100+" "+z[E]},k=f=>{switch(f){case"completed":return c.jsx(Hl,{className:"w-4 h-4 text-green-500"});case"failed":return c.jsx(Mn,{className:"w-4 h-4 text-red-500"});case"in-progress":return c.jsx(Ah,{className:"w-4 h-4 text-blue-500 animate-spin"});default:return null}},ne=f=>{switch(f){case"full":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"incremental":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"differential":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}},ie=async f=>{if(r)return;const z=`${f.charAt(0).toUpperCase()+f.slice(1)} Backup`;o(z);try{for(let ee=0;ee<=100;ee+=10)await new Promise(de=>setTimeout(de,300)),v(ee);const E={id:Date.now().toString(),name:`${z} - ${new Date().toLocaleDateString()}`,type:f==="quick"?"differential":f,size:Math.random()*1024*1024*1024,createdAt:new Date,status:"completed",location:`${F.defaultLocation}/${f}_${Date.now()}.avb`,description:`${z} created on ${new Date().toLocaleString()}`,includedItems:f==="full"?["System Settings","Application Data","User Preferences","Database Files"]:["Recent Changes","Modified Files"]};T(ee=>[E,...ee]),w(!0,`${z} completed successfully`,`Backup saved to ${E.location}`),O({operation:"backup",status:"success",message:`${z} completed`,details:`Size: ${ce(E.size)}`})}catch(E){w(!1,`${z} failed`,E instanceof Error?E.message:"Unknown error")}},ke=async f=>{if(!r){o("Restore Backup");try{for(let z=0;z<=100;z+=15)await new Promise(E=>setTimeout(E,400)),v(z);w(!0,"Backup restored successfully",`Restored from ${f.name}`),O({operation:"restore",status:"success",message:"Backup restored",details:`Restored from: ${f.name}`})}catch(z){w(!1,"Restore failed",z instanceof Error?z.message:"Unknown error")}}},Ke=f=>{T(z=>z.filter(E=>E.id!==f)),O({operation:"delete",status:"success",message:"Backup deleted",details:`Backup ID: ${f}`})},B=f=>{q(z=>z.map(E=>E.id===f?{...E,enabled:!E.enabled}:E))},U=()=>c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[c.jsxs(Ge,{onClick:()=>ie("full"),disabled:r,className:"h-20 flex flex-col items-center justify-center space-y-2",children:[c.jsx(Dt,{className:"w-6 h-6"}),c.jsx("span",{children:"Full Backup"})]}),c.jsxs(Ge,{onClick:()=>ie("incremental"),disabled:r,variant:"outline",className:"h-20 flex flex-col items-center justify-center space-y-2",children:[c.jsx(Mf,{className:"w-6 h-6"}),c.jsx("span",{children:"Incremental"})]}),c.jsxs(Ge,{onClick:()=>ie("quick"),disabled:r,variant:"outline",className:"h-20 flex flex-col items-center justify-center space-y-2",children:[c.jsx(vu,{className:"w-6 h-6"}),c.jsx("span",{children:"Quick Backup"})]})]}),c.jsxs("div",{className:"space-y-4",children:[c.jsx("h3",{className:"text-lg font-semibold",children:"Recent Backups"}),G.map(f=>c.jsx(pl,{className:"p-4",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsxs("div",{className:"flex items-center space-x-2",children:[k(f.status),c.jsxs("div",{children:[c.jsx("h4",{className:"font-medium",children:f.name}),c.jsx("p",{className:"text-sm text-gray-500",children:f.description})]})]}),c.jsx($s,{className:ne(f.type),children:f.type})]}),c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsxs("div",{className:"text-right text-sm",children:[c.jsx("p",{className:"font-medium",children:ce(f.size)}),c.jsx("p",{className:"text-gray-500",children:f.createdAt.toLocaleDateString()})]}),c.jsxs("div",{className:"flex space-x-2",children:[c.jsx(Ge,{size:"sm",variant:"outline",onClick:()=>ke(f),disabled:r,children:c.jsx(Js,{className:"w-4 h-4"})}),c.jsx(Ge,{size:"sm",variant:"outline",onClick:()=>Ke(f.id),disabled:r,children:c.jsx(Fs,{className:"w-4 h-4"})})]})]})]})},f.id))]})]}),re=()=>c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:"flex justify-between items-center",children:[c.jsx("h3",{className:"text-lg font-semibold",children:"Backup Schedules"}),c.jsxs(Ge,{onClick:()=>P(!0),children:[c.jsx(Nf,{className:"w-4 h-4 mr-2"}),"New Schedule"]})]}),c.jsx("div",{className:"space-y-4",children:C.map(f=>c.jsx(pl,{className:"p-4",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsxs("div",{className:"flex items-center space-x-2",children:[c.jsx(Su,{className:"w-5 h-5 text-blue-500"}),c.jsxs("div",{children:[c.jsx("h4",{className:"font-medium",children:f.name}),c.jsxs("p",{className:"text-sm text-gray-500",children:[f.frequency," at ",f.time," • ",f.backupType," backup"]})]})]}),c.jsx($s,{variant:f.enabled?"success":"outline",children:f.enabled?"Active":"Disabled"})]}),c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsxs("div",{className:"text-right text-sm",children:[c.jsxs("p",{className:"font-medium",children:["Next: ",f.nextRun.toLocaleDateString()]}),c.jsxs("p",{className:"text-gray-500",children:["Last: ",f.lastRun?.toLocaleDateString()||"Never"]})]}),c.jsxs("div",{className:"flex space-x-2",children:[c.jsx(Ge,{size:"sm",variant:"outline",onClick:()=>B(f.id),children:f.enabled?c.jsx(Sh,{className:"w-4 h-4"}):c.jsx(Ws,{className:"w-4 h-4"})}),c.jsx(Ge,{size:"sm",variant:"outline",onClick:()=>{},children:c.jsx(Dn,{className:"w-4 h-4"})})]})]})]})},f.id))})]}),Ae=()=>c.jsxs("div",{className:"space-y-6",children:[c.jsx("h3",{className:"text-lg font-semibold",children:"Restore from Backup"}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[c.jsxs(pl,{className:"p-6",children:[c.jsx("h4",{className:"font-medium mb-4",children:"Select Backup to Restore"}),c.jsx("div",{className:"space-y-3",children:G.filter(f=>f.status==="completed").map(f=>c.jsx("div",{className:`p-3 border rounded-lg cursor-pointer transition-colors ${Z?.id===f.id?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 hover:border-gray-300"}`,onClick:()=>ae(f),children:c.jsxs("div",{className:"flex justify-between items-start",children:[c.jsxs("div",{children:[c.jsx("p",{className:"font-medium",children:f.name}),c.jsx("p",{className:"text-sm text-gray-500",children:f.createdAt.toLocaleDateString()})]}),c.jsx($s,{className:ne(f.type),children:f.type})]})},f.id))})]}),c.jsxs(pl,{className:"p-6",children:[c.jsx("h4",{className:"font-medium mb-4",children:"Restore Options"}),Z?c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{children:[c.jsx("h5",{className:"font-medium text-sm mb-2",children:"Selected Backup:"}),c.jsx("p",{className:"text-sm",children:Z.name}),c.jsx("p",{className:"text-xs text-gray-500",children:ce(Z.size)})]}),c.jsxs("div",{children:[c.jsx("h5",{className:"font-medium text-sm mb-2",children:"Included Items:"}),c.jsx("div",{className:"space-y-1",children:Z.includedItems.map((f,z)=>c.jsxs("div",{className:"flex items-center space-x-2 text-sm",children:[c.jsx(Hl,{className:"w-3 h-3 text-green-500"}),c.jsx("span",{children:f})]},z))})]}),c.jsxs(Ge,{onClick:()=>ke(Z),disabled:r,className:"w-full",children:[c.jsx(Js,{className:"w-4 h-4 mr-2"}),"Restore Backup"]})]}):c.jsx("p",{className:"text-gray-500 text-sm",children:"Select a backup to see restore options"})]})]})]}),Ye=()=>c.jsxs("div",{className:"space-y-6",children:[c.jsx("h3",{className:"text-lg font-semibold",children:"Backup Settings"}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[c.jsxs(pl,{className:"p-6",children:[c.jsx("h4",{className:"font-medium mb-4",children:"General Settings"}),c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{children:[c.jsx("label",{className:"block text-sm font-medium mb-2",children:"Default Backup Location"}),c.jsxs("div",{className:"flex space-x-2",children:[c.jsx("input",{type:"text",value:F.defaultLocation,onChange:f=>K(z=>({...z,defaultLocation:f.target.value})),className:"flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"}),c.jsx(Ge,{size:"sm",variant:"outline",children:c.jsx(wh,{className:"w-4 h-4"})})]})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-sm font-medium mb-2",children:"Maximum Backups to Keep"}),c.jsx("input",{type:"number",value:F.maxBackups,onChange:f=>K(z=>({...z,maxBackups:parseInt(f.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm",min:"1",max:"100"})]}),c.jsxs("div",{className:"space-y-3",children:[c.jsxs("label",{className:"flex items-center space-x-2",children:[c.jsx("input",{type:"checkbox",checked:F.compression,onChange:f=>K(z=>({...z,compression:f.target.checked})),className:"rounded"}),c.jsx("span",{className:"text-sm",children:"Enable compression"})]}),c.jsxs("label",{className:"flex items-center space-x-2",children:[c.jsx("input",{type:"checkbox",checked:F.encryption,onChange:f=>K(z=>({...z,encryption:f.target.checked})),className:"rounded"}),c.jsx("span",{className:"text-sm",children:"Enable encryption"})]}),c.jsxs("label",{className:"flex items-center space-x-2",children:[c.jsx("input",{type:"checkbox",checked:F.autoCleanup,onChange:f=>K(z=>({...z,autoCleanup:f.target.checked})),className:"rounded"}),c.jsx("span",{className:"text-sm",children:"Auto-cleanup old backups"})]})]})]})]}),c.jsxs(pl,{className:"p-6",children:[c.jsx("h4",{className:"font-medium mb-4",children:"Storage Information"}),c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{className:"flex justify-between items-center",children:[c.jsx("span",{className:"text-sm",children:"Total Backups:"}),c.jsx("span",{className:"font-medium",children:G.length})]}),c.jsxs("div",{className:"flex justify-between items-center",children:[c.jsx("span",{className:"text-sm",children:"Total Size:"}),c.jsx("span",{className:"font-medium",children:ce(G.reduce((f,z)=>f+z.size,0))})]}),c.jsxs("div",{className:"flex justify-between items-center",children:[c.jsx("span",{className:"text-sm",children:"Available Space:"}),c.jsx("span",{className:"font-medium text-green-600",children:"2.1 TB"})]}),c.jsxs("div",{className:"flex justify-between items-center",children:[c.jsx("span",{className:"text-sm",children:"Active Schedules:"}),c.jsx("span",{className:"font-medium",children:C.filter(f=>f.enabled).length})]})]}),c.jsx("div",{className:"mt-6 pt-4 border-t",children:c.jsxs(Ge,{variant:"outline",className:"w-full",children:[c.jsx(Is,{className:"w-4 h-4 mr-2"}),"Verify All Backups"]})})]})]})]});return c.jsxs("div",{className:"p-6 max-w-7xl mx-auto",children:[c.jsxs("div",{className:"mb-6",children:[c.jsx("h1",{className:"text-2xl font-bold mb-2",children:"Backup Management"}),c.jsx("p",{className:"text-gray-600",children:"Create, schedule, and manage your system backups"})]}),c.jsx("div",{className:"flex space-x-1 mb-6 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg",children:[{id:"backups",label:"Backups",icon:Bf},{id:"schedules",label:"Schedules",icon:Nf},{id:"restore",label:"Restore",icon:Js},{id:"settings",label:"Settings",icon:Dn}].map(f=>{const z=f.icon;return c.jsxs("button",{onClick:()=>R(f.id),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${D===f.id?"bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 shadow-sm":"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"}`,children:[c.jsx(z,{className:"w-4 h-4"}),c.jsx("span",{children:f.label})]},f.id)})}),D==="backups"&&U(),D==="schedules"&&re(),D==="restore"&&Ae(),D==="settings"&&Ye()]})},Dg=()=>{const{theme:r,setTheme:j,addOperationLog:g}=_l(),[o,w]=V.useState("general"),[v,O]=V.useState({theme:r,language:"en",autoStart:!0,minimizeToTray:!0,closeToTray:!1,enableNotifications:!0,soundEnabled:!0,showDesktopNotifications:!0,notificationDuration:5,autoCleanEnabled:!1,autoCleanInterval:24,confirmDangerousOperations:!0,showOperationProgress:!0,autoBackupEnabled:!0,backupLocation:"/Users/<USER>/AugmentVIP/Backups",backupRetention:30,compressBackups:!0,requirePasswordForOperations:!1,encryptBackups:!1,logOperations:!0,maxLogEntries:1e3,maxConcurrentOperations:3,enableHardwareAcceleration:!0,memoryLimit:512,debugMode:!1,telemetryEnabled:!0,autoUpdatesEnabled:!0,betaUpdates:!1}),[D,R]=V.useState(!1),G=[{id:"general",title:"General",icon:Dn,description:"Basic application settings and preferences"},{id:"notifications",title:"Notifications",icon:zh,description:"Configure alerts and notification preferences"},{id:"operations",title:"Operations",icon:vu,description:"Settings for cleanup and system operations"},{id:"backup",title:"Backup",icon:Dt,description:"Backup and restore configuration"},{id:"security",title:"Security",icon:Is,description:"Security and privacy settings"},{id:"performance",title:"Performance",icon:vu,description:"Performance and resource management"},{id:"advanced",title:"Advanced",icon:Dn,description:"Advanced settings and developer options"}];V.useEffect(()=>{v.theme!==r&&j(v.theme)},[v.theme,r,j]);const T=(k,ne)=>{O(ie=>({...ie,[k]:ne})),R(!0)},C=()=>{localStorage.setItem("augment-vip-settings",JSON.stringify(v)),R(!1),g({operation:"settings",status:"success",message:"Settings saved successfully",details:"All settings have been saved to local storage"})},q=()=>{confirm("Are you sure you want to reset all settings to defaults? This action cannot be undone.")&&(O({theme:"system",language:"en",autoStart:!0,minimizeToTray:!0,closeToTray:!1,enableNotifications:!0,soundEnabled:!0,showDesktopNotifications:!0,notificationDuration:5,autoCleanEnabled:!1,autoCleanInterval:24,confirmDangerousOperations:!0,showOperationProgress:!0,autoBackupEnabled:!0,backupLocation:"/Users/<USER>/AugmentVIP/Backups",backupRetention:30,compressBackups:!0,requirePasswordForOperations:!1,encryptBackups:!1,logOperations:!0,maxLogEntries:1e3,maxConcurrentOperations:3,enableHardwareAcceleration:!0,memoryLimit:512,debugMode:!1,telemetryEnabled:!0,autoUpdatesEnabled:!0,betaUpdates:!1}),R(!0))},Z=()=>{const k=JSON.stringify(v,null,2),ne=new Blob([k],{type:"application/json"}),ie=URL.createObjectURL(ne),ke=document.createElement("a");ke.href=ie,ke.download="augment-vip-settings.json",ke.click(),URL.revokeObjectURL(ie)},ae=k=>{const ne=k.target.files?.[0];if(ne){const ie=new FileReader;ie.onload=ke=>{try{const Ke=JSON.parse(ke.target?.result);O(Ke),R(!0),g({operation:"import",status:"success",message:"Settings imported successfully",details:`Imported from ${ne.name}`})}catch{alert("Failed to import settings. Please check the file format.")}},ie.readAsText(ne)}},M=()=>c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{children:[c.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Notification Preferences"}),c.jsxs("div",{className:"space-y-3",children:[c.jsxs("label",{className:"flex items-center space-x-3",children:[c.jsx("input",{type:"checkbox",checked:v.enableNotifications,onChange:k=>T("enableNotifications",k.target.checked),className:"rounded"}),c.jsxs("div",{children:[c.jsx("span",{className:"text-sm font-medium",children:"Enable notifications"}),c.jsx("p",{className:"text-xs text-gray-500",children:"Show notifications for operations and events"})]})]}),c.jsxs("label",{className:"flex items-center space-x-3",children:[c.jsx("input",{type:"checkbox",checked:v.soundEnabled,onChange:k=>T("soundEnabled",k.target.checked),className:"rounded"}),c.jsxs("div",{children:[c.jsx("span",{className:"text-sm font-medium",children:"Sound notifications"}),c.jsx("p",{className:"text-xs text-gray-500",children:"Play sounds for important notifications"})]})]}),c.jsxs("label",{className:"flex items-center space-x-3",children:[c.jsx("input",{type:"checkbox",checked:v.showDesktopNotifications,onChange:k=>T("showDesktopNotifications",k.target.checked),className:"rounded"}),c.jsxs("div",{children:[c.jsx("span",{className:"text-sm font-medium",children:"Desktop notifications"}),c.jsx("p",{className:"text-xs text-gray-500",children:"Show system notifications on desktop"})]})]})]})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-sm font-medium mb-2",children:"Notification Duration"}),c.jsxs("select",{value:v.notificationDuration,onChange:k=>T("notificationDuration",parseInt(k.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md",children:[c.jsx("option",{value:3,children:"3 seconds"}),c.jsx("option",{value:5,children:"5 seconds"}),c.jsx("option",{value:10,children:"10 seconds"}),c.jsx("option",{value:0,children:"Until dismissed"})]})]})]}),Y=()=>c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{children:[c.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Automatic Operations"}),c.jsxs("div",{className:"space-y-4",children:[c.jsxs("label",{className:"flex items-center space-x-3",children:[c.jsx("input",{type:"checkbox",checked:v.autoCleanEnabled,onChange:k=>T("autoCleanEnabled",k.target.checked),className:"rounded"}),c.jsxs("div",{children:[c.jsx("span",{className:"text-sm font-medium",children:"Enable automatic cleaning"}),c.jsx("p",{className:"text-xs text-gray-500",children:"Automatically run cleanup operations"})]})]}),v.autoCleanEnabled&&c.jsxs("div",{className:"ml-6",children:[c.jsx("label",{className:"block text-sm font-medium mb-2",children:"Clean every"}),c.jsxs("select",{value:v.autoCleanInterval,onChange:k=>T("autoCleanInterval",parseInt(k.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md",children:[c.jsx("option",{value:6,children:"6 hours"}),c.jsx("option",{value:12,children:"12 hours"}),c.jsx("option",{value:24,children:"24 hours"}),c.jsx("option",{value:48,children:"48 hours"}),c.jsx("option",{value:168,children:"1 week"})]})]})]})]}),c.jsxs("div",{children:[c.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Safety & Confirmation"}),c.jsxs("div",{className:"space-y-3",children:[c.jsxs("label",{className:"flex items-center space-x-3",children:[c.jsx("input",{type:"checkbox",checked:v.confirmDangerousOperations,onChange:k=>T("confirmDangerousOperations",k.target.checked),className:"rounded"}),c.jsxs("div",{children:[c.jsx("span",{className:"text-sm font-medium",children:"Confirm dangerous operations"}),c.jsx("p",{className:"text-xs text-gray-500",children:"Ask for confirmation before destructive actions"})]})]}),c.jsxs("label",{className:"flex items-center space-x-3",children:[c.jsx("input",{type:"checkbox",checked:v.showOperationProgress,onChange:k=>T("showOperationProgress",k.target.checked),className:"rounded"}),c.jsxs("div",{children:[c.jsx("span",{className:"text-sm font-medium",children:"Show operation progress"}),c.jsx("p",{className:"text-xs text-gray-500",children:"Display progress dialogs during operations"})]})]})]})]})]}),$=()=>c.jsx("div",{className:"space-y-6",children:c.jsxs("div",{children:[c.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Automatic Backup"}),c.jsxs("div",{className:"space-y-4",children:[c.jsxs("label",{className:"flex items-center space-x-3",children:[c.jsx("input",{type:"checkbox",checked:v.autoBackupEnabled,onChange:k=>T("autoBackupEnabled",k.target.checked),className:"rounded"}),c.jsxs("div",{children:[c.jsx("span",{className:"text-sm font-medium",children:"Enable automatic backups"}),c.jsx("p",{className:"text-xs text-gray-500",children:"Automatically create backups before operations"})]})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-sm font-medium mb-2",children:"Backup Location"}),c.jsxs("div",{className:"flex space-x-2",children:[c.jsx("input",{type:"text",value:v.backupLocation,onChange:k=>T("backupLocation",k.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"}),c.jsx(Ge,{size:"sm",variant:"outline",children:c.jsx(kh,{className:"w-4 h-4"})})]})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-sm font-medium mb-2",children:"Backup Retention"}),c.jsxs("select",{value:v.backupRetention,onChange:k=>T("backupRetention",parseInt(k.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md",children:[c.jsx("option",{value:7,children:"7 days"}),c.jsx("option",{value:14,children:"14 days"}),c.jsx("option",{value:30,children:"30 days"}),c.jsx("option",{value:90,children:"90 days"}),c.jsx("option",{value:365,children:"1 year"})]})]}),c.jsxs("label",{className:"flex items-center space-x-3",children:[c.jsx("input",{type:"checkbox",checked:v.compressBackups,onChange:k=>T("compressBackups",k.target.checked),className:"rounded"}),c.jsxs("div",{children:[c.jsx("span",{className:"text-sm font-medium",children:"Compress backups"}),c.jsx("p",{className:"text-xs text-gray-500",children:"Reduce backup file size using compression"})]})]})]})]})}),P=()=>c.jsx("div",{className:"space-y-6",children:c.jsxs("div",{children:[c.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Resource Management"}),c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{children:[c.jsx("label",{className:"block text-sm font-medium mb-2",children:"Maximum Concurrent Operations"}),c.jsx("input",{type:"number",value:v.maxConcurrentOperations,onChange:k=>T("maxConcurrentOperations",parseInt(k.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md",min:"1",max:"10"}),c.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Higher values may improve speed but use more resources"})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-sm font-medium mb-2",children:"Memory Limit (MB)"}),c.jsx("input",{type:"number",value:v.memoryLimit,onChange:k=>T("memoryLimit",parseInt(k.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md",min:"256",max:"4096",step:"256"}),c.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Maximum memory usage for operations"})]}),c.jsxs("label",{className:"flex items-center space-x-3",children:[c.jsx("input",{type:"checkbox",checked:v.enableHardwareAcceleration,onChange:k=>T("enableHardwareAcceleration",k.target.checked),className:"rounded"}),c.jsxs("div",{children:[c.jsx("span",{className:"text-sm font-medium",children:"Enable hardware acceleration"}),c.jsx("p",{className:"text-xs text-gray-500",children:"Use GPU acceleration when available"})]})]})]})]})}),F=()=>c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{children:[c.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Access Control"}),c.jsxs("div",{className:"space-y-3",children:[c.jsxs("label",{className:"flex items-center space-x-3",children:[c.jsx("input",{type:"checkbox",checked:v.requirePasswordForOperations,onChange:k=>T("requirePasswordForOperations",k.target.checked),className:"rounded"}),c.jsxs("div",{children:[c.jsx("span",{className:"text-sm font-medium",children:"Require password for operations"}),c.jsx("p",{className:"text-xs text-gray-500",children:"Protect sensitive operations with password"})]})]}),c.jsxs("label",{className:"flex items-center space-x-3",children:[c.jsx("input",{type:"checkbox",checked:v.encryptBackups,onChange:k=>T("encryptBackups",k.target.checked),className:"rounded"}),c.jsxs("div",{children:[c.jsx("span",{className:"text-sm font-medium",children:"Encrypt backups"}),c.jsx("p",{className:"text-xs text-gray-500",children:"Encrypt backup files for security"})]})]})]})]}),c.jsxs("div",{children:[c.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Logging & Audit"}),c.jsxs("div",{className:"space-y-4",children:[c.jsxs("label",{className:"flex items-center space-x-3",children:[c.jsx("input",{type:"checkbox",checked:v.logOperations,onChange:k=>T("logOperations",k.target.checked),className:"rounded"}),c.jsxs("div",{children:[c.jsx("span",{className:"text-sm font-medium",children:"Log all operations"}),c.jsx("p",{className:"text-xs text-gray-500",children:"Keep detailed logs of all operations"})]})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-sm font-medium mb-2",children:"Maximum Log Entries"}),c.jsx("input",{type:"number",value:v.maxLogEntries,onChange:k=>T("maxLogEntries",parseInt(k.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md",min:"100",max:"10000",step:"100"})]})]})]})]}),K=()=>c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{children:[c.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Developer Options"}),c.jsxs("div",{className:"space-y-3",children:[c.jsxs("label",{className:"flex items-center space-x-3",children:[c.jsx("input",{type:"checkbox",checked:v.debugMode,onChange:k=>T("debugMode",k.target.checked),className:"rounded"}),c.jsxs("div",{children:[c.jsx("span",{className:"text-sm font-medium",children:"Debug mode"}),c.jsx("p",{className:"text-xs text-gray-500",children:"Enable detailed logging and debug features"})]})]}),c.jsxs("label",{className:"flex items-center space-x-3",children:[c.jsx("input",{type:"checkbox",checked:v.telemetryEnabled,onChange:k=>T("telemetryEnabled",k.target.checked),className:"rounded"}),c.jsxs("div",{children:[c.jsx("span",{className:"text-sm font-medium",children:"Send usage data"}),c.jsx("p",{className:"text-xs text-gray-500",children:"Help improve the app by sending anonymous usage data"})]})]})]})]}),c.jsxs("div",{children:[c.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Updates"}),c.jsxs("div",{className:"space-y-3",children:[c.jsxs("label",{className:"flex items-center space-x-3",children:[c.jsx("input",{type:"checkbox",checked:v.autoUpdatesEnabled,onChange:k=>T("autoUpdatesEnabled",k.target.checked),className:"rounded"}),c.jsxs("div",{children:[c.jsx("span",{className:"text-sm font-medium",children:"Automatic updates"}),c.jsx("p",{className:"text-xs text-gray-500",children:"Automatically download and install updates"})]})]}),c.jsxs("label",{className:"flex items-center space-x-3",children:[c.jsx("input",{type:"checkbox",checked:v.betaUpdates,onChange:k=>T("betaUpdates",k.target.checked),className:"rounded"}),c.jsxs("div",{children:[c.jsx("span",{className:"text-sm font-medium",children:"Beta updates"}),c.jsx("p",{className:"text-xs text-gray-500",children:"Receive pre-release versions with new features"})]})]})]})]}),c.jsxs("div",{children:[c.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Data Management"}),c.jsxs("div",{className:"space-y-3",children:[c.jsxs(Ge,{variant:"outline",onClick:Z,className:"w-full",children:[c.jsx(Dh,{className:"w-4 h-4 mr-2"}),"Export Settings"]}),c.jsxs("div",{children:[c.jsx("input",{type:"file",accept:".json",onChange:ae,className:"hidden",id:"import-settings"}),c.jsxs(Ge,{variant:"outline",onClick:()=>document.getElementById("import-settings")?.click(),className:"w-full",children:[c.jsx(Mh,{className:"w-4 h-4 mr-2"}),"Import Settings"]})]}),c.jsxs(Ge,{variant:"outline",onClick:q,className:"w-full text-red-600 hover:text-red-700",children:[c.jsx(Js,{className:"w-4 h-4 mr-2"}),"Reset to Defaults"]})]})]})]}),ce=()=>c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{children:[c.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Appearance"}),c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{children:[c.jsx("label",{className:"block text-sm font-medium mb-2",children:"Theme"}),c.jsx("div",{className:"flex space-x-2",children:[{value:"light",label:"Light",icon:Ef},{value:"dark",label:"Dark",icon:Of},{value:"system",label:"System",icon:Th}].map(({value:k,label:ne,icon:ie})=>c.jsxs("button",{onClick:()=>T("theme",k),className:`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${v.theme===k?"border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300":"border-gray-200 hover:border-gray-300"}`,children:[c.jsx(ie,{className:"w-4 h-4"}),c.jsx("span",{children:ne})]},k))})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-sm font-medium mb-2",children:"Language"}),c.jsxs("select",{value:v.language,onChange:k=>T("language",k.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md",children:[c.jsx("option",{value:"en",children:"English"}),c.jsx("option",{value:"es",children:"Español"}),c.jsx("option",{value:"fr",children:"Français"}),c.jsx("option",{value:"de",children:"Deutsch"}),c.jsx("option",{value:"zh",children:"中文"}),c.jsx("option",{value:"ja",children:"日本語"})]})]})]})]}),c.jsxs("div",{children:[c.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Startup & Window"}),c.jsxs("div",{className:"space-y-3",children:[c.jsxs("label",{className:"flex items-center space-x-3",children:[c.jsx("input",{type:"checkbox",checked:v.autoStart,onChange:k=>T("autoStart",k.target.checked),className:"rounded"}),c.jsxs("div",{children:[c.jsx("span",{className:"text-sm font-medium",children:"Start with system"}),c.jsx("p",{className:"text-xs text-gray-500",children:"Launch Augment VIP when your computer starts"})]})]}),c.jsxs("label",{className:"flex items-center space-x-3",children:[c.jsx("input",{type:"checkbox",checked:v.minimizeToTray,onChange:k=>T("minimizeToTray",k.target.checked),className:"rounded"}),c.jsxs("div",{children:[c.jsx("span",{className:"text-sm font-medium",children:"Minimize to system tray"}),c.jsx("p",{className:"text-xs text-gray-500",children:"Hide window in system tray when minimized"})]})]}),c.jsxs("label",{className:"flex items-center space-x-3",children:[c.jsx("input",{type:"checkbox",checked:v.closeToTray,onChange:k=>T("closeToTray",k.target.checked),className:"rounded"}),c.jsxs("div",{children:[c.jsx("span",{className:"text-sm font-medium",children:"Close to system tray"}),c.jsx("p",{className:"text-xs text-gray-500",children:"Keep running in background when window is closed"})]})]})]})]})]});return c.jsxs("div",{className:"p-6 max-w-7xl mx-auto",children:[c.jsxs("div",{className:"mb-6",children:[c.jsx("h1",{className:"text-2xl font-bold mb-2",children:"Settings"}),c.jsx("p",{className:"text-gray-600",children:"Configure your Augment VIP preferences and options"})]}),c.jsxs("div",{className:"flex gap-6",children:[c.jsx("div",{className:"w-64 flex-shrink-0",children:c.jsx("div",{className:"space-y-1",children:G.map(k=>{const ne=k.icon;return c.jsxs("button",{onClick:()=>w(k.id),className:`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${o===k.id?"bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border border-blue-200":"text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800"}`,children:[c.jsx(ne,{className:"w-5 h-5"}),c.jsxs("div",{children:[c.jsx("div",{className:"font-medium",children:k.title}),c.jsx("div",{className:"text-xs text-gray-500",children:k.description})]})]},k.id)})})}),c.jsxs("div",{className:"flex-1",children:[c.jsxs(pl,{className:"p-6",children:[o==="general"&&ce(),o==="notifications"&&M(),o==="operations"&&Y(),o==="backup"&&$(),o==="security"&&F(),o==="performance"&&P(),o==="advanced"&&K()]}),D&&c.jsxs("div",{className:"mt-4 flex justify-between items-center p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 rounded-lg",children:[c.jsxs("div",{className:"flex items-center space-x-2",children:[c.jsx(Cf,{className:"w-5 h-5 text-yellow-600"}),c.jsx("span",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-200",children:"You have unsaved changes"})]}),c.jsxs("div",{className:"flex space-x-2",children:[c.jsx(Ge,{variant:"outline",size:"sm",onClick:()=>R(!1),children:"Discard"}),c.jsxs(Ge,{size:"sm",onClick:C,children:[c.jsx(Bf,{className:"w-4 h-4 mr-2"}),"Save Changes"]})]})]})]})]})]})};function Mg(){const{activeTab:r,theme:j,setTheme:g}=_l();V.useEffect(()=>{if(window.themeAPI){const w=window.themeAPI.getTheme();g(w)}return window.electronAPI&&(window.electronAPI.onCommandOutput(w=>{console.log("Command output:",w)}),window.electronAPI.onQuickClean(()=>{console.log("Quick clean triggered from system tray")})),()=>{window.electronAPI&&(window.electronAPI.removeAllListeners("command-output"),window.electronAPI.removeAllListeners("quick-clean"))}},[g]);const o=()=>{switch(r){case"dashboard":return c.jsx(Tf,{});case"operations":return c.jsx(Yh,{});case"backups":return c.jsx(kg,{});case"settings":return c.jsx(Dg,{});default:return c.jsx(Tf,{})}};return c.jsx("div",{className:j==="dark"?"dark":"",children:c.jsx(_h,{children:o()})})}Hh.createRoot(document.getElementById("root")).render(c.jsx(V.StrictMode,{children:c.jsx(Mg,{})}));
//# sourceMappingURL=index-D-4ml8Yv.js.map
