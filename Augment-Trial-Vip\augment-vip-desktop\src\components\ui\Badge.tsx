import React, { forwardRef } from "react";
import { cn } from "@/utils/cn";

export type BadgeVariant = "default" | "success" | "warning" | "error" | "info" | "outline";
export type BadgeSize = "sm" | "md" | "lg";

export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Badge variant
   * @default "default"
   */
  variant?: BadgeVariant;
  
  /**
   * Badge size
   * @default "md"
   */
  size?: BadgeSize;
  
  /**
   * Whether the badge has a dot indicator
   * @default false
   */
  dot?: boolean;
}

/**
 * Badge component for status indicators and labels
 */
const Badge = forwardRef<HTMLDivElement, BadgeProps>(
  ({ className, variant = "default", size = "md", dot = false, children, ...props }, ref) => {
    const variantStyles: Record<BadgeVariant, string> = {
      default: "bg-neutral-100 text-neutral-800 dark:bg-dark-700 dark:text-neutral-200",
      success: "bg-success-100 text-success-800 dark:bg-success-900/20 dark:text-success-400",
      warning: "bg-warning-100 text-warning-800 dark:bg-warning-900/20 dark:text-warning-400",
      error: "bg-error-100 text-error-800 dark:bg-error-900/20 dark:text-error-400",
      info: "bg-brand-100 text-brand-800 dark:bg-brand-900/20 dark:text-brand-400",
      outline: "border border-neutral-300 text-neutral-700 dark:border-dark-600 dark:text-neutral-300",
    };

    const sizeStyles: Record<BadgeSize, string> = {
      sm: "px-2 py-0.5 text-xs",
      md: "px-2.5 py-1 text-sm",
      lg: "px-3 py-1.5 text-base",
    };

    const dotStyles: Record<BadgeVariant, string> = {
      default: "bg-neutral-400 dark:bg-neutral-500",
      success: "bg-success-500",
      warning: "bg-warning-500",
      error: "bg-error-500",
      info: "bg-brand-500",
      outline: "bg-neutral-400 dark:bg-neutral-500",
    };

    return (
      <div
        ref={ref}
        className={cn(
          // Base styles
          "inline-flex items-center rounded-full font-medium transition-colors",
          // Variant styles
          variantStyles[variant],
          // Size styles
          sizeStyles[size],
          // Custom classes
          className
        )}
        {...props}
      >
        {dot && (
          <div
            className={cn(
              "mr-1.5 h-2 w-2 rounded-full",
              dotStyles[variant]
            )}
            aria-hidden="true"
          />
        )}
        {children}
      </div>
    );
  }
);

Badge.displayName = "Badge";

export { Badge };
