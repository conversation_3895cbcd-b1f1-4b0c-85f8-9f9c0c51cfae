import React, { useState, useEffect, useRef } from 'react';
import { 
  Shield, 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Eye, 
  Globe, 
  Lock,
  Wifi,
  Database,
  Clock,
  TrendingUp,
  TrendingDown,
  Pause,
  Play,
  Settings
} from 'lucide-react';

interface PrivacyMetrics {
  overall_score: number;
  threat_level: 'low' | 'medium' | 'high' | 'critical';
  active_protections: number;
  blocked_threats: number;
  network_status: 'secure' | 'exposed' | 'monitoring';
  fingerprint_protection: boolean;
  domain_blocking: boolean;
  last_scan: string;
}

interface ThreatEvent {
  id: string;
  timestamp: string;
  type: 'tracking' | 'fingerprinting' | 'network' | 'data_leak';
  severity: 'low' | 'medium' | 'high' | 'critical';
  source: string;
  description: string;
  blocked: boolean;
}

interface RealTimePrivacyMonitorProps {
  isOpen: boolean;
  onClose: () => void;
}

const RealTimePrivacyMonitor: React.FC<RealTimePrivacyMonitorProps> = ({ isOpen, onClose }) => {
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [metrics, setMetrics] = useState<PrivacyMetrics>({
    overall_score: 75,
    threat_level: 'medium',
    active_protections: 3,
    blocked_threats: 0,
    network_status: 'monitoring',
    fingerprint_protection: true,
    domain_blocking: true,
    last_scan: new Date().toISOString()
  });
  const [threatEvents, setThreatEvents] = useState<ThreatEvent[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const monitoringInterval = useRef<NodeJS.Timeout | null>(null);

  // Start/Stop monitoring
  const toggleMonitoring = async () => {
    if (isMonitoring) {
      // Stop monitoring
      if (monitoringInterval.current) {
        clearInterval(monitoringInterval.current);
        monitoringInterval.current = null;
      }
      setIsMonitoring(false);
    } else {
      // Start monitoring
      setIsMonitoring(true);
      setIsLoading(true);
      
      try {
        // Initial scan
        await performPrivacyScan();
        
        // Set up periodic monitoring
        monitoringInterval.current = setInterval(async () => {
          await performPrivacyScan();
        }, 10000); // Scan every 10 seconds
        
      } catch (error) {
        console.error('Failed to start monitoring:', error);
        setIsMonitoring(false);
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Perform privacy scan
  const performPrivacyScan = async () => {
    try {
      // Simulate real-time monitoring data
      const newMetrics: PrivacyMetrics = {
        overall_score: Math.floor(Math.random() * 30) + 70, // 70-100
        threat_level: Math.random() > 0.7 ? 'high' : Math.random() > 0.4 ? 'medium' : 'low',
        active_protections: 3 + Math.floor(Math.random() * 3), // 3-5
        blocked_threats: metrics.blocked_threats + Math.floor(Math.random() * 3),
        network_status: Math.random() > 0.8 ? 'exposed' : 'secure',
        fingerprint_protection: Math.random() > 0.1, // 90% chance active
        domain_blocking: Math.random() > 0.1, // 90% chance active
        last_scan: new Date().toISOString()
      };

      setMetrics(newMetrics);

      // Generate threat events occasionally
      if (Math.random() > 0.7) {
        const threatTypes = ['tracking', 'fingerprinting', 'network', 'data_leak'] as const;
        const severities = ['low', 'medium', 'high', 'critical'] as const;
        const sources = [
          'doubleclick.net', 'google-analytics.com', 'facebook.com/tr',
          'tiktok.com/analytics', 'microsoft.com/telemetry', 'unknown-tracker.com'
        ];

        const newThreat: ThreatEvent = {
          id: `threat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: new Date().toISOString(),
          type: threatTypes[Math.floor(Math.random() * threatTypes.length)],
          severity: severities[Math.floor(Math.random() * severities.length)],
          source: sources[Math.floor(Math.random() * sources.length)],
          description: `Detected ${threatTypes[Math.floor(Math.random() * threatTypes.length)]} attempt`,
          blocked: Math.random() > 0.2 // 80% chance blocked
        };

        setThreatEvents(prev => [newThreat, ...prev.slice(0, 19)]); // Keep last 20 events
      }

    } catch (error) {
      console.error('Privacy scan failed:', error);
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (monitoringInterval.current) {
        clearInterval(monitoringInterval.current);
      }
    };
  }, []);

  // Get threat level color
  const getThreatLevelColor = (level: string) => {
    switch (level) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // Get score color
  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    if (score >= 50) return 'text-orange-600';
    return 'text-red-600';
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
          <div className="flex items-center space-x-3">
            <Activity className="w-6 h-6" />
            <h2 className="text-xl font-semibold">Real-Time Privacy Monitor</h2>
            {isMonitoring && (
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm">Live Monitoring</span>
              </div>
            )}
          </div>
          <button
            onClick={onClose}
            className="text-white hover:text-gray-200 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
          {/* Control Panel */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <button
                onClick={toggleMonitoring}
                disabled={isLoading}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                  isMonitoring 
                    ? 'bg-red-600 text-white hover:bg-red-700' 
                    : 'bg-green-600 text-white hover:bg-green-700'
                } disabled:opacity-50 disabled:cursor-not-allowed`}
              >
                {isLoading ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                ) : isMonitoring ? (
                  <Pause className="w-4 h-4" />
                ) : (
                  <Play className="w-4 h-4" />
                )}
                <span>{isLoading ? 'Starting...' : isMonitoring ? 'Stop Monitoring' : 'Start Monitoring'}</span>
              </button>
              
              <div className="text-sm text-gray-600">
                Last scan: {new Date(metrics.last_scan).toLocaleTimeString()}
              </div>
            </div>

            <button className="flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-800 transition-colors">
              <Settings className="w-4 h-4" />
              <span>Settings</span>
            </button>
          </div>

          {/* Privacy Score Dashboard */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            {/* Overall Score */}
            <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-gray-900">Privacy Score</h3>
                <Shield className="w-5 h-5 text-blue-600" />
              </div>
              <div className={`text-2xl font-bold ${getScoreColor(metrics.overall_score)}`}>
                {metrics.overall_score}/100
              </div>
              <div className="flex items-center mt-2">
                {metrics.overall_score >= 80 ? (
                  <TrendingUp className="w-4 h-4 text-green-600 mr-1" />
                ) : (
                  <TrendingDown className="w-4 h-4 text-red-600 mr-1" />
                )}
                <span className="text-sm text-gray-600">
                  {metrics.overall_score >= 80 ? 'Excellent' : metrics.overall_score >= 60 ? 'Good' : 'Needs Improvement'}
                </span>
              </div>
            </div>

            {/* Threat Level */}
            <div className="bg-gradient-to-br from-yellow-50 to-orange-100 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-gray-900">Threat Level</h3>
                <AlertTriangle className="w-5 h-5 text-orange-600" />
              </div>
              <div className={`inline-flex px-2 py-1 rounded-full text-sm font-medium capitalize ${getThreatLevelColor(metrics.threat_level)}`}>
                {metrics.threat_level}
              </div>
              <div className="text-sm text-gray-600 mt-2">
                Current risk assessment
              </div>
            </div>

            {/* Active Protections */}
            <div className="bg-gradient-to-br from-green-50 to-emerald-100 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-gray-900">Active Protections</h3>
                <Lock className="w-5 h-5 text-green-600" />
              </div>
              <div className="text-2xl font-bold text-green-600">
                {metrics.active_protections}
              </div>
              <div className="text-sm text-gray-600 mt-2">
                Protection layers active
              </div>
            </div>

            {/* Blocked Threats */}
            <div className="bg-gradient-to-br from-red-50 to-pink-100 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-gray-900">Blocked Threats</h3>
                <Eye className="w-5 h-5 text-red-600" />
              </div>
              <div className="text-2xl font-bold text-red-600">
                {metrics.blocked_threats}
              </div>
              <div className="text-sm text-gray-600 mt-2">
                Since monitoring started
              </div>
            </div>
          </div>

          {/* Protection Status */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div className="bg-white border rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-gray-900">Network Status</h4>
                <Wifi className={`w-5 h-5 ${metrics.network_status === 'secure' ? 'text-green-600' : 'text-red-600'}`} />
              </div>
              <div className={`inline-flex px-2 py-1 rounded-full text-sm font-medium capitalize ${
                metrics.network_status === 'secure' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {metrics.network_status}
              </div>
            </div>

            <div className="bg-white border rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-gray-900">Fingerprint Protection</h4>
                <Globe className={`w-5 h-5 ${metrics.fingerprint_protection ? 'text-green-600' : 'text-red-600'}`} />
              </div>
              <div className={`inline-flex px-2 py-1 rounded-full text-sm font-medium ${
                metrics.fingerprint_protection ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {metrics.fingerprint_protection ? 'Active' : 'Inactive'}
              </div>
            </div>

            <div className="bg-white border rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-gray-900">Domain Blocking</h4>
                <Database className={`w-5 h-5 ${metrics.domain_blocking ? 'text-green-600' : 'text-red-600'}`} />
              </div>
              <div className={`inline-flex px-2 py-1 rounded-full text-sm font-medium ${
                metrics.domain_blocking ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {metrics.domain_blocking ? 'Active' : 'Inactive'}
              </div>
            </div>
          </div>

          {/* Real-Time Threat Feed */}
          <div className="bg-white border rounded-lg">
            <div className="p-4 border-b border-gray-200">
              <h4 className="font-medium text-gray-900 flex items-center">
                <Clock className="w-4 h-4 mr-2" />
                Real-Time Threat Feed
              </h4>
            </div>
            <div className="max-h-64 overflow-y-auto">
              {threatEvents.length === 0 ? (
                <div className="p-4 text-center text-gray-500">
                  {isMonitoring ? 'No threats detected. Your privacy is protected!' : 'Start monitoring to see real-time threats'}
                </div>
              ) : (
                <div className="divide-y divide-gray-200">
                  {threatEvents.map((threat) => (
                    <div key={threat.id} className="p-4 hover:bg-gray-50">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={`w-2 h-2 rounded-full ${
                            threat.blocked ? 'bg-green-500' : 'bg-red-500'
                          }`}></div>
                          <div>
                            <div className="font-medium text-gray-900">{threat.description}</div>
                            <div className="text-sm text-gray-600">
                              Source: {threat.source} • {new Date(threat.timestamp).toLocaleTimeString()}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium capitalize ${getThreatLevelColor(threat.severity)}`}>
                            {threat.severity}
                          </span>
                          {threat.blocked ? (
                            <CheckCircle className="w-4 h-4 text-green-600" />
                          ) : (
                            <AlertTriangle className="w-4 h-4 text-red-600" />
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RealTimePrivacyMonitor;
