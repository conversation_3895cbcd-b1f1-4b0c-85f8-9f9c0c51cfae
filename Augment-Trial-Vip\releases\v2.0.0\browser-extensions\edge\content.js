
// Augment VIP Privacy Protection - Content Script
// Advanced fingerprint spoofing and privacy protection

(function() {
  'use strict';

  // Configuration from profile
  const config = {
  "profile_type": "stealth",
  "target_browser": "chrome",
  "spoofed_user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
  "spoofed_screen_resolution": "1920x1080",
  "spoofed_timezone": "America/New_York",
  "spoofed_webgl_vendor": "Intel Inc.",
  "spoofed_webgl_renderer": "Intel Iris OpenGL Engine",
  "protection_features": {
    "canvas_noise": true,
    "webgl_spoofing": true,
    "font_masking": true,
    "user_agent_spoofing": true,
    "screen_spoofing": true,
    "timezone_spoofing": true
  },
  "consistency_score": 85
};

  // Canvas fingerprint protection
  function protectCanvas() {
    const originalGetContext = HTMLCanvasElement.prototype.getContext;
    const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
    const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;

    HTMLCanvasElement.prototype.getContext = function(type, ...args) {
      const context = originalGetContext.apply(this, [type, ...args]);
      if (type === '2d' && config.protection_features.canvas_noise) {
        // Add noise to canvas operations
        const originalFillText = context.fillText;
        context.fillText = function(...args) {
          // Add slight randomization
          const result = originalFillText.apply(this, args);
          const imageData = context.getImageData(0, 0, 1, 1);
          if (imageData.data.length > 0) {
            imageData.data[0] = (imageData.data[0] + Math.floor(Math.random() * 3)) % 256;
            context.putImageData(imageData, 0, 0);
          }
          return result;
        };
      }
      return context;
    };

    HTMLCanvasElement.prototype.toDataURL = function(...args) {
      if (config.protection_features.canvas_noise) {
        // Add noise before converting to data URL
        const ctx = this.getContext('2d');
        const imageData = ctx.getImageData(0, 0, this.width, this.height);
        for (let i = 0; i < imageData.data.length; i += 4) {
          if (Math.random() < 0.001) { // Very subtle noise
            imageData.data[i] = (imageData.data[i] + Math.floor(Math.random() * 3)) % 256;
          }
        }
        ctx.putImageData(imageData, 0, 0);
      }
      return originalToDataURL.apply(this, args);
    };
  }

  // WebGL fingerprint protection
  function protectWebGL() {
    if (!config.protection_features.webgl_spoofing) return;

    const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
    WebGLRenderingContext.prototype.getParameter = function(parameter) {
      // Spoof specific WebGL parameters
      switch (parameter) {
        case this.VENDOR:
          return config.spoofed_webgl_vendor || 'Intel Inc.';
        case this.RENDERER:
          return config.spoofed_webgl_renderer || 'Intel Iris OpenGL Engine';
        case this.VERSION:
          return 'WebGL 1.0 (OpenGL ES 2.0 Chromium)';
        case this.SHADING_LANGUAGE_VERSION:
          return 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)';
        default:
          return originalGetParameter.apply(this, arguments);
      }
    };
  }

  // User Agent spoofing
  function spoofUserAgent() {
    if (config.spoofed_user_agent) {
      Object.defineProperty(navigator, 'userAgent', {
        get: () => config.spoofed_user_agent,
        configurable: true
      });
    }
  }

  // Screen resolution spoofing
  function spoofScreen() {
    if (config.spoofed_screen_resolution) {
      const [width, height] = config.spoofed_screen_resolution.split('x').map(Number);
      Object.defineProperty(screen, 'width', { get: () => width });
      Object.defineProperty(screen, 'height', { get: () => height });
      Object.defineProperty(screen, 'availWidth', { get: () => width });
      Object.defineProperty(screen, 'availHeight', { get: () => height - 40 });
    }
  }

  // Timezone spoofing
  function spoofTimezone() {
    if (config.spoofed_timezone) {
      const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
      Date.prototype.getTimezoneOffset = function() {
        // Convert timezone to offset (simplified)
        const timezoneOffsets = {
          'America/New_York': 300,
          'Europe/London': 0,
          'Asia/Tokyo': -540,
          'UTC': 0
        };
        return timezoneOffsets[config.spoofed_timezone] || originalGetTimezoneOffset.call(this);
      };
    }
  }

  // Font enumeration protection
  function protectFonts() {
    if (config.protection_features.font_masking) {
      // Limit available fonts to common ones
      const commonFonts = [
        'Arial', 'Helvetica', 'Times New Roman', 'Courier New',
        'Verdana', 'Georgia', 'Palatino', 'Garamond', 'Bookman'
      ];
      
      // Override font detection methods
      if (document.fonts && document.fonts.check) {
        const originalCheck = document.fonts.check;
        document.fonts.check = function(font, text) {
          const fontFamily = font.split(' ').pop().replace(/['"]/g, '');
          if (!commonFonts.includes(fontFamily)) {
            return false;
          }
          return originalCheck.call(this, font, text);
        };
      }
    }
  }

  // Initialize all protections
  function initializeProtections() {
    try {
      protectCanvas();
      protectWebGL();
      spoofUserAgent();
      spoofScreen();
      spoofTimezone();
      protectFonts();
      
      console.log('[Augment VIP] Privacy protections activated');
    } catch (error) {
      console.error('[Augment VIP] Error initializing protections:', error);
    }
  }

  // Run protections
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeProtections);
  } else {
    initializeProtections();
  }

})();
