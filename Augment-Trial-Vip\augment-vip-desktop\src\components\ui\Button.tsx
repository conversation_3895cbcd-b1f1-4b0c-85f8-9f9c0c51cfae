import React, { forwardRef } from "react";
import { cn } from "@/utils/cn";
import { Size, sizes } from "@/utils/theme";
import { Loader2 } from "lucide-react";

export type ButtonVariant = "primary" | "secondary" | "outline" | "ghost" | "link" | "danger" | "warning";

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /**
   * Button variant
   * @default "primary"
   */
  variant?: ButtonVariant;
  
  /**
   * Button size
   * @default "md"
   */
  size?: Size;
  
  /**
   * Whether the button is in a loading state
   * @default false
   */
  isLoading?: boolean;
  
  /**
   * Icon to display before the button text
   */
  leftIcon?: React.ReactNode;
  
  /**
   * Icon to display after the button text
   */
  rightIcon?: React.ReactNode;
  
  /**
   * Whether the button takes the full width of its container
   * @default false
   */
  fullWidth?: boolean;
}

/**
 * Primary UI component for user interaction
 */
const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      children,
      className,
      variant = "primary",
      size = "md",
      isLoading = false,
      leftIcon,
      rightIcon,
      fullWidth = false,
      disabled,
      ...props
    },
    ref
  ) => {
    // Variant styles
    const variantStyles: Record<ButtonVariant, string> = {
      primary: "bg-brand-500 text-white hover:bg-brand-600 focus-visible:ring-brand-500/50 dark:bg-brand-600 dark:hover:bg-brand-700",
      secondary: "bg-neutral-100 text-neutral-900 hover:bg-neutral-200 focus-visible:ring-neutral-500/50 dark:bg-dark-700 dark:text-neutral-100 dark:hover:bg-dark-600",
      outline: "border border-neutral-300 bg-transparent text-neutral-900 hover:bg-neutral-100 focus-visible:ring-neutral-500/50 dark:border-dark-600 dark:text-neutral-100 dark:hover:bg-dark-800",
      ghost: "bg-transparent text-neutral-900 hover:bg-neutral-100 focus-visible:ring-neutral-500/50 dark:text-neutral-100 dark:hover:bg-dark-800",
      link: "bg-transparent text-brand-500 hover:underline focus-visible:ring-brand-500/50 p-0 h-auto dark:text-brand-400",
      danger: "bg-error-500 text-white hover:bg-error-600 focus-visible:ring-error-500/50 dark:bg-error-600 dark:hover:bg-error-700",
      warning: "bg-warning-500 text-white hover:bg-warning-600 focus-visible:ring-warning-500/50 dark:bg-warning-600 dark:hover:bg-warning-700",
    };

    // Size styles
    const sizeStyles = {
      xs: "text-xs px-2 py-1 h-6 gap-1",
      sm: "text-sm px-3 py-1.5 h-8 gap-1.5",
      md: "text-base px-4 py-2 h-10 gap-2",
      lg: "text-lg px-6 py-3 h-12 gap-2",
      xl: "text-xl px-8 py-4 h-14 gap-3",
    };

    return (
      <button
        ref={ref}
        className={cn(
          // Base styles
          "inline-flex items-center justify-center rounded-md font-medium transition-colors",
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 dark:focus-visible:ring-offset-dark-900",
          "disabled:opacity-50 disabled:pointer-events-none",
          // Variant styles
          variantStyles[variant],
          // Size styles
          sizeStyles[size],
          // Full width
          fullWidth && "w-full",
          // Custom classes
          className
        )}
        disabled={isLoading || disabled}
        {...props}
      >
        {isLoading && (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" aria-hidden="true" />
        )}
        {!isLoading && leftIcon && (
          <span className="inline-flex">{leftIcon}</span>
        )}
        {children}
        {!isLoading && rightIcon && (
          <span className="inline-flex">{rightIcon}</span>
        )}
      </button>
    );
  }
);

Button.displayName = "Button";

export { Button };
