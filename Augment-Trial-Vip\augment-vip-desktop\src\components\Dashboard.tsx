import React, { useState, useEffect } from "react";
import {
  Database,
  Shield,
  Clock,
  CheckCircle,
  AlertCircle,
  Play,
  Activity,
  Info,
  HardDrive,
  Search,
  Trash2,
} from "lucide-react";
import useAppStore from "../store/useAppStore";
import ProgressModal from "./ProgressModal";
import { DatabaseModal } from "./DatabaseModal";
import PrivacyScoreDashboard from "./PrivacyScoreDashboard";

const Dashboard: React.FC = () => {
  const {
    systemInfo,
    appVersion,
    operationLogs,
    isOperationRunning,
    currentOperation,
    operationProgress,
    startOperation,
    finishOperation,
    updateOperationProgress,
    addOperationLog,
  } = useAppStore();

  const [lastCleanDate, setLastCleanDate] = useState<Date | null>(null);
  const [vsCodeStatus, setVsCodeStatus] = useState<
    "unknown" | "found" | "not-found"
  >("unknown");
  const [showProgressModal, setShowProgressModal] = useState(false);
  const [showDatabaseModal, setShowDatabaseModal] = useState(false);

  useEffect(() => {
    // Check VS Code installation status
    const checkVsCodeStatus = async () => {
      if (window.electronAPI) {
        try {
          const vsCodeInfo = await window.electronAPI.checkVsCodeInstallation();
          setVsCodeStatus(vsCodeInfo.found ? "found" : "not-found");
        } catch (error) {
          console.error("Failed to check VS Code status:", error);
          setVsCodeStatus("not-found");
        }
      }
    };

    if (systemInfo) {
      checkVsCodeStatus();
    }
  }, [systemInfo]);

  const handleQuickOperation = async (
    operation: "clean" | "modify-ids" | "all"
  ) => {
    if (isOperationRunning) return;

    try {
      startOperation(operation);
      setShowProgressModal(true);

      // Set up progress simulation
      let progress = 0;
      const progressInterval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 85) progress = 85;
        updateOperationProgress(progress);
      }, 800);

      // Execute the actual operation
      const result = await window.electronAPI.executePythonCommand(operation);

      clearInterval(progressInterval);
      updateOperationProgress(100);

      setTimeout(() => {
        finishOperation(
          result.success,
          result.success
            ? `${operation} completed successfully`
            : `${operation} failed`,
          result.stderr || result.stdout
        );

        if (result.success && (operation === "clean" || operation === "all")) {
          setLastCleanDate(new Date());
        }
      }, 500);
    } catch (error) {
      finishOperation(
        false,
        `Failed to execute ${operation}`,
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  };

  const recentLogs = operationLogs.slice(0, 5);

  return (
    <div className="p-6 space-y-8">
      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* VS Code Status */}
        <div className="bg-white dark:bg-slate-800 rounded-2xl p-6 border border-gray-200 dark:border-slate-700 shadow-lg hover:shadow-xl transition-all duration-300 group">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                VS Code Status
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {vsCodeStatus === "found"
                  ? "Found"
                  : vsCodeStatus === "not-found"
                  ? "Not Found"
                  : "Checking..."}
              </p>
            </div>
            <div
              className={`p-4 rounded-2xl shadow-md group-hover:shadow-lg transition-all duration-300 ${
                vsCodeStatus === "found"
                  ? "bg-gradient-to-r from-green-500 to-emerald-500"
                  : vsCodeStatus === "not-found"
                  ? "bg-gradient-to-r from-red-500 to-rose-500"
                  : "bg-gray-100 dark:bg-gray-700"
              }`}
            >
              {vsCodeStatus === "found" ? (
                <CheckCircle className="w-7 h-7 text-white" />
              ) : vsCodeStatus === "not-found" ? (
                <AlertCircle className="w-7 h-7 text-white" />
              ) : (
                <Activity className="w-7 h-7 text-gray-600 dark:text-gray-400 animate-spin" />
              )}
            </div>
          </div>
        </div>

        {/* Last Clean */}
        <div className="bg-white dark:bg-slate-800 rounded-2xl p-6 border border-gray-200 dark:border-slate-700 shadow-lg hover:shadow-xl transition-all duration-300 group">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                Last Clean
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {lastCleanDate ? lastCleanDate.toLocaleDateString() : "Never"}
              </p>
            </div>
            <div className="p-4 rounded-2xl bg-gradient-to-r from-blue-500 to-purple-500 shadow-md group-hover:shadow-lg transition-all duration-300">
              <Clock className="w-7 h-7 text-white" />
            </div>
          </div>
        </div>

        {/* Total Operations */}
        <div className="bg-white dark:bg-dark-800 rounded-lg p-6 border border-gray-200 dark:border-dark-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Operations
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {operationLogs.length}
              </p>
            </div>
            <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900">
              <Database className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>

        {/* System Info */}
        <div className="bg-white dark:bg-dark-800 rounded-lg p-6 border border-gray-200 dark:border-dark-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Platform
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100 capitalize">
                {systemInfo?.platform || "Unknown"}
              </p>
            </div>
            <div className="p-3 rounded-full bg-gray-100 dark:bg-gray-700">
              <Info className="w-6 h-6 text-gray-600 dark:text-gray-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Privacy Score Dashboard */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-1">
          <PrivacyScoreDashboard />
        </div>

        {/* Recent Activity */}
        <div className="lg:col-span-2 bg-white dark:bg-dark-800 rounded-lg border border-gray-200 dark:border-dark-700">
          <div className="p-4 border-b border-gray-200 dark:border-dark-700">
            <h3 className="font-semibold text-gray-900 dark:text-gray-100 flex items-center">
              <Activity className="w-5 h-5 text-blue-600 mr-2" />
              Recent Privacy Activity
            </h3>
          </div>
          <div className="p-4">
            {recentLogs.length > 0 ? (
              <div className="space-y-3">
                {recentLogs.map((log, index) => (
                  <div key={index} className="flex items-center space-x-3 p-2 bg-gray-50 dark:bg-dark-700 rounded">
                    <div className={`w-2 h-2 rounded-full ${
                      log.type === 'success' ? 'bg-green-500' :
                      log.type === 'error' ? 'bg-red-500' : 'bg-blue-500'
                    }`}></div>
                    <div className="flex-1">
                      <p className="text-sm text-gray-900 dark:text-gray-100">{log.message}</p>
                      <p className="text-xs text-gray-500">{new Date(log.timestamp).toLocaleString()}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Activity className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p>No recent privacy activity</p>
                <p className="text-sm">Start using privacy features to see activity here</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white dark:bg-slate-800 rounded-2xl p-8 border border-gray-200 dark:border-slate-700 shadow-lg">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-3 h-8 bg-gradient-to-b from-blue-500 to-purple-600 rounded-full"></div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Quick Actions
          </h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <button
            onClick={() => setShowDatabaseModal(true)}
            disabled={isOperationRunning}
            className="group relative overflow-hidden flex flex-col items-center justify-center space-y-2 p-6
                     bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700
                     rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300
                     disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105"
          >
            <HardDrive className="w-8 h-8 text-white group-hover:scale-110 transition-transform duration-200" />
            <div className="text-center">
              <div className="font-semibold text-white">Database Manager</div>
              <div className="text-xs text-blue-100">
                Scan & Clean Databases
              </div>
            </div>
            <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </button>

          <button
            onClick={() => handleQuickOperation("clean")}
            disabled={isOperationRunning}
            className="group relative overflow-hidden flex flex-col items-center justify-center space-y-2 p-6
                     bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700
                     rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300
                     disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105"
          >
            <Trash2 className="w-8 h-8 text-white group-hover:scale-110 transition-transform duration-200" />
            <div className="text-center">
              <div className="font-semibold text-white">Quick Clean</div>
              <div className="text-xs text-green-100">VS Code Cleanup</div>
            </div>
            <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </button>

          <button
            onClick={() => handleQuickOperation("modify-ids")}
            disabled={isOperationRunning}
            className="group relative overflow-hidden flex flex-col items-center justify-center space-y-2 p-6
                     bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700
                     rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300
                     disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105"
          >
            <Shield className="w-8 h-8 text-white group-hover:scale-110 transition-transform duration-200" />
            <div className="text-center">
              <div className="font-semibold text-white">Modify IDs</div>
              <div className="text-xs text-purple-100">Privacy Protection</div>
            </div>
            <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </button>

          <button
            onClick={() => handleQuickOperation("all")}
            disabled={isOperationRunning}
            className="group relative overflow-hidden flex flex-col items-center justify-center space-y-2 p-6
                     bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700
                     rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300
                     disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105"
          >
            <Play className="w-8 h-8 text-white group-hover:scale-110 transition-transform duration-200" />
            <div className="text-center">
              <div className="font-semibold text-white">Run All</div>
              <div className="text-xs text-orange-100">Complete Cleanup</div>
            </div>
            <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </button>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white dark:bg-dark-800 rounded-lg p-6 border border-gray-200 dark:border-dark-700">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Recent Activity
        </h2>
        {recentLogs.length > 0 ? (
          <div className="space-y-3">
            {recentLogs.map((log) => (
              <div
                key={log.id}
                className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-dark-700 rounded-lg"
              >
                <div
                  className={`p-2 rounded-full ${
                    log.status === "success"
                      ? "bg-green-100 dark:bg-green-900"
                      : log.status === "error"
                      ? "bg-red-100 dark:bg-red-900"
                      : "bg-yellow-100 dark:bg-yellow-900"
                  }`}
                >
                  {log.status === "success" ? (
                    <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400" />
                  ) : log.status === "error" ? (
                    <AlertCircle className="w-4 h-4 text-red-600 dark:text-red-400" />
                  ) : (
                    <Activity className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
                  )}
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {log.message}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {log.timestamp.toLocaleString()} • {log.operation}
                  </p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500 dark:text-gray-400 text-center py-8">
            No recent activity. Run an operation to get started.
          </p>
        )}
      </div>

      {/* Progress Modal */}
      <ProgressModal
        isOpen={showProgressModal}
        onClose={() => setShowProgressModal(false)}
      />

      {/* Database Modal */}
      <DatabaseModal
        isOpen={showDatabaseModal}
        onClose={() => setShowDatabaseModal(false)}
      />
    </div>
  );
};

export default Dashboard;
