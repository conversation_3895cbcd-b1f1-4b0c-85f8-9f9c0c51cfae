import { setupServer } from 'msw/node'
import { http, HttpResponse } from 'msw'

// Mock API responses
export const handlers = [
  // System endpoints
  http.get('http://127.0.0.1:8000/api/v1/system/info', () => {
    return HttpResponse.json({
      platform: 'Darwin',
      architecture: '64bit',
      python_version: '3.10.1',
      app_version: '2.0.0',
      environment: 'development',
      uptime: **********.707226,
      timestamp: new Date().toISOString(),
    })
  }),

  http.get('http://127.0.0.1:8000/api/v1/system/health', () => {
    return HttpResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '2.0.0',
      environment: 'development',
      database: {
        healthy: true,
        status: 'connected',
        pool_type: 'StaticPool',
      },
      system: {
        cpu_percent: 21.3,
        memory_total: 17179869184,
        memory_available: **********,
        memory_percent: 62.1,
        disk_total: 245107195904,
        disk_used: 11245871104,
        disk_free: 108198338560,
        disk_percent: 4.588144000637427,
      },
    })
  }),

  // Authentication endpoints
  http.post('http://127.0.0.1:8000/api/v1/auth/login', () => {
    return HttpResponse.json({
      access_token: 'mock-access-token',
      refresh_token: 'mock-refresh-token',
      token_type: 'bearer',
      expires_in: 1800,
    })
  }),

  // Database endpoints
  http.get('http://127.0.0.1:8000/api/v1/database/scan', () => {
    return HttpResponse.json([
      {
        path: '/Users/<USER>/Library/Application Support/Code/User/workspaceStorage/test.db',
        type: 'sqlite',
        size: 1024000,
        last_modified: new Date().toISOString(),
        readable: true,
        writable: true,
      },
    ])
  }),

  // Operations endpoints
  http.post('http://127.0.0.1:8000/api/v1/operations/start', () => {
    return HttpResponse.json({
      task_id: 'mock-task-id',
      operation_type: 'database_cleanup',
      status: 'pending',
      progress: 0.0,
      message: 'Operation queued',
      created_at: new Date().toISOString(),
    })
  }),

  http.get('http://127.0.0.1:8000/api/v1/operations/status/:taskId', () => {
    return HttpResponse.json({
      task_id: 'mock-task-id',
      operation_type: 'database_cleanup',
      status: 'completed',
      progress: 100.0,
      message: 'Operation completed successfully',
      created_at: new Date().toISOString(),
      completed_at: new Date().toISOString(),
      result: {
        records_found: 150,
        records_removed: 145,
        backup_created: true,
        duration: 2.5,
      },
    })
  }),
]

export const server = setupServer(...handlers)
