{"version": 3, "file": "rebuild.js", "sourceRoot": "", "sources": ["../../../src/util/rebuild/rebuild.ts"], "names": [], "mappings": ";;;AACA,+CAAkC;AAClC,oCAAmC;AACnC,6BAA4B;AAErB,MAAM,OAAO,GAAG,KAAK,EAAE,OAAuB,EAAiB,EAAE;;IACtE,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO,CAAA;IACxB,kBAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,gCAAgC,CAAC,CAAA;IAEpD,MAAM,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,mBAAmB,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE;QAC7F,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC;KACvC,CAAC,CAAA;IAEF,IAAI,YAAmB,CAAA;IAEvB,MAAA,KAAK,CAAC,MAAM,0CAAE,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE;QAC/B,kBAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAA;IAC5B,CAAC,CAAC,CAAA;IACF,MAAA,KAAK,CAAC,MAAM,0CAAE,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE;QAC/B,kBAAG,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAA;IAC7B,CAAC,CAAC,CAAA;IAEF,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,OAAqF,EAAE,EAAE;;QAC5G,MAAM,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG,OAAO,CAAA;QACnC,QAAQ,GAAG,EAAE,CAAC;YACZ,KAAK,cAAc,CAAC,CAAC,CAAC;gBACpB,kBAAG,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,WAAW,CAAC,CAAA;gBAC3C,MAAK;YACP,CAAC;YACD,KAAK,aAAa,CAAC,CAAC,CAAC;gBACnB,kBAAG,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,UAAU,CAAC,CAAA;gBAC1C,MAAK;YACP,CAAC;YACD,KAAK,aAAa,CAAC,CAAC,CAAC;gBACnB,MAAA,kBAAG,CAAC,KAAK,mEAAG,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,oDAAoD,CAAC,CAAA;gBACvF,MAAK;YACP,CAAC;YACD,KAAK,eAAe,CAAC,CAAC,CAAC;gBACrB,YAAY,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;gBAC7C,YAAY,CAAC,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAA;gBACtC,MAAK;YACP,CAAC;YACD,KAAK,cAAc,CAAC,CAAC,CAAC;gBACpB,kBAAG,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAA;gBACpD,MAAK;YACP,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC1C,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;YACtB,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;gBAChC,OAAO,EAAE,CAAA;YACX,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,YAAY,IAAI,IAAI,KAAK,CAAC,oCAAoC,IAAI,EAAE,CAAC,CAAC,CAAA;YAC/E,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;AACJ,CAAC,CAAA;AArDY,QAAA,OAAO,WAqDnB", "sourcesContent": ["import { RebuildOptions } from \"@electron/rebuild\"\nimport { log } from \"builder-util\"\nimport * as cp from \"child_process\"\nimport * as path from \"path\"\n\nexport const rebuild = async (options: RebuildOptions): Promise<void> => {\n  const { arch } = options\n  log.info({ arch }, `installing native dependencies`)\n\n  const child = cp.fork(path.resolve(__dirname, \"remote-rebuild.js\"), [JSON.stringify(options)], {\n    stdio: [\"pipe\", \"pipe\", \"pipe\", \"ipc\"],\n  })\n\n  let pendingError: Error\n\n  child.stdout?.on(\"data\", chunk => {\n    log.info(chunk.toString())\n  })\n  child.stderr?.on(\"data\", chunk => {\n    log.error(chunk.toString())\n  })\n\n  child.on(\"message\", (message: { msg: string; moduleName: string; err: { message: string; stack: string } }) => {\n    const { moduleName, msg } = message\n    switch (msg) {\n      case \"module-found\": {\n        log.info({ moduleName, arch }, \"preparing\")\n        break\n      }\n      case \"module-done\": {\n        log.info({ moduleName, arch }, \"finished\")\n        break\n      }\n      case \"module-skip\": {\n        log.debug?.({ moduleName, arch }, \"skipped. set ENV=electron-rebuild to determine why\")\n        break\n      }\n      case \"rebuild-error\": {\n        pendingError = new Error(message.err.message)\n        pendingError.stack = message.err.stack\n        break\n      }\n      case \"rebuild-done\": {\n        log.info(\"completed installing native dependencies\")\n        break\n      }\n    }\n  })\n\n  await new Promise<void>((resolve, reject) => {\n    child.on(\"exit\", code => {\n      if (code === 0 && !pendingError) {\n        resolve()\n      } else {\n        reject(pendingError || new Error(`Rebuilder failed with exit code: ${code}`))\n      }\n    })\n  })\n}\n"]}