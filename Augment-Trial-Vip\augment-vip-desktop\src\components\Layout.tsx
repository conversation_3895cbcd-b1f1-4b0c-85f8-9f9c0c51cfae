import React, { useEffect } from "react";
import {
  Database,
  Settings,
  Archive,
  Home,
  Menu,
  Sun,
  Moon,
  Minimize2,
  X,
} from "lucide-react";
import useAppStore from "../store/useAppStore";

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const {
    theme,
    activeTab,
    sidebarCollapsed,
    setActiveTab,
    toggleSidebar,
    toggleTheme,
    setSystemInfo,
    setAppVersion,
  } = useAppStore();

  useEffect(() => {
    // Initialize app data
    const initializeApp = async () => {
      if (window.electronAPI) {
        try {
          const systemInfo = await window.electronAPI.getSystemInfo();
          setSystemInfo(systemInfo);

          const appVersion = await window.electronAPI.getAppVersion();
          setAppVersion(appVersion);
        } catch (error) {
          console.error("Failed to initialize app:", error);
        }
      }
    };

    initializeApp();
  }, [setSystemInfo, setAppVersion]);

  const navigationItems = [
    { id: "dashboard", label: "Dashboard", icon: Home },
    { id: "operations", label: "Operations", icon: Database },
    { id: "backups", label: "Backups", icon: Archive },
    { id: "settings", label: "Settings", icon: Settings },
  ] as const;

  return (
    <div
      className={`flex h-screen ${
        theme === "dark" ? "dark" : ""
      } bg-gray-50 dark:bg-slate-900`}
    >
      {/* Sidebar */}
      <div
        className={`
        bg-white dark:bg-slate-800 border-r border-gray-200 dark:border-slate-700
        transition-all duration-300 ease-in-out shadow-lg relative
        ${sidebarCollapsed ? "w-16" : "w-64"}
      `}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-slate-700 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-slate-800 dark:to-slate-700">
          {!sidebarCollapsed && (
            <div className="flex items-center space-x-3 animate-slide-in-left">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <Database className="w-6 h-6 text-white" />
              </div>
              <div>
                <span className="font-bold text-lg bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Augment VIP
                </span>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Desktop
                </p>
              </div>
            </div>
          )}
          <button
            onClick={toggleSidebar}
            className="p-2.5 rounded-xl hover:bg-white/50 dark:hover:bg-slate-600 transition-all duration-200 hover:shadow-md group"
          >
            <Menu className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors" />
          </button>
        </div>

        {/* Navigation */}
        <nav className="p-4 space-y-1">
          {navigationItems.map((item, index) => {
            const Icon = item.icon;
            const isActive = activeTab === item.id;

            return (
              <button
                key={item.id}
                onClick={() => setActiveTab(item.id)}
                className={`
                  w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200
                  group relative overflow-hidden
                  ${
                    isActive
                      ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg transform scale-105"
                      : "text-gray-600 dark:text-gray-400 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 dark:hover:from-slate-700 dark:hover:to-slate-600 hover:text-blue-600 dark:hover:text-blue-400 hover:shadow-md"
                  }
                `}
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <Icon
                  className={`w-5 h-5 flex-shrink-0 transition-transform duration-200 ${
                    isActive ? "scale-110" : "group-hover:scale-110"
                  }`}
                />
                {!sidebarCollapsed && (
                  <span className="font-medium transition-all duration-200">
                    {item.label}
                  </span>
                )}
                {isActive && (
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-600/20 rounded-xl animate-pulse" />
                )}
              </button>
            );
          })}
        </nav>

        {/* Theme Toggle */}
        <div className="absolute bottom-4 left-4 right-4">
          <button
            onClick={toggleTheme}
            className="w-full flex items-center justify-center space-x-2 px-4 py-3 rounded-xl
                     bg-gradient-to-r from-gray-100 to-gray-200 dark:from-dark-700 dark:to-dark-600
                     hover:from-blue-100 hover:to-purple-100 dark:hover:from-blue-900/30 dark:hover:to-purple-900/30
                     transition-all duration-200 shadow-soft hover:shadow-medium group"
          >
            {theme === "light" ? (
              <Moon className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors" />
            ) : (
              <Sun className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-500 dark:group-hover:text-yellow-400 transition-colors" />
            )}
            {!sidebarCollapsed && (
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                {theme === "light" ? "Dark Mode" : "Light Mode"}
              </span>
            )}
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800">
        {/* Title Bar */}
        <div
          className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl border-b border-gray-200/50 dark:border-slate-700/50
                      px-6 py-4 flex items-center justify-between shadow-lg"
        >
          <div className="flex items-center space-x-3">
            <div className="w-2 h-8 bg-gradient-to-b from-blue-500 to-purple-600 rounded-full"></div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 capitalize">
              {activeTab}
            </h1>
          </div>

          {/* Window Controls (for custom title bar if needed) */}
          <div className="flex items-center space-x-2">
            <button className="p-2.5 rounded-xl hover:bg-gray-100/50 dark:hover:bg-slate-700/50 transition-all duration-200 hover:shadow-md group">
              <Minimize2 className="w-4 h-4 text-gray-600 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors" />
            </button>
            <button className="p-2.5 rounded-xl hover:bg-red-100/50 dark:hover:bg-red-900/50 transition-all duration-200 hover:shadow-soft group">
              <X className="w-4 h-4 text-gray-600 dark:text-gray-400 group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors" />
            </button>
          </div>
        </div>

        {/* Page Content */}
        <div className="flex-1 overflow-auto">{children}</div>
      </div>
    </div>
  );
};

export default Layout;
