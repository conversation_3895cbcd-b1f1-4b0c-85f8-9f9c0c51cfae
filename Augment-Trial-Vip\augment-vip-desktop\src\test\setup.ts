import '@testing-library/jest-dom'
import { beforeAll, afterEach, afterAll, vi } from 'vitest'
import { cleanup } from '@testing-library/react'
import { server } from './mocks/server'

// Mock Electron APIs
Object.defineProperty(window, 'electronAPI', {
  value: {
    getSystemInfo: vi.fn(),
    getAppVersion: vi.fn(),
    onCommandOutput: vi.fn(),
    onQuickClean: vi.fn(),
    removeAllListeners: vi.fn(),
    scanDatabases: vi.fn(),
    cleanDatabase: vi.fn(),
    backupDatabase: vi.fn(),
    analyzeSystemState: vi.fn(),
  },
  writable: true,
})

Object.defineProperty(window, 'themeAPI', {
  value: {
    getTheme: vi.fn(() => 'light'),
    setTheme: vi.fn(),
  },
  writable: true,
})

// Establish API mocking before all tests
beforeAll(() => server.listen())

// Reset any request handlers that we may add during the tests,
// so they don't affect other tests
afterEach(() => {
  cleanup()
  server.resetHandlers()
})

// Clean up after the tests are finished
afterAll(() => server.close())
