/**
 * Theme utilities and constants
 */

export const themes = {
  light: "light",
  dark: "dark",
  system: "system",
} as const;

export type Theme = keyof typeof themes;

/**
 * Get the current theme from localStorage or system preference
 */
export function getStoredTheme(): Theme {
  if (typeof window === "undefined") return "system";
  
  const stored = localStorage.getItem("theme") as Theme;
  if (stored && stored in themes) {
    return stored;
  }
  
  return "system";
}

/**
 * Set theme in localStorage and apply to document
 */
export function setStoredTheme(theme: Theme) {
  if (typeof window === "undefined") return;
  
  localStorage.setItem("theme", theme);
  applyTheme(theme);
}

/**
 * Apply theme to document element
 */
export function applyTheme(theme: Theme) {
  if (typeof window === "undefined") return;
  
  const root = window.document.documentElement;
  root.classList.remove("light", "dark");
  
  if (theme === "system") {
    const systemTheme = window.matchMedia("(prefers-color-scheme: dark)").matches
      ? "dark"
      : "light";
    root.classList.add(systemTheme);
  } else {
    root.classList.add(theme);
  }
}

/**
 * Get system theme preference
 */
export function getSystemTheme(): "light" | "dark" {
  if (typeof window === "undefined") return "light";
  
  return window.matchMedia("(prefers-color-scheme: dark)").matches
    ? "dark"
    : "light";
}

/**
 * Listen for system theme changes
 */
export function watchSystemTheme(callback: (theme: "light" | "dark") => void) {
  if (typeof window === "undefined") return () => {};
  
  const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
  
  const handler = (e: MediaQueryListEvent) => {
    callback(e.matches ? "dark" : "light");
  };
  
  mediaQuery.addEventListener("change", handler);
  
  return () => mediaQuery.removeEventListener("change", handler);
}

/**
 * Theme-aware color utilities
 */
export const colors = {
  // Semantic colors that adapt to theme
  background: {
    primary: "bg-white dark:bg-dark-900",
    secondary: "bg-neutral-50 dark:bg-dark-800",
    tertiary: "bg-neutral-100 dark:bg-dark-700",
  },
  text: {
    primary: "text-neutral-900 dark:text-neutral-100",
    secondary: "text-neutral-600 dark:text-neutral-400",
    tertiary: "text-neutral-500 dark:text-neutral-500",
    inverse: "text-white dark:text-dark-900",
  },
  border: {
    primary: "border-neutral-200 dark:border-dark-700",
    secondary: "border-neutral-300 dark:border-dark-600",
    focus: "border-brand-500 dark:border-brand-400",
  },
  interactive: {
    primary: "bg-brand-500 hover:bg-brand-600 dark:bg-brand-600 dark:hover:bg-brand-700",
    secondary: "bg-neutral-100 hover:bg-neutral-200 dark:bg-dark-700 dark:hover:bg-dark-600",
    ghost: "hover:bg-neutral-100 dark:hover:bg-dark-800",
  },
} as const;

/**
 * Component size variants
 */
export const sizes = {
  xs: {
    padding: "px-2 py-1",
    text: "text-xs",
    height: "h-6",
  },
  sm: {
    padding: "px-3 py-1.5",
    text: "text-sm",
    height: "h-8",
  },
  md: {
    padding: "px-4 py-2",
    text: "text-base",
    height: "h-10",
  },
  lg: {
    padding: "px-6 py-3",
    text: "text-lg",
    height: "h-12",
  },
  xl: {
    padding: "px-8 py-4",
    text: "text-xl",
    height: "h-14",
  },
} as const;

export type Size = keyof typeof sizes;
