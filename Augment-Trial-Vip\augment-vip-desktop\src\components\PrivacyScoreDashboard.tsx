import React, { useState, useEffect } from 'react';
import { 
  Shield, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  CheckCircle, 
  Eye,
  Globe,
  Lock,
  Activity,
  RefreshCw
} from 'lucide-react';

interface PrivacyScore {
  overall_score: number;
  last_updated: string;
  trend: 'up' | 'down' | 'stable';
  components: {
    fingerprint_protection: { score: number; status: 'active' | 'inactive' | 'partial' };
    domain_blocking: { score: number; status: 'active' | 'inactive' | 'partial' };
    network_security: { score: number; status: 'secure' | 'exposed' | 'monitoring' };
    browser_privacy: { score: number; status: 'protected' | 'vulnerable' | 'unknown' };
    system_cleanup: { score: number; status: 'clean' | 'needs_attention' | 'unknown' };
  };
  recommendations: string[];
  threats_blocked_today: number;
}

interface PrivacyScoreDashboardProps {
  className?: string;
}

const PrivacyScoreDashboard: React.FC<PrivacyScoreDashboardProps> = ({ className = '' }) => {
  const [privacyScore, setPrivacyScore] = useState<PrivacyScore>({
    overall_score: 0,
    last_updated: new Date().toISOString(),
    trend: 'stable',
    components: {
      fingerprint_protection: { score: 0, status: 'inactive' },
      domain_blocking: { score: 0, status: 'inactive' },
      network_security: { score: 0, status: 'monitoring' },
      browser_privacy: { score: 0, status: 'unknown' },
      system_cleanup: { score: 0, status: 'unknown' }
    },
    recommendations: [],
    threats_blocked_today: 0
  });
  const [isLoading, setIsLoading] = useState(false);

  // Calculate privacy score
  const calculatePrivacyScore = async () => {
    setIsLoading(true);
    try {
      // Simulate privacy score calculation
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const components = {
        fingerprint_protection: { 
          score: Math.floor(Math.random() * 30) + 70, 
          status: Math.random() > 0.3 ? 'active' : 'inactive' as const
        },
        domain_blocking: { 
          score: Math.floor(Math.random() * 25) + 75, 
          status: Math.random() > 0.2 ? 'active' : 'inactive' as const
        },
        network_security: { 
          score: Math.floor(Math.random() * 40) + 60, 
          status: Math.random() > 0.7 ? 'exposed' : 'secure' as const
        },
        browser_privacy: { 
          score: Math.floor(Math.random() * 35) + 65, 
          status: Math.random() > 0.4 ? 'protected' : 'vulnerable' as const
        },
        system_cleanup: { 
          score: Math.floor(Math.random() * 20) + 80, 
          status: Math.random() > 0.6 ? 'clean' : 'needs_attention' as const
        }
      };

      const overall_score = Math.round(
        Object.values(components).reduce((sum, comp) => sum + comp.score, 0) / 5
      );

      const recommendations = [];
      if (components.fingerprint_protection.status === 'inactive') {
        recommendations.push('Enable fingerprint protection to prevent browser tracking');
      }
      if (components.domain_blocking.status === 'inactive') {
        recommendations.push('Activate domain blocking to stop tracking requests');
      }
      if (components.network_security.status === 'exposed') {
        recommendations.push('Improve network security with VPN or secure DNS');
      }
      if (components.browser_privacy.status === 'vulnerable') {
        recommendations.push('Update browser privacy settings and clear tracking data');
      }
      if (components.system_cleanup.status === 'needs_attention') {
        recommendations.push('Run system cleanup to remove telemetry and tracking files');
      }

      const newScore: PrivacyScore = {
        overall_score,
        last_updated: new Date().toISOString(),
        trend: overall_score > privacyScore.overall_score ? 'up' : 
               overall_score < privacyScore.overall_score ? 'down' : 'stable',
        components,
        recommendations,
        threats_blocked_today: Math.floor(Math.random() * 50) + 10
      };

      setPrivacyScore(newScore);
    } catch (error) {
      console.error('Failed to calculate privacy score:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-refresh every 30 seconds
  useEffect(() => {
    calculatePrivacyScore();
    const interval = setInterval(calculatePrivacyScore, 30000);
    return () => clearInterval(interval);
  }, []);

  // Get score color
  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 75) return 'text-blue-600';
    if (score >= 60) return 'text-yellow-600';
    if (score >= 40) return 'text-orange-600';
    return 'text-red-600';
  };

  // Get score background
  const getScoreBackground = (score: number) => {
    if (score >= 90) return 'bg-green-50 border-green-200';
    if (score >= 75) return 'bg-blue-50 border-blue-200';
    if (score >= 60) return 'bg-yellow-50 border-yellow-200';
    if (score >= 40) return 'bg-orange-50 border-orange-200';
    return 'bg-red-50 border-red-200';
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'protected':
      case 'secure':
      case 'clean':
        return 'text-green-600 bg-green-100';
      case 'partial':
      case 'monitoring':
        return 'text-blue-600 bg-blue-100';
      case 'inactive':
      case 'vulnerable':
      case 'exposed':
      case 'needs_attention':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className={`bg-white dark:bg-dark-800 rounded-lg border border-gray-200 dark:border-dark-700 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-dark-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Shield className="w-5 h-5 text-blue-600" />
            <h3 className="font-semibold text-gray-900 dark:text-gray-100">Privacy Score</h3>
          </div>
          <button
            onClick={calculatePrivacyScore}
            disabled={isLoading}
            className="flex items-center space-x-1 px-2 py-1 text-sm text-gray-600 hover:text-gray-800 transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      <div className="p-4 space-y-4">
        {/* Overall Score */}
        <div className={`rounded-lg p-4 border ${getScoreBackground(privacyScore.overall_score)}`}>
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700">Overall Privacy Score</span>
              {privacyScore.trend === 'up' && <TrendingUp className="w-4 h-4 text-green-600" />}
              {privacyScore.trend === 'down' && <TrendingDown className="w-4 h-4 text-red-600" />}
            </div>
            <span className="text-xs text-gray-500">
              {new Date(privacyScore.last_updated).toLocaleTimeString()}
            </span>
          </div>
          <div className={`text-3xl font-bold ${getScoreColor(privacyScore.overall_score)}`}>
            {privacyScore.overall_score}/100
          </div>
          <div className="text-sm text-gray-600 mt-1">
            {privacyScore.overall_score >= 90 ? 'Excellent Privacy Protection' :
             privacyScore.overall_score >= 75 ? 'Good Privacy Protection' :
             privacyScore.overall_score >= 60 ? 'Moderate Privacy Protection' :
             privacyScore.overall_score >= 40 ? 'Basic Privacy Protection' :
             'Poor Privacy Protection'}
          </div>
        </div>

        {/* Component Scores */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Protection Components</h4>
          <div className="grid grid-cols-1 gap-2">
            {Object.entries(privacyScore.components).map(([key, component]) => (
              <div key={key} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-dark-700 rounded">
                <div className="flex items-center space-x-2">
                  {key === 'fingerprint_protection' && <Globe className="w-4 h-4 text-purple-600" />}
                  {key === 'domain_blocking' && <Shield className="w-4 h-4 text-blue-600" />}
                  {key === 'network_security' && <Lock className="w-4 h-4 text-green-600" />}
                  {key === 'browser_privacy' && <Eye className="w-4 h-4 text-orange-600" />}
                  {key === 'system_cleanup' && <Activity className="w-4 h-4 text-red-600" />}
                  <span className="text-sm text-gray-700 dark:text-gray-300 capitalize">
                    {key.replace('_', ' ')}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {component.score}
                  </span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium capitalize ${getStatusColor(component.status)}`}>
                    {component.status.replace('_', ' ')}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-1">
              <CheckCircle className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-900 dark:text-blue-100">Threats Blocked</span>
            </div>
            <div className="text-lg font-bold text-blue-600">{privacyScore.threats_blocked_today}</div>
            <div className="text-xs text-blue-700 dark:text-blue-300">Today</div>
          </div>
          
          <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-1">
              <Activity className="w-4 h-4 text-green-600" />
              <span className="text-sm font-medium text-green-900 dark:text-green-100">Active Protections</span>
            </div>
            <div className="text-lg font-bold text-green-600">
              {Object.values(privacyScore.components).filter(c => c.status === 'active' || c.status === 'protected' || c.status === 'secure').length}
            </div>
            <div className="text-xs text-green-700 dark:text-green-300">of 5</div>
          </div>
        </div>

        {/* Recommendations */}
        {privacyScore.recommendations.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
              <AlertTriangle className="w-4 h-4 text-yellow-600 mr-1" />
              Recommendations
            </h4>
            <div className="space-y-1">
              {privacyScore.recommendations.slice(0, 3).map((rec, index) => (
                <div key={index} className="text-xs text-gray-600 dark:text-gray-400 bg-yellow-50 dark:bg-yellow-900/20 p-2 rounded">
                  • {rec}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PrivacyScoreDashboard;
