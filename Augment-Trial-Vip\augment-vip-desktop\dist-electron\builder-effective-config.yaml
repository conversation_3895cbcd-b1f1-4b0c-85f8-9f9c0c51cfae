directories:
  output: dist-electron
  buildResources: build
appId: com.augment.vip.desktop
productName: Augment VIP Desktop
files:
  - filter:
      - dist/**/*
      - electron/**/*
      - node_modules/**/*
extraResources:
  - from: ../augment-vip
    to: augment-vip
    filter:
      - '**/*'
      - '!**/.git/**/*'
      - '!**/node_modules/**/*'
win:
  target:
    - target: portable
      arch:
        - x64
  verifyUpdateCodeSignature: false
  signAndEditExecutable: false
mac:
  target:
    - target: dmg
      arch:
        - x64
        - arm64
    - target: zip
      arch:
        - x64
        - arm64
  category: public.app-category.utilities
  hardenedRuntime: true
  gatekeeperAssess: false
  entitlements: electron/entitlements.mac.plist
  entitlementsInherit: electron/entitlements.mac.plist
linux:
  target:
    - target: AppImage
      arch:
        - x64
    - target: deb
      arch:
        - x64
    - target: rpm
      arch:
        - x64
  category: Utility
electronVersion: 37.2.0
