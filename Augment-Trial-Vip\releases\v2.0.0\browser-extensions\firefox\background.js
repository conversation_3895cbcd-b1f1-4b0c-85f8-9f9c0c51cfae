
// Augment VIP Privacy Protection - Background Script
// Handles blocking requests and managing privacy settings

const browser = chrome;

// Tracking domains to block
const trackingDomains = [
  'doubleclick.net',
  'googleadservices.com',
  'googlesyndication.com',
  'google-analytics.com',
  'googletagmanager.com',
  'facebook.com/tr',
  'connect.facebook.net',
  'analytics.tiktok.com',
  'ads.tiktok.com'
];

// Block tracking requests
function blockTrackingRequests() {
  const callback = (details) => {
    const url = new URL(details.url);
    const shouldBlock = trackingDomains.some(domain => 
      url.hostname.includes(domain) || url.hostname.endsWith(domain)
    );
    
    if (shouldBlock) {
      console.log('[Augment VIP] Blocked tracking request:', details.url);
      return { cancel: true };
    }
    
    return {};
  };

  
  browser.webRequest.onBeforeRequest.addListener(
    callback,
    { urls: ["<all_urls>"] },
    ["blocking"]
  );
  
}

// Initialize background script
function initialize() {
  try {
    blockTrackingRequests();
    console.log('[Augment VIP] Background script initialized');
  } catch (error) {
    console.error('[Augment VIP] Background script error:', error);
  }
}


// Manifest V2 background script
initialize();

