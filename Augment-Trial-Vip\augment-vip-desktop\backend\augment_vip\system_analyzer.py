"""
Advanced System State Detection and Analysis
Cross-platform system monitoring for safe operations
"""

import os
import sys
import psutil
import platform
import time
import json
from pathlib import Path
from typing import List, Dict, Any, Optional, Set, Tuple
from datetime import datetime
import subprocess

from .utils import info, success, error, warning

class SystemAnalyzer:
    """Advanced system state detection and analysis"""
    
    def __init__(self):
        self.platform = platform.system().lower()
        self.architecture = platform.machine()
        self.python_version = platform.python_version()
        
    def analyze_system_state(self, target_apps: List[str] = None) -> Dict[str, Any]:
        """
        Comprehensive system state analysis
        
        Args:
            target_apps: List of applications to specifically monitor
        
        Returns:
            Dictionary with complete system analysis
        """
        if target_apps is None:
            target_apps = ['code', 'vscode', 'jetbrains', 'chrome', 'firefox']
        
        analysis_start = time.time()
        
        system_state = {
            'timestamp': datetime.now().isoformat(),
            'platform': {
                'system': self.platform,
                'architecture': self.architecture,
                'python_version': self.python_version,
                'platform_version': platform.platform()
            },
            'running_processes': self.detect_running_processes(target_apps),
            'file_locks': self.check_file_locks(),
            'resource_usage': self.analyze_resource_usage(),
            'disk_space': self.check_disk_space(),
            'network_status': self.check_network_connectivity(),
            'permissions': self.check_permissions(),
            'safety_assessment': {},
            'recommendations': [],
            'warnings': [],
            'analysis_duration': 0
        }
        
        # Perform safety assessment
        system_state['safety_assessment'] = self._assess_safety(system_state)
        
        # Generate recommendations
        system_state['recommendations'] = self._generate_recommendations(system_state)
        
        system_state['analysis_duration'] = time.time() - analysis_start
        
        return system_state
    
    def detect_running_processes(self, target_apps: List[str]) -> Dict[str, Any]:
        """
        Detect running processes related to target applications
        
        Args:
            target_apps: List of application names to monitor
        
        Returns:
            Dictionary with process information
        """
        process_info = {
            'total_processes': 0,
            'target_processes': [],
            'critical_processes': [],
            'resource_intensive': [],
            'scan_time': time.time()
        }
        
        try:
            all_processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'exe', 'cmdline', 'cpu_percent', 'memory_percent']):
                try:
                    pinfo = proc.info
                    all_processes.append(pinfo)
                    
                    # Check if this is a target application
                    proc_name = pinfo['name'].lower() if pinfo['name'] else ''
                    proc_exe = pinfo['exe'].lower() if pinfo['exe'] else ''
                    
                    for target_app in target_apps:
                        if (target_app.lower() in proc_name or 
                            target_app.lower() in proc_exe or
                            any(target_app.lower() in arg.lower() for arg in (pinfo['cmdline'] or []))):
                            
                            process_info['target_processes'].append({
                                'pid': pinfo['pid'],
                                'name': pinfo['name'],
                                'exe': pinfo['exe'],
                                'app_type': target_app,
                                'cpu_percent': pinfo['cpu_percent'],
                                'memory_percent': pinfo['memory_percent'],
                                'is_critical': self._is_critical_process(pinfo, target_app)
                            })
                            
                            if self._is_critical_process(pinfo, target_app):
                                process_info['critical_processes'].append(pinfo['pid'])
                    
                    # Check for resource-intensive processes
                    if (pinfo['cpu_percent'] and pinfo['cpu_percent'] > 50) or \
                       (pinfo['memory_percent'] and pinfo['memory_percent'] > 20):
                        process_info['resource_intensive'].append({
                            'pid': pinfo['pid'],
                            'name': pinfo['name'],
                            'cpu_percent': pinfo['cpu_percent'],
                            'memory_percent': pinfo['memory_percent']
                        })
                
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
            
            process_info['total_processes'] = len(all_processes)
            
        except Exception as e:
            warning(f"Error detecting processes: {e}")
            process_info['error'] = str(e)
        
        return process_info
    
    def check_file_locks(self) -> Dict[str, Any]:
        """
        Check for file locks that might interfere with operations
        
        Returns:
            Dictionary with file lock information
        """
        lock_info = {
            'locked_files': [],
            'database_locks': [],
            'config_locks': [],
            'temp_locks': [],
            'check_time': time.time()
        }
        
        try:
            # Get common application paths
            common_paths = self._get_common_app_paths()
            
            for app_name, paths in common_paths.items():
                for path in paths:
                    if path.exists():
                        locked_files = self._check_directory_locks(path)
                        for locked_file in locked_files:
                            lock_entry = {
                                'file': str(locked_file),
                                'app': app_name,
                                'type': self._classify_file_type(locked_file),
                                'size': locked_file.stat().st_size if locked_file.exists() else 0,
                                'processes': self._get_processes_using_file(locked_file)
                            }
                            
                            lock_info['locked_files'].append(lock_entry)
                            
                            # Categorize by type
                            if lock_entry['type'] == 'database':
                                lock_info['database_locks'].append(lock_entry)
                            elif lock_entry['type'] == 'config':
                                lock_info['config_locks'].append(lock_entry)
                            elif lock_entry['type'] == 'temp':
                                lock_info['temp_locks'].append(lock_entry)
        
        except Exception as e:
            warning(f"Error checking file locks: {e}")
            lock_info['error'] = str(e)
        
        return lock_info
    
    def analyze_resource_usage(self) -> Dict[str, Any]:
        """
        Analyze current system resource usage
        
        Returns:
            Dictionary with resource usage information
        """
        resource_info = {
            'cpu': {},
            'memory': {},
            'disk': {},
            'network': {},
            'analysis_time': time.time()
        }
        
        try:
            # CPU Information
            resource_info['cpu'] = {
                'percent': psutil.cpu_percent(interval=1),
                'count': psutil.cpu_count(),
                'count_logical': psutil.cpu_count(logical=True),
                'freq': psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None,
                'load_avg': os.getloadavg() if hasattr(os, 'getloadavg') else None
            }
            
            # Memory Information
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()
            
            resource_info['memory'] = {
                'total': memory.total,
                'available': memory.available,
                'percent': memory.percent,
                'used': memory.used,
                'free': memory.free,
                'swap_total': swap.total,
                'swap_used': swap.used,
                'swap_percent': swap.percent
            }
            
            # Disk Information
            disk_usage = {}
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_usage[partition.mountpoint] = {
                        'total': usage.total,
                        'used': usage.used,
                        'free': usage.free,
                        'percent': (usage.used / usage.total) * 100,
                        'fstype': partition.fstype
                    }
                except PermissionError:
                    continue
            
            resource_info['disk'] = disk_usage
            
            # Network Information
            network_io = psutil.net_io_counters()
            resource_info['network'] = {
                'bytes_sent': network_io.bytes_sent,
                'bytes_recv': network_io.bytes_recv,
                'packets_sent': network_io.packets_sent,
                'packets_recv': network_io.packets_recv,
                'connections': len(psutil.net_connections())
            }
        
        except Exception as e:
            warning(f"Error analyzing resource usage: {e}")
            resource_info['error'] = str(e)
        
        return resource_info
    
    def check_disk_space(self, min_free_gb: float = 1.0) -> Dict[str, Any]:
        """
        Check available disk space for backup operations
        
        Args:
            min_free_gb: Minimum free space required in GB
        
        Returns:
            Dictionary with disk space information
        """
        disk_info = {
            'sufficient_space': False,
            'free_space_gb': 0,
            'total_space_gb': 0,
            'used_percent': 0,
            'min_required_gb': min_free_gb,
            'partitions': {},
            'warnings': []
        }
        
        try:
            # Check system drive (where application is likely installed)
            if self.platform == 'windows':
                system_drive = 'C:\\'
            else:
                system_drive = '/'
            
            usage = psutil.disk_usage(system_drive)
            disk_info['free_space_gb'] = usage.free / (1024**3)
            disk_info['total_space_gb'] = usage.total / (1024**3)
            disk_info['used_percent'] = (usage.used / usage.total) * 100
            disk_info['sufficient_space'] = disk_info['free_space_gb'] >= min_free_gb
            
            # Check all partitions
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_info['partitions'][partition.mountpoint] = {
                        'free_gb': usage.free / (1024**3),
                        'total_gb': usage.total / (1024**3),
                        'used_percent': (usage.used / usage.total) * 100,
                        'fstype': partition.fstype
                    }
                except PermissionError:
                    continue
            
            # Generate warnings
            if not disk_info['sufficient_space']:
                disk_info['warnings'].append(f"Insufficient disk space. Available: {disk_info['free_space_gb']:.1f}GB, Required: {min_free_gb}GB")
            
            if disk_info['used_percent'] > 90:
                disk_info['warnings'].append(f"Disk usage is very high: {disk_info['used_percent']:.1f}%")
        
        except Exception as e:
            warning(f"Error checking disk space: {e}")
            disk_info['error'] = str(e)
        
        return disk_info
    
    def check_network_connectivity(self) -> Dict[str, Any]:
        """
        Check network connectivity for potential remote operations
        
        Returns:
            Dictionary with network status
        """
        network_info = {
            'connected': False,
            'interfaces': {},
            'dns_resolution': False,
            'internet_access': False,
            'check_time': time.time()
        }
        
        try:
            # Check network interfaces
            interfaces = psutil.net_if_addrs()
            for interface_name, addresses in interfaces.items():
                interface_info = {
                    'addresses': [],
                    'is_up': interface_name in psutil.net_if_stats() and psutil.net_if_stats()[interface_name].isup
                }
                
                for addr in addresses:
                    interface_info['addresses'].append({
                        'family': str(addr.family),
                        'address': addr.address,
                        'netmask': addr.netmask,
                        'broadcast': addr.broadcast
                    })
                
                network_info['interfaces'][interface_name] = interface_info
                
                if interface_info['is_up'] and any('192.168.' in addr['address'] or '10.' in addr['address'] or '172.' in addr['address'] for addr in interface_info['addresses']):
                    network_info['connected'] = True
            
            # Test DNS resolution
            try:
                import socket
                socket.gethostbyname('google.com')
                network_info['dns_resolution'] = True
            except:
                pass
            
            # Test internet access (simple ping-like test)
            try:
                import urllib.request
                urllib.request.urlopen('http://google.com', timeout=5)
                network_info['internet_access'] = True
            except:
                pass
        
        except Exception as e:
            warning(f"Error checking network connectivity: {e}")
            network_info['error'] = str(e)
        
        return network_info
    
    def check_permissions(self) -> Dict[str, Any]:
        """
        Check file system permissions for required operations
        
        Returns:
            Dictionary with permission information
        """
        permission_info = {
            'can_write_app_data': False,
            'can_write_temp': False,
            'can_execute_python': False,
            'is_admin': False,
            'user_info': {},
            'path_permissions': {}
        }
        
        try:
            # Check if running as administrator/root
            if self.platform == 'windows':
                import ctypes
                permission_info['is_admin'] = ctypes.windll.shell32.IsUserAnAdmin() != 0
            else:
                permission_info['is_admin'] = os.geteuid() == 0
            
            # Get user information
            permission_info['user_info'] = {
                'username': os.getenv('USERNAME') or os.getenv('USER'),
                'home_dir': str(Path.home()),
                'current_dir': str(Path.cwd())
            }
            
            # Test common paths
            test_paths = self._get_permission_test_paths()
            
            for path_name, path in test_paths.items():
                permission_info['path_permissions'][path_name] = {
                    'path': str(path),
                    'exists': path.exists(),
                    'readable': os.access(path, os.R_OK) if path.exists() else False,
                    'writable': os.access(path, os.W_OK) if path.exists() else False,
                    'executable': os.access(path, os.X_OK) if path.exists() else False
                }
            
            # Overall permission assessment
            permission_info['can_write_app_data'] = any(
                perm['writable'] for perm in permission_info['path_permissions'].values()
                if 'app_data' in perm.get('path', '').lower()
            )
            
            permission_info['can_write_temp'] = any(
                perm['writable'] for perm in permission_info['path_permissions'].values()
                if 'temp' in perm.get('path', '').lower()
            )
            
            # Test Python execution
            try:
                result = subprocess.run([sys.executable, '--version'], 
                                      capture_output=True, text=True, timeout=5)
                permission_info['can_execute_python'] = result.returncode == 0
            except:
                permission_info['can_execute_python'] = False
        
        except Exception as e:
            warning(f"Error checking permissions: {e}")
            permission_info['error'] = str(e)

        return permission_info

    def _is_critical_process(self, process_info: Dict[str, Any], app_type: str) -> bool:
        """Determine if a process is critical for the application"""
        proc_name = process_info.get('name', '').lower()

        critical_patterns = {
            'code': ['code.exe', 'code', 'electron'],
            'vscode': ['code.exe', 'code', 'electron'],
            'jetbrains': ['idea64.exe', 'pycharm64.exe', 'webstorm64.exe', 'java'],
            'chrome': ['chrome.exe', 'chrome'],
            'firefox': ['firefox.exe', 'firefox']
        }

        patterns = critical_patterns.get(app_type, [])
        return any(pattern in proc_name for pattern in patterns)

    def _get_common_app_paths(self) -> Dict[str, List[Path]]:
        """Get common application paths for file lock checking"""
        paths = {}

        if self.platform == 'windows':
            appdata = Path(os.environ.get('APPDATA', ''))
            localappdata = Path(os.environ.get('LOCALAPPDATA', ''))

            paths['vscode'] = [
                appdata / 'Code' / 'User',
                appdata / 'Code' / 'logs',
                localappdata / 'Programs' / 'Microsoft VS Code'
            ]

            paths['jetbrains'] = [
                appdata / 'JetBrains'
            ]

            paths['chrome'] = [
                localappdata / 'Google' / 'Chrome' / 'User Data'
            ]

        elif self.platform == 'darwin':  # macOS
            home = Path.home()

            paths['vscode'] = [
                home / 'Library' / 'Application Support' / 'Code',
                home / 'Library' / 'Logs' / 'Code'
            ]

            paths['jetbrains'] = [
                home / 'Library' / 'Application Support' / 'JetBrains'
            ]

            paths['chrome'] = [
                home / 'Library' / 'Application Support' / 'Google' / 'Chrome'
            ]

        return paths

    def _check_directory_locks(self, directory: Path) -> List[Path]:
        """Check for locked files in a directory"""
        locked_files = []

        try:
            for root, dirs, files in os.walk(directory):
                # Skip hidden directories
                dirs[:] = [d for d in dirs if not d.startswith('.')]

                for file in files:
                    file_path = Path(root) / file

                    # Check if file is locked by trying to open it
                    if self._is_file_locked(file_path):
                        locked_files.append(file_path)

        except (PermissionError, OSError):
            pass

        return locked_files

    def _is_file_locked(self, file_path: Path) -> bool:
        """Check if a specific file is locked"""
        try:
            # Try to open the file in append mode (least intrusive)
            with open(file_path, 'a'):
                pass
            return False
        except (PermissionError, OSError):
            return True

    def _classify_file_type(self, file_path: Path) -> str:
        """Classify file type for lock categorization"""
        name = file_path.name.lower()
        suffix = file_path.suffix.lower()

        if suffix in ['.db', '.sqlite', '.sqlite3', '.vscdb']:
            return 'database'
        elif suffix in ['.json', '.xml', '.ini', '.conf', '.config']:
            return 'config'
        elif suffix in ['.tmp', '.temp', '.log']:
            return 'temp'
        elif 'lock' in name or 'pid' in name:
            return 'lock'
        else:
            return 'other'

    def _get_processes_using_file(self, file_path: Path) -> List[Dict[str, Any]]:
        """Get processes that are using a specific file"""
        using_processes = []

        try:
            for proc in psutil.process_iter(['pid', 'name', 'open_files']):
                try:
                    if proc.info['open_files']:
                        for open_file in proc.info['open_files']:
                            if Path(open_file.path) == file_path:
                                using_processes.append({
                                    'pid': proc.info['pid'],
                                    'name': proc.info['name'],
                                    'file_descriptor': open_file.fd
                                })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception:
            pass

        return using_processes

    def _get_permission_test_paths(self) -> Dict[str, Path]:
        """Get paths to test for permissions"""
        test_paths = {}

        if self.platform == 'windows':
            test_paths.update({
                'app_data': Path(os.environ.get('APPDATA', '')),
                'local_app_data': Path(os.environ.get('LOCALAPPDATA', '')),
                'temp': Path(os.environ.get('TEMP', '')),
                'user_profile': Path(os.environ.get('USERPROFILE', ''))
            })
        else:  # macOS/Linux
            home = Path.home()
            test_paths.update({
                'home': home,
                'app_support': home / 'Library' / 'Application Support',
                'temp': Path('/tmp'),
                'usr_local': Path('/usr/local')
            })

        return test_paths

    def _assess_safety(self, system_state: Dict[str, Any]) -> Dict[str, Any]:
        """Assess overall system safety for operations"""
        safety_assessment = {
            'overall_safety': 'safe',
            'risk_factors': [],
            'blocking_issues': [],
            'warnings': [],
            'safe_to_proceed': True
        }

        # Check running processes
        running_processes = system_state.get('running_processes', {})
        if running_processes.get('critical_processes'):
            safety_assessment['risk_factors'].append('Critical application processes are running')
            safety_assessment['warnings'].append('Close VS Code and other target applications before proceeding')

        # Check file locks
        file_locks = system_state.get('file_locks', {})
        if file_locks.get('database_locks'):
            safety_assessment['blocking_issues'].append('Database files are locked')
            safety_assessment['safe_to_proceed'] = False

        # Check disk space
        disk_space = system_state.get('disk_space', {})
        if not disk_space.get('sufficient_space', True):
            safety_assessment['blocking_issues'].append('Insufficient disk space for backups')
            safety_assessment['safe_to_proceed'] = False

        # Check resource usage
        resource_usage = system_state.get('resource_usage', {})
        memory = resource_usage.get('memory', {})
        if memory.get('percent', 0) > 90:
            safety_assessment['risk_factors'].append('High memory usage detected')
            safety_assessment['warnings'].append('System memory usage is very high')

        # Check permissions
        permissions = system_state.get('permissions', {})
        if not permissions.get('can_write_app_data', True):
            safety_assessment['blocking_issues'].append('Insufficient permissions to modify application data')
            safety_assessment['safe_to_proceed'] = False

        # Determine overall safety level
        if safety_assessment['blocking_issues']:
            safety_assessment['overall_safety'] = 'unsafe'
        elif len(safety_assessment['risk_factors']) > 2:
            safety_assessment['overall_safety'] = 'risky'
        elif safety_assessment['risk_factors']:
            safety_assessment['overall_safety'] = 'caution'

        return safety_assessment

    def _generate_recommendations(self, system_state: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on system analysis"""
        recommendations = []

        safety = system_state.get('safety_assessment', {})

        if not safety.get('safe_to_proceed', True):
            recommendations.append("⚠️ System is not safe for operations. Address blocking issues first.")

        # Process-based recommendations
        running_processes = system_state.get('running_processes', {})
        if running_processes.get('critical_processes'):
            recommendations.append("🔄 Close VS Code and related applications before proceeding")

        if running_processes.get('resource_intensive'):
            recommendations.append("⚡ High resource usage detected. Consider closing unnecessary applications")

        # File lock recommendations
        file_locks = system_state.get('file_locks', {})
        if file_locks.get('database_locks'):
            recommendations.append("🔒 Database files are locked. Close applications using these files")

        # Disk space recommendations
        disk_space = system_state.get('disk_space', {})
        if disk_space.get('used_percent', 0) > 80:
            recommendations.append("💾 Disk space is running low. Consider cleaning up temporary files")

        # Permission recommendations
        permissions = system_state.get('permissions', {})
        if not permissions.get('is_admin', False) and self.platform == 'windows':
            recommendations.append("🛡️ Consider running as administrator for full access")

        # Network recommendations
        network = system_state.get('network_status', {})
        if not network.get('connected', True):
            recommendations.append("🌐 No network connection detected. Some features may be limited")

        # Platform-specific recommendations
        if self.platform == 'windows':
            recommendations.append("🪟 Windows detected: Ensure antivirus is not blocking operations")
        elif self.platform == 'darwin':
            recommendations.append("🍎 macOS detected: May require additional permissions for some operations")

        # General recommendations
        recommendations.append("💾 Automatic backups will be created before any changes")
        recommendations.append("🔍 Use preview mode to see exactly what will be changed")

        return recommendations
