"""
Enterprise Batch Operations Manager
Handles multiple systems, scheduled operations, and bulk processing
"""

import os
import sys
import json
import asyncio
import threading
import time
import uuid
from pathlib import Path
from typing import List, Dict, Any, Optional, Callable, Union
from datetime import datetime, timedelta
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from dataclasses import dataclass, asdict
from enum import Enum

from .utils import info, success, error, warning
from .database_manager import DatabaseManager
from .system_analyzer import SystemAnalyzer
from .db_cleaner import clean_vscode_db, preview_vscode_cleanup
from .id_modifier import modify_telemetry_ids, preview_id_modification

class OperationType(Enum):
    """Types of operations that can be batched"""
    SCAN_DATABASES = "scan_databases"
    CLEAN_DATABASES = "clean_databases"
    MODIFY_IDS = "modify_ids"
    SYSTEM_ANALYSIS = "system_analysis"
    PREVIEW_CLEAN = "preview_clean"
    PREVIEW_MODIFY = "preview_modify"
    CUSTOM = "custom"

class OperationStatus(Enum):
    """Status of batch operations"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    SKIPPED = "skipped"

@dataclass
class BatchOperation:
    """Individual operation in a batch"""
    id: str
    operation_type: OperationType
    target_system: str
    parameters: Dict[str, Any]
    status: OperationStatus = OperationStatus.PENDING
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3

@dataclass
class BatchJob:
    """Batch job containing multiple operations"""
    id: str
    name: str
    description: str
    operations: List[BatchOperation]
    status: OperationStatus = OperationStatus.PENDING
    created_time: datetime = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    progress: float = 0.0
    total_operations: int = 0
    completed_operations: int = 0
    failed_operations: int = 0
    parallel_execution: bool = False
    max_workers: int = 4

class BatchProcessor:
    """Enterprise batch operations manager"""
    
    def __init__(self, max_concurrent_jobs: int = 2):
        self.max_concurrent_jobs = max_concurrent_jobs
        self.active_jobs: Dict[str, BatchJob] = {}
        self.job_history: List[BatchJob] = []
        self.executor = ThreadPoolExecutor(max_workers=max_concurrent_jobs * 4)
        self.db_manager = DatabaseManager()
        self.system_analyzer = SystemAnalyzer()
        self._shutdown = False
        
    def create_batch_job(self, 
                        name: str, 
                        description: str,
                        operations: List[Dict[str, Any]],
                        parallel_execution: bool = False,
                        max_workers: int = 4) -> str:
        """
        Create a new batch job
        
        Args:
            name: Human-readable name for the job
            description: Description of what the job does
            operations: List of operation definitions
            parallel_execution: Whether to run operations in parallel
            max_workers: Maximum number of parallel workers
        
        Returns:
            Job ID
        """
        job_id = str(uuid.uuid4())
        
        # Convert operation definitions to BatchOperation objects
        batch_operations = []
        for op_def in operations:
            operation = BatchOperation(
                id=str(uuid.uuid4()),
                operation_type=OperationType(op_def['type']),
                target_system=op_def.get('target_system', 'local'),
                parameters=op_def.get('parameters', {}),
                max_retries=op_def.get('max_retries', 3)
            )
            batch_operations.append(operation)
        
        job = BatchJob(
            id=job_id,
            name=name,
            description=description,
            operations=batch_operations,
            created_time=datetime.now(),
            total_operations=len(batch_operations),
            parallel_execution=parallel_execution,
            max_workers=max_workers
        )
        
        self.active_jobs[job_id] = job
        info(f"Created batch job '{name}' with {len(batch_operations)} operations")
        
        return job_id
    
    def execute_batch_job(self, job_id: str, progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        Execute a batch job
        
        Args:
            job_id: ID of the job to execute
            progress_callback: Optional callback for progress updates
        
        Returns:
            Job execution results
        """
        if job_id not in self.active_jobs:
            raise ValueError(f"Job {job_id} not found")
        
        job = self.active_jobs[job_id]
        job.status = OperationStatus.RUNNING
        job.start_time = datetime.now()
        
        info(f"Starting batch job '{job.name}' with {job.total_operations} operations")
        
        try:
            if job.parallel_execution:
                self._execute_parallel(job, progress_callback)
            else:
                self._execute_sequential(job, progress_callback)
            
            job.status = OperationStatus.COMPLETED
            job.end_time = datetime.now()
            
            # Calculate final statistics
            job.completed_operations = sum(1 for op in job.operations if op.status == OperationStatus.COMPLETED)
            job.failed_operations = sum(1 for op in job.operations if op.status == OperationStatus.FAILED)
            job.progress = 100.0
            
            success(f"Batch job '{job.name}' completed: {job.completed_operations}/{job.total_operations} successful")
            
        except Exception as e:
            job.status = OperationStatus.FAILED
            job.end_time = datetime.now()
            error(f"Batch job '{job.name}' failed: {e}")
        
        # Move to history
        self.job_history.append(job)
        del self.active_jobs[job_id]
        
        return self._job_to_dict(job)
    
    def _execute_sequential(self, job: BatchJob, progress_callback: Optional[Callable] = None):
        """Execute operations sequentially"""
        for i, operation in enumerate(job.operations):
            if self._shutdown:
                break
                
            try:
                self._execute_single_operation(operation)
                job.completed_operations += 1
            except Exception as e:
                job.failed_operations += 1
                warning(f"Operation {operation.id} failed: {e}")
            
            # Update progress
            job.progress = ((i + 1) / job.total_operations) * 100
            
            if progress_callback:
                progress_callback(job.progress, operation)
    
    def _execute_parallel(self, job: BatchJob, progress_callback: Optional[Callable] = None):
        """Execute operations in parallel"""
        with ThreadPoolExecutor(max_workers=job.max_workers) as executor:
            # Submit all operations
            future_to_operation = {
                executor.submit(self._execute_single_operation, op): op 
                for op in job.operations
            }
            
            completed = 0
            for future in as_completed(future_to_operation):
                if self._shutdown:
                    break
                    
                operation = future_to_operation[future]
                
                try:
                    future.result()
                    job.completed_operations += 1
                except Exception as e:
                    job.failed_operations += 1
                    warning(f"Operation {operation.id} failed: {e}")
                
                completed += 1
                job.progress = (completed / job.total_operations) * 100
                
                if progress_callback:
                    progress_callback(job.progress, operation)
    
    def _execute_single_operation(self, operation: BatchOperation):
        """Execute a single operation with retry logic"""
        operation.start_time = datetime.now()
        operation.status = OperationStatus.RUNNING
        
        for attempt in range(operation.max_retries + 1):
            try:
                operation.retry_count = attempt
                
                # Execute based on operation type
                if operation.operation_type == OperationType.SCAN_DATABASES:
                    result = self._scan_databases_operation(operation)
                elif operation.operation_type == OperationType.CLEAN_DATABASES:
                    result = self._clean_databases_operation(operation)
                elif operation.operation_type == OperationType.MODIFY_IDS:
                    result = self._modify_ids_operation(operation)
                elif operation.operation_type == OperationType.SYSTEM_ANALYSIS:
                    result = self._system_analysis_operation(operation)
                elif operation.operation_type == OperationType.PREVIEW_CLEAN:
                    result = self._preview_clean_operation(operation)
                elif operation.operation_type == OperationType.PREVIEW_MODIFY:
                    result = self._preview_modify_operation(operation)
                else:
                    raise ValueError(f"Unknown operation type: {operation.operation_type}")
                
                operation.result = result
                operation.status = OperationStatus.COMPLETED
                operation.end_time = datetime.now()
                operation.duration = (operation.end_time - operation.start_time).total_seconds()
                
                info(f"Operation {operation.id} completed successfully")
                return
                
            except Exception as e:
                operation.error_message = str(e)
                
                if attempt < operation.max_retries:
                    warning(f"Operation {operation.id} failed (attempt {attempt + 1}), retrying...")
                    time.sleep(2 ** attempt)  # Exponential backoff
                else:
                    operation.status = OperationStatus.FAILED
                    operation.end_time = datetime.now()
                    operation.duration = (operation.end_time - operation.start_time).total_seconds()
                    error(f"Operation {operation.id} failed after {operation.max_retries} retries: {e}")
                    raise
    
    def _scan_databases_operation(self, operation: BatchOperation) -> Dict[str, Any]:
        """Execute database scan operation"""
        apps = operation.parameters.get('apps', ['vscode'])
        return self.db_manager.scan_all_databases(include_apps=apps)
    
    def _clean_databases_operation(self, operation: BatchOperation) -> Dict[str, Any]:
        """Execute database cleaning operation"""
        # First scan to get databases
        apps = operation.parameters.get('apps', ['vscode'])
        patterns = operation.parameters.get('patterns', ['augment'])
        exclude_patterns = operation.parameters.get('exclude_patterns', [])
        dry_run = operation.parameters.get('dry_run', False)
        
        scan_results = self.db_manager.scan_all_databases(include_apps=apps)
        
        # Collect all databases
        all_databases = []
        for db_type in ['sqlite', 'postgresql', 'mysql']:
            all_databases.extend(scan_results['databases'][db_type])
        
        return self.db_manager.selective_clean(
            databases=all_databases,
            patterns=patterns,
            exclude_patterns=exclude_patterns,
            dry_run=dry_run
        )
    
    def _modify_ids_operation(self, operation: BatchOperation) -> Dict[str, Any]:
        """Execute ID modification operation"""
        # For now, use the existing function
        # In the future, this could be enhanced for batch processing
        success_result = modify_telemetry_ids()
        return {
            'success': success_result,
            'operation': 'modify_telemetry_ids',
            'timestamp': datetime.now().isoformat()
        }
    
    def _system_analysis_operation(self, operation: BatchOperation) -> Dict[str, Any]:
        """Execute system analysis operation"""
        target_apps = operation.parameters.get('target_apps', ['code', 'vscode'])
        return self.system_analyzer.analyze_system_state(target_apps=target_apps)
    
    def _preview_clean_operation(self, operation: BatchOperation) -> Dict[str, Any]:
        """Execute preview clean operation"""
        silent = operation.parameters.get('silent', True)
        return preview_vscode_cleanup(silent=silent)
    
    def _preview_modify_operation(self, operation: BatchOperation) -> Dict[str, Any]:
        """Execute preview modify operation"""
        silent = operation.parameters.get('silent', True)
        return preview_id_modification(silent=silent)
    
    def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Get current status of a job"""
        if job_id in self.active_jobs:
            return self._job_to_dict(self.active_jobs[job_id])
        
        # Check history
        for job in self.job_history:
            if job.id == job_id:
                return self._job_to_dict(job)
        
        raise ValueError(f"Job {job_id} not found")
    
    def cancel_job(self, job_id: str) -> bool:
        """Cancel a running job"""
        if job_id in self.active_jobs:
            job = self.active_jobs[job_id]
            job.status = OperationStatus.CANCELLED
            job.end_time = datetime.now()
            
            # Cancel pending operations
            for operation in job.operations:
                if operation.status == OperationStatus.PENDING:
                    operation.status = OperationStatus.CANCELLED
            
            info(f"Cancelled job '{job.name}'")
            return True
        
        return False
    
    def list_jobs(self, include_history: bool = True) -> Dict[str, Any]:
        """List all jobs"""
        result = {
            'active_jobs': [self._job_to_dict(job) for job in self.active_jobs.values()],
            'job_history': []
        }
        
        if include_history:
            result['job_history'] = [self._job_to_dict(job) for job in self.job_history[-50:]]  # Last 50 jobs
        
        return result
    
    def _job_to_dict(self, job: BatchJob) -> Dict[str, Any]:
        """Convert job to dictionary for serialization"""
        job_dict = asdict(job)
        
        # Convert datetime objects to ISO strings
        for field in ['created_time', 'start_time', 'end_time']:
            if job_dict[field]:
                job_dict[field] = job_dict[field].isoformat()
        
        # Convert operation datetime objects
        for op in job_dict['operations']:
            for field in ['start_time', 'end_time']:
                if op[field]:
                    op[field] = op[field].isoformat()
        
        return job_dict
    
    def shutdown(self):
        """Shutdown the batch processor"""
        self._shutdown = True
        self.executor.shutdown(wait=True)
        info("Batch processor shutdown complete")
