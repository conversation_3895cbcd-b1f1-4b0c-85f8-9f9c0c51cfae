{"version": 3, "file": "utils-jn9BeX0-.js", "sources": ["../../node_modules/zustand/esm/vanilla.mjs", "../../node_modules/zustand/esm/react.mjs"], "sourcesContent": ["const createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const api = { setState, getState, getInitialState, subscribe };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\n\nexport { createStore };\n", "import React from 'react';\nimport { createStore } from 'zustand/vanilla';\n\nconst identity = (arg) => arg;\nfunction useStore(api, selector = identity) {\n  const slice = React.useSyncExternalStore(\n    api.subscribe,\n    () => selector(api.getState()),\n    () => selector(api.getInitialState())\n  );\n  React.useDebugValue(slice);\n  return slice;\n}\nconst createImpl = (createState) => {\n  const api = createStore(createState);\n  const useBoundStore = (selector) => useStore(api, selector);\n  Object.assign(useBoundStore, api);\n  return useBoundStore;\n};\nconst create = (createState) => createState ? createImpl(createState) : createImpl;\n\nexport { create, useStore };\n"], "names": ["createStoreImpl", "createState", "state", "listeners", "setState", "partial", "replace", "nextState", "previousState", "listener", "getState", "api", "initialState", "createStore", "identity", "arg", "useStore", "selector", "slice", "React", "createImpl", "useBoundStore", "create"], "mappings": "qCAAA,MAAMA,EAAmBC,GAAgB,CACvC,IAAIC,EACJ,MAAMC,EAA4B,IAAI,IAChCC,EAAW,CAACC,EAASC,IAAY,CACrC,MAAMC,EAAY,OAAOF,GAAY,WAAaA,EAAQH,CAAK,EAAIG,EACnE,GAAI,CAAC,OAAO,GAAGE,EAAWL,CAAK,EAAG,CAChC,MAAMM,EAAgBN,EACtBA,EAASI,IAA4B,OAAOC,GAAc,UAAYA,IAAc,MAAQA,EAAY,OAAO,OAAO,CAAA,EAAIL,EAAOK,CAAS,EAC1IJ,EAAU,QAASM,GAAaA,EAASP,EAAOM,CAAa,CAAC,CAChE,CACF,EACME,EAAW,IAAMR,EAMjBS,EAAM,CAAE,SAAAP,EAAU,SAAAM,EAAU,gBALV,IAAME,EAKqB,UAJhCH,IACjBN,EAAU,IAAIM,CAAQ,EACf,IAAMN,EAAU,OAAOM,CAAQ,EAEoB,EACtDG,EAAeV,EAAQD,EAAYG,EAAUM,EAAUC,CAAG,EAChE,OAAOA,CACT,EACME,EAAeZ,GAAgBA,EAAcD,EAAgBC,CAAW,EAAID,EClB5Ec,EAAYC,GAAQA,EAC1B,SAASC,EAASL,EAAKM,EAAWH,EAAU,CAC1C,MAAMI,EAAQC,EAAM,qBAClBR,EAAI,UACJ,IAAMM,EAASN,EAAI,UAAU,EAC7B,IAAMM,EAASN,EAAI,gBAAe,CAAE,CACxC,EACE,OAAAQ,EAAM,cAAcD,CAAK,EAClBA,CACT,CACA,MAAME,EAAcnB,GAAgB,CAClC,MAAMU,EAAME,EAAYZ,CAAW,EAC7BoB,EAAiBJ,GAAaD,EAASL,EAAKM,CAAQ,EAC1D,cAAO,OAAOI,EAAeV,CAAG,EACzBU,CACT,EACMC,EAAUrB,GAAgBA,EAAcmB,EAAWnB,CAAW,EAAImB", "x_google_ignoreList": [0, 1]}