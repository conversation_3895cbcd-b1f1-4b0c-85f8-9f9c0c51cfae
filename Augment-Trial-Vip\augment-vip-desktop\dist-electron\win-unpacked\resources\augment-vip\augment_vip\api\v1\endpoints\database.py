"""
Database Management Endpoints
Database operations, cleanup, and backup management
"""

import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
import structlog

from ....core.config import get_settings
from ....core.security import get_current_user
from ....core.metrics import track_database_operation
from ....core.exceptions import DatabaseError, FileOperationError

logger = structlog.get_logger(__name__)
settings = get_settings()

router = APIRouter()


class DatabaseInfo(BaseModel):
    """Database information model"""
    path: str
    type: str
    size: int
    last_modified: datetime
    readable: bool
    writable: bool


class CleanupPattern(BaseModel):
    """Database cleanup pattern model"""
    pattern: str
    description: str
    enabled: bool = True


class CleanupRequest(BaseModel):
    """Database cleanup request model"""
    database_path: str
    patterns: List[str] = Field(default=["augment", "Augment"])
    exclude_patterns: List[str] = Field(default=[])
    dry_run: bool = Field(default=True)
    create_backup: bool = Field(default=True)


class CleanupResult(BaseModel):
    """Database cleanup result model"""
    database_path: str
    records_found: int
    records_removed: int
    backup_created: bool
    backup_path: Optional[str]
    duration: float
    timestamp: datetime


class BackupRequest(BaseModel):
    """Database backup request model"""
    database_path: str
    backup_path: Optional[str] = None
    compress: bool = Field(default=True)


class BackupResult(BaseModel):
    """Database backup result model"""
    database_path: str
    backup_path: str
    size: int
    compressed: bool
    timestamp: datetime


@router.get("/scan", response_model=List[DatabaseInfo])
async def scan_databases(
    search_paths: List[str] = None,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> List[DatabaseInfo]:
    """
    Scan for database files in specified paths
    """
    logger.info("Starting database scan", user=current_user["user_id"])
    
    if not search_paths:
        search_paths = _get_default_search_paths()
    
    databases = []
    
    for search_path in search_paths:
        try:
            path_obj = Path(search_path)
            if not path_obj.exists():
                continue
            
            # Find database files
            for pattern in ["*.db", "*.sqlite", "*.sqlite3"]:
                for db_file in path_obj.rglob(pattern):
                    try:
                        stat = db_file.stat()
                        databases.append(DatabaseInfo(
                            path=str(db_file),
                            type="sqlite",
                            size=stat.st_size,
                            last_modified=datetime.fromtimestamp(stat.st_mtime),
                            readable=os.access(db_file, os.R_OK),
                            writable=os.access(db_file, os.W_OK)
                        ))
                    except Exception as e:
                        logger.warning("Error accessing database file", 
                                     file=str(db_file), error=str(e))
        
        except Exception as e:
            logger.warning("Error scanning path", path=search_path, error=str(e))
    
    logger.info("Database scan completed", count=len(databases))
    return databases


@router.post("/cleanup", response_model=CleanupResult)
@track_database_operation("cleanup", "multiple")
async def cleanup_database(
    cleanup_request: CleanupRequest,
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> CleanupResult:
    """
    Clean database by removing entries matching patterns
    """
    logger.info("Starting database cleanup", 
                database=cleanup_request.database_path,
                dry_run=cleanup_request.dry_run,
                user=current_user["user_id"])
    
    start_time = datetime.utcnow()
    
    # Validate database file
    db_path = Path(cleanup_request.database_path)
    if not db_path.exists():
        raise DatabaseError(f"Database file not found: {cleanup_request.database_path}")
    
    if not db_path.suffix.lower() in ['.db', '.sqlite', '.sqlite3']:
        raise DatabaseError("Only SQLite databases are currently supported")
    
    # Create backup if requested
    backup_path = None
    backup_created = False
    
    if cleanup_request.create_backup and not cleanup_request.dry_run:
        backup_result = await _create_backup(cleanup_request.database_path)
        backup_path = backup_result["backup_path"]
        backup_created = True
    
    # Perform cleanup
    records_found, records_removed = await _perform_cleanup(
        cleanup_request.database_path,
        cleanup_request.patterns,
        cleanup_request.exclude_patterns,
        cleanup_request.dry_run
    )
    
    duration = (datetime.utcnow() - start_time).total_seconds()
    
    result = CleanupResult(
        database_path=cleanup_request.database_path,
        records_found=records_found,
        records_removed=records_removed,
        backup_created=backup_created,
        backup_path=backup_path,
        duration=duration,
        timestamp=datetime.utcnow()
    )
    
    logger.info("Database cleanup completed", 
                records_found=records_found,
                records_removed=records_removed,
                duration=duration)
    
    return result


@router.post("/backup", response_model=BackupResult)
@track_database_operation("backup", "single")
async def backup_database(
    backup_request: BackupRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> BackupResult:
    """
    Create database backup
    """
    logger.info("Starting database backup", 
                database=backup_request.database_path,
                user=current_user["user_id"])
    
    result = await _create_backup(
        backup_request.database_path,
        backup_request.backup_path,
        backup_request.compress
    )
    
    return BackupResult(**result)


@router.get("/patterns", response_model=List[CleanupPattern])
async def get_cleanup_patterns() -> List[CleanupPattern]:
    """
    Get available cleanup patterns
    """
    return [
        CleanupPattern(
            pattern="augment",
            description="Remove entries containing 'augment' (case-insensitive)",
            enabled=True
        ),
        CleanupPattern(
            pattern="Augment",
            description="Remove entries containing 'Augment' (case-sensitive)",
            enabled=True
        ),
        CleanupPattern(
            pattern="telemetry",
            description="Remove telemetry-related entries",
            enabled=False
        ),
        CleanupPattern(
            pattern="extension",
            description="Remove extension-related entries",
            enabled=False
        )
    ]


@router.get("/{database_id}/info", response_model=DatabaseInfo)
async def get_database_info(
    database_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> DatabaseInfo:
    """
    Get detailed information about a specific database
    """
    # In a real implementation, you would decode the database_id
    # For now, treat it as a file path
    db_path = Path(database_id)
    
    if not db_path.exists():
        raise DatabaseError(f"Database not found: {database_id}")
    
    stat = db_path.stat()
    
    return DatabaseInfo(
        path=str(db_path),
        type="sqlite",
        size=stat.st_size,
        last_modified=datetime.fromtimestamp(stat.st_mtime),
        readable=os.access(db_path, os.R_OK),
        writable=os.access(db_path, os.W_OK)
    )


def _get_default_search_paths() -> List[str]:
    """Get default paths to search for databases"""
    import platform
    
    system = platform.system().lower()
    home = Path.home()
    
    if system == "darwin":  # macOS
        return [
            str(home / "Library/Application Support/Code"),
            str(home / "Library/Application Support/Code - Insiders"),
            str(home / ".vscode"),
        ]
    elif system == "windows":
        return [
            str(home / "AppData/Roaming/Code"),
            str(home / "AppData/Roaming/Code - Insiders"),
            str(home / ".vscode"),
        ]
    else:  # Linux
        return [
            str(home / ".config/Code"),
            str(home / ".config/Code - Insiders"),
            str(home / ".vscode"),
        ]


async def _create_backup(
    database_path: str,
    backup_path: Optional[str] = None,
    compress: bool = True
) -> Dict[str, Any]:
    """Create database backup"""
    import shutil
    import gzip
    
    db_path = Path(database_path)
    
    if not backup_path:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{db_path.stem}_backup_{timestamp}{db_path.suffix}"
        backup_path = str(db_path.parent / backup_name)
    
    try:
        if compress and not backup_path.endswith('.gz'):
            backup_path += '.gz'
            
            with open(db_path, 'rb') as f_in:
                with gzip.open(backup_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
        else:
            shutil.copy2(db_path, backup_path)
        
        backup_stat = Path(backup_path).stat()
        
        return {
            "database_path": database_path,
            "backup_path": backup_path,
            "size": backup_stat.st_size,
            "compressed": compress,
            "timestamp": datetime.utcnow()
        }
        
    except Exception as e:
        logger.error("Backup creation failed", error=str(e))
        raise FileOperationError(f"Failed to create backup: {str(e)}")


async def _perform_cleanup(
    database_path: str,
    patterns: List[str],
    exclude_patterns: List[str],
    dry_run: bool
) -> tuple[int, int]:
    """Perform database cleanup operation"""
    import sqlite3
    
    records_found = 0
    records_removed = 0
    
    try:
        conn = sqlite3.connect(database_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        for (table_name,) in tables:
            # Build query to find matching records
            conditions = []
            for pattern in patterns:
                # Simple pattern matching - in production, use more sophisticated matching
                conditions.append(f"CAST(rowid AS TEXT) LIKE '%{pattern}%'")
            
            if conditions:
                query = f"SELECT COUNT(*) FROM {table_name} WHERE " + " OR ".join(conditions)
                cursor.execute(query)
                count = cursor.fetchone()[0]
                records_found += count
                
                if not dry_run and count > 0:
                    delete_query = f"DELETE FROM {table_name} WHERE " + " OR ".join(conditions)
                    cursor.execute(delete_query)
                    records_removed += cursor.rowcount
        
        if not dry_run:
            conn.commit()
        
        conn.close()
        
    except Exception as e:
        logger.error("Database cleanup failed", error=str(e))
        raise DatabaseError(f"Cleanup operation failed: {str(e)}")
    
    return records_found, records_removed
