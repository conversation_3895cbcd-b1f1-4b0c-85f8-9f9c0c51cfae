"""
System Management Endpoints
System state analysis, health checks, and configuration
"""

import platform
import psutil
from datetime import datetime
from typing import Dict, List, Any, Optional

from fastapi import APIRouter, Depends, Query
from pydantic import BaseModel, Field
import structlog

from ....core.config import get_settings
from ....core.database import <PERSON><PERSON>ealthCheck
from ....core.security import get_current_user
from ....core.metrics import track_system_operation

logger = structlog.get_logger(__name__)
settings = get_settings()

router = APIRouter()


class SystemInfo(BaseModel):
    """System information model"""
    platform: str
    architecture: str
    python_version: str
    app_version: str
    environment: str
    uptime: float
    timestamp: datetime


class ProcessInfo(BaseModel):
    """Process information model"""
    pid: int
    name: str
    status: str
    cpu_percent: float
    memory_percent: float
    create_time: datetime


class ResourceUsage(BaseModel):
    """Resource usage model"""
    cpu_percent: float
    memory_total: int
    memory_available: int
    memory_percent: float
    disk_total: int
    disk_used: int
    disk_free: int
    disk_percent: float


class HealthStatus(BaseModel):
    """Health status model"""
    status: str
    timestamp: datetime
    version: str
    environment: str
    database: Dict[str, Any]
    system: ResourceUsage


class SystemState(BaseModel):
    """Complete system state model"""
    info: SystemInfo
    resources: ResourceUsage
    processes: List[ProcessInfo]
    running_applications: List[str]
    warnings: List[str]
    recommendations: List[str]


@router.get("/info", response_model=SystemInfo)
async def get_system_info() -> SystemInfo:
    """
    Get basic system information
    """
    import time
    
    return SystemInfo(
        platform=platform.system(),
        architecture=platform.architecture()[0],
        python_version=platform.python_version(),
        app_version=settings.VERSION,
        environment=settings.ENVIRONMENT,
        uptime=time.time(),  # Placeholder - in production, track actual uptime
        timestamp=datetime.utcnow()
    )


@router.get("/health", response_model=HealthStatus)
async def health_check() -> HealthStatus:
    """
    Comprehensive health check
    """
    # Check database health
    db_healthy = await DatabaseHealthCheck.check_connection()
    db_info = await DatabaseHealthCheck.get_connection_info()
    
    # Get system resources
    resources = _get_resource_usage()
    
    # Determine overall status
    status = "healthy"
    if not db_healthy:
        status = "unhealthy"
    elif resources.cpu_percent > 90 or resources.memory_percent > 90:
        status = "degraded"
    
    return HealthStatus(
        status=status,
        timestamp=datetime.utcnow(),
        version=settings.VERSION,
        environment=settings.ENVIRONMENT,
        database={
            "healthy": db_healthy,
            **db_info
        },
        system=resources
    )


@router.get("/resources", response_model=ResourceUsage)
async def get_resource_usage() -> ResourceUsage:
    """
    Get current system resource usage
    """
    return _get_resource_usage()


@router.get("/processes", response_model=List[ProcessInfo])
async def get_processes(
    limit: int = Query(default=50, ge=1, le=500),
    sort_by: str = Query(default="cpu_percent", regex="^(cpu_percent|memory_percent|name)$")
) -> List[ProcessInfo]:
    """
    Get running processes information
    """
    processes = []
    
    try:
        for proc in psutil.process_iter(['pid', 'name', 'status', 'cpu_percent', 'memory_percent', 'create_time']):
            try:
                proc_info = proc.info
                processes.append(ProcessInfo(
                    pid=proc_info['pid'],
                    name=proc_info['name'] or 'Unknown',
                    status=proc_info['status'],
                    cpu_percent=proc_info['cpu_percent'] or 0.0,
                    memory_percent=proc_info['memory_percent'] or 0.0,
                    create_time=datetime.fromtimestamp(proc_info['create_time'])
                ))
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
    except Exception as e:
        logger.warning("Error getting process information", error=str(e))
    
    # Sort processes
    if sort_by == "cpu_percent":
        processes.sort(key=lambda x: x.cpu_percent, reverse=True)
    elif sort_by == "memory_percent":
        processes.sort(key=lambda x: x.memory_percent, reverse=True)
    else:
        processes.sort(key=lambda x: x.name)
    
    return processes[:limit]


@router.get("/state", response_model=SystemState)
@track_system_operation("system_state_analysis")
async def analyze_system_state(
    target_apps: List[str] = Query(default=["code", "vscode"], description="Applications to monitor"),
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> SystemState:
    """
    Comprehensive system state analysis
    """
    logger.info("Starting system state analysis", user=current_user["user_id"])
    
    # Get system info
    info = await get_system_info()
    
    # Get resource usage
    resources = _get_resource_usage()
    
    # Get processes
    processes = await get_processes(limit=100)
    
    # Detect running applications
    running_apps = _detect_running_applications(processes, target_apps)
    
    # Generate warnings and recommendations
    warnings = _generate_warnings(resources, running_apps)
    recommendations = _generate_recommendations(resources, running_apps)
    
    logger.info("System state analysis completed", 
                running_apps=len(running_apps),
                warnings=len(warnings),
                recommendations=len(recommendations))
    
    return SystemState(
        info=info,
        resources=resources,
        processes=processes,
        running_applications=running_apps,
        warnings=warnings,
        recommendations=recommendations
    )


@router.post("/cleanup")
@track_system_operation("system_cleanup")
async def cleanup_system(
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Perform system cleanup operations
    """
    logger.info("Starting system cleanup", user=current_user["user_id"])
    
    cleanup_results = {
        "temp_files_cleaned": 0,
        "cache_cleared": False,
        "logs_rotated": False,
        "timestamp": datetime.utcnow()
    }
    
    # Placeholder cleanup operations
    # In a real implementation, you would:
    # - Clean temporary files
    # - Clear application caches
    # - Rotate log files
    # - Optimize database
    
    logger.info("System cleanup completed", results=cleanup_results)
    
    return cleanup_results


def _get_resource_usage() -> ResourceUsage:
    """Get current system resource usage"""
    # CPU usage
    cpu_percent = psutil.cpu_percent(interval=1)
    
    # Memory usage
    memory = psutil.virtual_memory()
    
    # Disk usage
    disk = psutil.disk_usage('/')
    
    return ResourceUsage(
        cpu_percent=cpu_percent,
        memory_total=memory.total,
        memory_available=memory.available,
        memory_percent=memory.percent,
        disk_total=disk.total,
        disk_used=disk.used,
        disk_free=disk.free,
        disk_percent=(disk.used / disk.total) * 100
    )


def _detect_running_applications(processes: List[ProcessInfo], target_apps: List[str]) -> List[str]:
    """Detect running target applications"""
    running_apps = []
    
    for process in processes:
        process_name = process.name.lower()
        for app in target_apps:
            if app.lower() in process_name:
                if app not in running_apps:
                    running_apps.append(app)
    
    return running_apps


def _generate_warnings(resources: ResourceUsage, running_apps: List[str]) -> List[str]:
    """Generate system warnings"""
    warnings = []
    
    if resources.cpu_percent > 80:
        warnings.append(f"High CPU usage: {resources.cpu_percent:.1f}%")
    
    if resources.memory_percent > 80:
        warnings.append(f"High memory usage: {resources.memory_percent:.1f}%")
    
    if resources.disk_percent > 90:
        warnings.append(f"Low disk space: {resources.disk_percent:.1f}% used")
    
    if "code" in running_apps or "vscode" in running_apps:
        warnings.append("VS Code is running - close it before performing database operations")
    
    return warnings


def _generate_recommendations(resources: ResourceUsage, running_apps: List[str]) -> List[str]:
    """Generate system recommendations"""
    recommendations = []
    
    if resources.memory_percent > 70:
        recommendations.append("Consider closing unnecessary applications to free memory")
    
    if resources.disk_percent > 80:
        recommendations.append("Clean up disk space before performing backup operations")
    
    if running_apps:
        recommendations.append("Close target applications before running cleanup operations")
    
    recommendations.append("Create a backup before performing any destructive operations")
    
    return recommendations
