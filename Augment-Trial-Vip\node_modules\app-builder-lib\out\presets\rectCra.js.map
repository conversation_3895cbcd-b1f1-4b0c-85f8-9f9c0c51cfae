{"version": 3, "file": "rectCra.js", "sourceRoot": "", "sources": ["../../src/presets/rectCra.ts"], "names": [], "mappings": ";;AAKA,4BAeC;AApBD,+CAA8C;AAC9C,6BAA4B;AAG5B,gBAAgB;AACT,KAAK,UAAU,QAAQ,CAAC,UAAkB;IAC/C,IAAI,CAAC,MAAM,IAAA,yBAAU,EAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;QAC/E,uCAAuC;QACvC,kBAAG,CAAC,IAAI,CAAC,8IAA8I,CAAC,CAAA;IAC1J,CAAC;IAED,OAAO;QACL,WAAW,EAAE;YACX,cAAc,EAAE,QAAQ;SACzB;QACD,KAAK,EAAE,CAAC,YAAY,CAAC;QACrB,aAAa,EAAE;YACb,IAAI,EAAE,mBAAmB;SAC1B;KACF,CAAA;AACH,CAAC", "sourcesContent": ["import { log, statOrNull } from \"builder-util\"\nimport * as path from \"path\"\nimport { Configuration } from \"../configuration\"\n\n/** @internal */\nexport async function reactCra(projectDir: string): Promise<Configuration> {\n  if ((await statOrNull(path.join(projectDir, \"public\", \"electron.js\"))) == null) {\n    // noinspection SpellCheckingInspection\n    log.warn(\"public/electron.js not found. Please see https://medium.com/@kitze/%EF%B8%8F-from-react-to-an-electron-app-ready-for-production-a0468ecb1da3\")\n  }\n\n  return {\n    directories: {\n      buildResources: \"assets\",\n    },\n    files: [\"build/**/*\"],\n    extraMetadata: {\n      main: \"build/electron.js\",\n    },\n  }\n}\n"]}