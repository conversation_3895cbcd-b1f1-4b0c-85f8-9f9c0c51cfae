import React, { forwardRef } from "react";
import { cn } from "@/utils/cn";

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Card variant
   * @default "default"
   */
  variant?: "default" | "outlined" | "elevated" | "ghost";
  
  /**
   * Whether the card is interactive (hover effects)
   * @default false
   */
  interactive?: boolean;
  
  /**
   * Padding size
   * @default "md"
   */
  padding?: "none" | "sm" | "md" | "lg" | "xl";
}

/**
 * Card component for grouping related content
 */
const Card = forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant = "default", interactive = false, padding = "md", ...props }, ref) => {
    const variantStyles = {
      default: "bg-white dark:bg-dark-800 border border-neutral-200 dark:border-dark-700",
      outlined: "bg-transparent border-2 border-neutral-300 dark:border-dark-600",
      elevated: "bg-white dark:bg-dark-800 shadow-medium border-0",
      ghost: "bg-neutral-50 dark:bg-dark-900 border-0",
    };

    const paddingStyles = {
      none: "",
      sm: "p-3",
      md: "p-4",
      lg: "p-6",
      xl: "p-8",
    };

    return (
      <div
        ref={ref}
        className={cn(
          // Base styles
          "rounded-lg transition-colors",
          // Variant styles
          variantStyles[variant],
          // Padding styles
          paddingStyles[padding],
          // Interactive styles
          interactive && "cursor-pointer hover:shadow-soft dark:hover:bg-dark-700/50",
          // Custom classes
          className
        )}
        {...props}
      />
    );
  }
);

Card.displayName = "Card";

/**
 * Card Header component
 */
const CardHeader = forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn("flex flex-col space-y-1.5 pb-4", className)}
      {...props}
    />
  )
);

CardHeader.displayName = "CardHeader";

/**
 * Card Title component
 */
const CardTitle = forwardRef<HTMLHeadingElement, React.HTMLAttributes<HTMLHeadingElement>>(
  ({ className, ...props }, ref) => (
    <h3
      ref={ref}
      className={cn(
        "text-lg font-semibold leading-none tracking-tight text-neutral-900 dark:text-neutral-100",
        className
      )}
      {...props}
    />
  )
);

CardTitle.displayName = "CardTitle";

/**
 * Card Description component
 */
const CardDescription = forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLParagraphElement>>(
  ({ className, ...props }, ref) => (
    <p
      ref={ref}
      className={cn("text-sm text-neutral-600 dark:text-neutral-400", className)}
      {...props}
    />
  )
);

CardDescription.displayName = "CardDescription";

/**
 * Card Content component
 */
const CardContent = forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn("", className)} {...props} />
  )
);

CardContent.displayName = "CardContent";

/**
 * Card Footer component
 */
const CardFooter = forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn("flex items-center pt-4", className)}
      {...props}
    />
  )
);

CardFooter.displayName = "CardFooter";

export { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter };
