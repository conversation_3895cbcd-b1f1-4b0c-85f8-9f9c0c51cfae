
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 300px;
      padding: 16px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
    }
    .header {
      text-align: center;
      margin-bottom: 16px;
    }
    .logo {
      font-size: 18px;
      font-weight: bold;
      color: #2563eb;
      margin-bottom: 4px;
    }
    .subtitle {
      font-size: 12px;
      color: #6b7280;
    }
    .status {
      background: #f3f4f6;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 16px;
    }
    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }
    .status-item:last-child {
      margin-bottom: 0;
    }
    .status-label {
      font-size: 14px;
      color: #374151;
    }
    .status-value {
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 4px;
      font-weight: 500;
    }
    .status-active {
      background: #dcfce7;
      color: #166534;
    }
    .status-inactive {
      background: #fee2e2;
      color: #991b1b;
    }
    .footer {
      text-align: center;
      font-size: 11px;
      color: #9ca3af;
      border-top: 1px solid #e5e7eb;
      padding-top: 12px;
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="logo">🛡️ Augment VIP</div>
    <div class="subtitle">Privacy Protection Active</div>
  </div>
  
  <div class="status">
    <div class="status-item">
      <span class="status-label">Fingerprint Protection</span>
      <span class="status-value status-active">Active</span>
    </div>
    <div class="status-item">
      <span class="status-label">Tracking Blocked</span>
      <span class="status-value status-active">Active</span>
    </div>
    <div class="status-item">
      <span class="status-label">Canvas Noise</span>
      <span class="status-value status-active">Active</span>
    </div>
    <div class="status-item">
      <span class="status-label">WebGL Spoofing</span>
      <span class="status-value status-active">Active</span>
    </div>
  </div>
  
  <div class="footer">
    Generated by Augment VIP v2.0.0
  </div>
</body>
</html>
