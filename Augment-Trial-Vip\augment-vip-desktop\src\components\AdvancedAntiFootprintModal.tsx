import React, { useState } from 'react';
import {
  Shield,
  Globe,
  Eye,
  X,
  Play,
  Loader,
  CheckCircle,
  AlertTriangle,
  Lock,
  Fingerprint,
  Network,
  Settings,
  FolderOpen,
  Copy
} from 'lucide-react';

interface AdvancedAntiFootprintModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface DomainBlockingResults {
  analysis_metadata?: any;
  current_status?: any;
  proposed_changes?: any;
  safety_checks?: any;
  warnings?: string[];
}

interface FingerprintResults {
  analysis_metadata?: any;
  fingerprint_vectors?: any;
  privacy_risk_assessment?: any;
  spoofing_opportunities?: any[];
  recommendations?: string[];
}

const AdvancedAntiFootprintModal: React.FC<AdvancedAntiFootprintModalProps> = ({ isOpen, onClose }) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [activeTab, setActiveTab] = useState<'domain-blocking' | 'fingerprint-spoofing' | 'network-protection'>('domain-blocking');
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [domainResults, setDomainResults] = useState<DomainBlockingResults | null>(null);
  const [fingerprintResults, setFingerprintResults] = useState<FingerprintResults | null>(null);
  const [networkResults, setNetworkResults] = useState<any>(null);

  // Helper function to parse IPC response
  const parseIPCResponse = (result: any): any => {
    if (!result.success) {
      throw new Error(result.stderr || 'Command failed');
    }

    // Handle different response formats
    let outputText = '';
    if (result.stdout) {
      outputText = result.stdout;
    } else if (typeof result.output === 'string') {
      outputText = result.output;
    } else if (result.data) {
      outputText = typeof result.data === 'string' ? result.data : JSON.stringify(result.data);
    } else {
      throw new Error('No output data found in response');
    }

    // Find the JSON part - it starts with { and ends with }
    const jsonStart = outputText.indexOf('{');
    const jsonEnd = outputText.lastIndexOf('}');

    if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
      const jsonText = outputText.substring(jsonStart, jsonEnd + 1);
      try {
        return JSON.parse(jsonText);
      } catch (parseError) {
        console.error('JSON parse error:', parseError);
        console.error('JSON text:', jsonText.substring(0, 200) + '...');
        throw new Error('Failed to parse JSON response');
      }
    } else {
      // Fallback: try to find JSON line by line
      const lines = outputText.split('\n').filter((line: string) => line.trim());
      const jsonLine = lines.find((line: string) => line.trim().startsWith('{'));

      if (jsonLine) {
        try {
          return JSON.parse(jsonLine.trim());
        } catch (parseError) {
          console.error('Line JSON parse error:', parseError);
          throw new Error('Failed to parse JSON line');
        }
      } else {
        console.error('No JSON found in output:', outputText.substring(0, 200) + '...');
        throw new Error('No valid JSON output found');
      }
    }
  };

  const analyzeDomainBlocking = async () => {
    setIsProcessing(true);
    setError(null);

    try {
      const result = await (window as any).electronAPI.executePythonCommand('analyze-domain-blocking', ['--json']);
      const parsedResult = parseIPCResponse(result);
      setDomainResults(parsedResult);
    } catch (err) {
      console.error('Domain blocking analysis error:', err);
      setError('Failed to analyze domain blocking: ' + (err as Error).message);
    } finally {
      setIsProcessing(false);
    }
  };

  const previewDomainBlocking = async () => {
    setIsProcessing(true);
    setError(null);

    try {
      const result = await (window as any).electronAPI.executePythonCommand('preview-domain-blocking', ['--json']);
      const parsedResult = parseIPCResponse(result);
      setDomainResults(parsedResult);
    } catch (err) {
      console.error('Domain blocking preview error:', err);
      setError('Failed to preview domain blocking: ' + (err as Error).message);
    } finally {
      setIsProcessing(false);
    }
  };

  const analyzeFingerprintExposure = async () => {
    setIsProcessing(true);
    setError(null);

    try {
      const result = await (window as any).electronAPI.executePythonCommand('analyze-fingerprint', ['--browser', 'chrome', '--json']);
      const parsedResult = parseIPCResponse(result);
      setFingerprintResults(parsedResult);
    } catch (err) {
      console.error('Fingerprint analysis error:', err);
      setError('Failed to analyze fingerprint: ' + (err as Error).message);
    } finally {
      setIsProcessing(false);
    }
  };

  const generateSpoofingProfile = async () => {
    setIsProcessing(true);
    setError(null);

    try {
      // First generate the spoofing profile
      const profileResult = await (window as any).electronAPI.executePythonCommand('generate-spoofing-profile', ['--profile-type', 'stealth', '--browser', 'chrome', '--json']);
      const parsedProfile = parseIPCResponse(profileResult);

      // Then create the browser extension
      const extensionResult = await (window as any).electronAPI.executePythonCommand('create-spoofing-extension', ['--browser', 'chrome', '--profile-id', parsedProfile.profile_metadata?.profile_id, '--json']);
      const parsedExtension = parseIPCResponse(extensionResult);

      // Store both results for display
      setFingerprintResults(prev => ({
        ...prev,
        generated_profile: parsedProfile,
        extension_files: parsedExtension
      }));
    } catch (err) {
      console.error('Spoofing profile generation error:', err);
      setError('Failed to generate spoofing profile: ' + (err as Error).message);
    } finally {
      setIsProcessing(false);
    }
  };

  const applyDomainBlocking = async () => {
    setIsProcessing(true);
    setError(null);
    setSuccessMessage(null);

    try {
      const result = await (window as any).electronAPI.executePythonCommand('apply-domain-blocking', ['--use-detected', '--backup', '--json']);
      const parsedResult = parseIPCResponse(result);

      // Show success message and refresh the analysis
      if (parsedResult.operation_result?.success) {
        const blockedCount = parsedResult.changes_made?.domains_blocked?.length || 0;
        setSuccessMessage(`✅ Successfully blocked ${blockedCount} tracking domains! Backup created at: ${parsedResult.operation_metadata?.backup_path}`);
        // Refresh the domain blocking analysis to show updated status
        await analyzeDomainBlocking();
      } else {
        setError('Domain blocking failed: ' + (parsedResult.operation_result?.error_message || 'Unknown error'));
      }
    } catch (err) {
      console.error('Domain blocking application error:', err);
      setError('Failed to apply domain blocking: ' + (err as Error).message);
    } finally {
      setIsProcessing(false);
    }
  };

  const analyzeNetworkProtection = async () => {
    setIsProcessing(true);
    setError(null);

    try {
      const result = await (window as any).electronAPI.executePythonCommand('analyze-network-protection', ['--scan-dns', '--scan-vpn', '--scan-proxy', '--json']);
      const parsedResult = parseIPCResponse(result);
      setNetworkResults(parsedResult);
    } catch (err) {
      console.error('Network protection analysis error:', err);
      setError('Failed to analyze network protection: ' + (err as Error).message);
    } finally {
      setIsProcessing(false);
    }
  };

  const enableNetworkProtection = async () => {
    setIsProcessing(true);
    setError(null);
    setSuccessMessage(null);

    try {
      const result = await (window as any).electronAPI.executePythonCommand('enable-network-protection', ['--dns-filtering', '--vpn-detection', '--proxy-detection', '--json']);
      const parsedResult = parseIPCResponse(result);

      if (parsedResult.operation_result?.success) {
        setSuccessMessage(`✅ Network protection enabled! Protected against ${parsedResult.protection_features?.length || 0} threat vectors.`);
        await analyzeNetworkProtection();
      } else {
        setError('Network protection failed: ' + (parsedResult.operation_result?.error_message || 'Unknown error'));
      }
    } catch (err) {
      console.error('Network protection error:', err);
      setError('Failed to enable network protection: ' + (err as Error).message);
    } finally {
      setIsProcessing(false);
    }
  };

  const getPrivacyRiskColor = (score: number) => {
    if (score >= 70) return 'text-red-600';
    if (score >= 40) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getPrivacyRiskIcon = (score: number) => {
    if (score >= 70) return <AlertTriangle className="w-5 h-5 text-red-600" />;
    if (score >= 40) return <AlertTriangle className="w-5 h-5 text-yellow-600" />;
    return <CheckCircle className="w-5 h-5 text-green-600" />;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-5xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-indigo-600 to-purple-600 text-white">
          <div className="flex items-center space-x-3">
            <Shield className="w-6 h-6" />
            <h2 className="text-xl font-semibold">Advanced Anti-Footprint Features</h2>
          </div>
          <button
            onClick={onClose}
            className="text-white hover:text-gray-200 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Tabs */}
          <div className="border-b border-gray-200 mb-6">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('domain-blocking')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'domain-blocking'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <Lock className="w-4 h-4" />
                  <span>Smart Domain Blocking</span>
                </div>
              </button>
              <button
                onClick={() => setActiveTab('fingerprint-spoofing')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'fingerprint-spoofing'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <Fingerprint className="w-4 h-4" />
                  <span>Fingerprint Spoofing</span>
                </div>
              </button>
              <button
                onClick={() => setActiveTab('network-protection')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'network-protection'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <Network className="w-4 h-4" />
                  <span>Network Protection</span>
                </div>
              </button>
            </nav>
          </div>

          {/* Error Display */}
          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center space-x-2 text-red-800">
                <AlertTriangle className="w-5 h-5" />
                <span className="font-medium">Error</span>
              </div>
              <p className="mt-1 text-red-700">{error}</p>
            </div>
          )}

          {/* Success Display */}
          {successMessage && (
            <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center space-x-2 text-green-800">
                <CheckCircle className="w-5 h-5" />
                <span className="font-medium">Success</span>
              </div>
              <p className="mt-1 text-green-700">{successMessage}</p>
            </div>
          )}

          {/* Tab Content */}
          <div className="space-y-6">
            {activeTab === 'domain-blocking' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Smart Domain Blocking</h3>
                <p className="text-gray-600 mb-6">
                  Block tracking domains safely with automatic backups and easy restoration.
                </p>

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-3 mb-6">
                  <button
                    onClick={analyzeDomainBlocking}
                    disabled={isProcessing}
                    className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {isProcessing ? <Loader className="w-4 h-4 animate-spin" /> : <Eye className="w-4 h-4" />}
                    <span>Analyze Current Blocking</span>
                  </button>
                  
                  <button
                    onClick={previewDomainBlocking}
                    disabled={isProcessing}
                    className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {isProcessing ? <Loader className="w-4 h-4 animate-spin" /> : <Play className="w-4 h-4" />}
                    <span>Preview Blocking Changes</span>
                  </button>

                  {domainResults?.proposed_changes?.domains_to_add?.length > 0 && (
                    <button
                      onClick={applyDomainBlocking}
                      disabled={isProcessing}
                      className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      {isProcessing ? <Loader className="w-4 h-4 animate-spin" /> : <Shield className="w-4 h-4" />}
                      <span>Apply Domain Blocking</span>
                    </button>
                  )}
                </div>

                {/* Domain Blocking Results */}
                {domainResults && (
                  <div className="space-y-4">
                    {domainResults.current_status && (
                      <div className="bg-gray-50 rounded-lg p-4">
                        <h4 className="font-medium text-gray-900 mb-3">Current Blocking Status</h4>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                          <div className="text-center">
                            <div className="text-2xl font-bold text-blue-600">
                              {domainResults.current_status.total_blocked_domains || 0}
                            </div>
                            <div className="text-sm text-gray-600">Total Blocked</div>
                          </div>
                          <div className="text-center">
                            <div className="text-2xl font-bold text-green-600">
                              {domainResults.current_status.augment_blocked_domains || 0}
                            </div>
                            <div className="text-sm text-gray-600">Augment Managed</div>
                          </div>
                          <div className="text-center">
                            <div className="text-2xl font-bold text-purple-600">
                              {domainResults.current_status.other_blocked_domains || 0}
                            </div>
                            <div className="text-sm text-gray-600">Other Entries</div>
                          </div>
                          <div className="text-center">
                            <div className={`text-2xl font-bold ${domainResults.current_status.is_writable ? 'text-green-600' : 'text-red-600'}`}>
                              {domainResults.current_status.is_writable ? 'Yes' : 'No'}
                            </div>
                            <div className="text-sm text-gray-600">Writable</div>
                          </div>
                        </div>
                      </div>
                    )}

                    {domainResults.proposed_changes && (
                      <div className="bg-blue-50 rounded-lg p-4">
                        <h4 className="font-medium text-gray-900 mb-3">Proposed Changes</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Domains to add:</span>
                            <span className="font-medium text-green-600">
                              {domainResults.proposed_changes.domains_to_add?.length || 0}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Domains to remove:</span>
                            <span className="font-medium text-red-600">
                              {domainResults.proposed_changes.domains_to_remove?.length || 0}
                            </span>
                          </div>
                        </div>
                        
                        {domainResults.proposed_changes.domains_to_add?.length > 0 && (
                          <div className="mt-3">
                            <span className="text-sm text-gray-600">Domains to block:</span>
                            <div className="mt-1 flex flex-wrap gap-1">
                              {domainResults.proposed_changes.domains_to_add.slice(0, 5).map((domain: string, index: number) => (
                                <span key={index} className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded">
                                  {domain}
                                </span>
                              ))}
                              {domainResults.proposed_changes.domains_to_add.length > 5 && (
                                <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                                  +{domainResults.proposed_changes.domains_to_add.length - 5} more
                                </span>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {activeTab === 'fingerprint-spoofing' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Browser Fingerprint Spoofing</h3>
                <p className="text-gray-600 mb-6">
                  Analyze and spoof browser fingerprints to prevent tracking and enhance privacy.
                </p>

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-3 mb-6">
                  <button
                    onClick={analyzeFingerprintExposure}
                    disabled={isProcessing}
                    className="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {isProcessing ? <Loader className="w-4 h-4 animate-spin" /> : <Fingerprint className="w-4 h-4" />}
                    <span>Analyze Fingerprint Exposure</span>
                  </button>
                  
                  <button
                    onClick={generateSpoofingProfile}
                    disabled={isProcessing}
                    className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {isProcessing ? <Loader className="w-4 h-4 animate-spin" /> : <Settings className="w-4 h-4" />}
                    <span>Generate Spoofing Profile</span>
                  </button>
                </div>

                {/* Fingerprint Results */}
                {fingerprintResults && (
                  <div className="space-y-4">
                    {fingerprintResults.privacy_risk_assessment && (
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-medium text-gray-900">Privacy Risk Assessment</h4>
                          <div className="flex items-center space-x-2">
                            {getPrivacyRiskIcon(fingerprintResults.privacy_risk_assessment.overall_risk_score)}
                            <span className={`text-lg font-bold ${getPrivacyRiskColor(fingerprintResults.privacy_risk_assessment.overall_risk_score)}`}>
                              {Math.round(fingerprintResults.privacy_risk_assessment.overall_risk_score)}/100
                            </span>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="text-center p-3 bg-red-50 rounded">
                            <div className="text-lg font-bold text-red-600">
                              {fingerprintResults.privacy_risk_assessment.high_risk_vectors?.length || 0}
                            </div>
                            <div className="text-sm text-red-700">High Risk Vectors</div>
                          </div>
                          <div className="text-center p-3 bg-yellow-50 rounded">
                            <div className="text-lg font-bold text-yellow-600">
                              {fingerprintResults.privacy_risk_assessment.medium_risk_vectors?.length || 0}
                            </div>
                            <div className="text-sm text-yellow-700">Medium Risk Vectors</div>
                          </div>
                          <div className="text-center p-3 bg-green-50 rounded">
                            <div className="text-lg font-bold text-green-600">
                              {fingerprintResults.privacy_risk_assessment.low_risk_vectors?.length || 0}
                            </div>
                            <div className="text-sm text-green-700">Low Risk Vectors</div>
                          </div>
                        </div>
                      </div>
                    )}

                    {fingerprintResults.spoofing_opportunities && fingerprintResults.spoofing_opportunities.length > 0 && (
                      <div className="bg-blue-50 rounded-lg p-4">
                        <h4 className="font-medium text-gray-900 mb-3">Spoofing Opportunities</h4>
                        <div className="space-y-2">
                          {fingerprintResults.spoofing_opportunities.map((opportunity: any, index: number) => (
                            <div key={index} className="flex items-center justify-between p-2 bg-white rounded border">
                              <div>
                                <span className="font-medium text-gray-900 capitalize">
                                  {opportunity.vector.replace('_', ' ')}
                                </span>
                                <span className={`ml-2 px-2 py-1 text-xs rounded ${
                                  opportunity.priority === 'high' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'
                                }`}>
                                  {opportunity.priority} priority
                                </span>
                              </div>
                              <div className="text-sm text-gray-600">
                                {opportunity.effectiveness} effectiveness
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {(fingerprintResults as any)?.generated_profile && (
                      <div className="bg-green-50 rounded-lg p-4">
                        <h4 className="font-medium text-gray-900 mb-3">Generated Spoofing Profile</h4>
                        <div className="space-y-3">
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <span className="text-gray-600">Profile Type:</span>
                              <p className="font-medium capitalize">{(fingerprintResults as any).generated_profile.profile_metadata?.profile_type}</p>
                            </div>
                            <div>
                              <span className="text-gray-600">Target Browser:</span>
                              <p className="font-medium capitalize">{(fingerprintResults as any).generated_profile.profile_metadata?.target_browser}</p>
                            </div>
                            <div>
                              <span className="text-gray-600">Profile ID:</span>
                              <p className="font-medium text-xs">{(fingerprintResults as any).generated_profile.profile_metadata?.profile_id}</p>
                            </div>
                            <div>
                              <span className="text-gray-600">Consistency Score:</span>
                              <p className="font-medium text-green-600">{(fingerprintResults as any).generated_profile.profile_consistency?.consistency_score}/100</p>
                            </div>
                          </div>

                          <div className="bg-white rounded p-3 border">
                            <h5 className="font-medium text-gray-900 mb-2">Spoofed Attributes</h5>
                            <div className="space-y-2 text-sm">
                              <div>
                                <span className="text-gray-600">User Agent:</span>
                                <p className="font-mono text-xs bg-gray-100 p-1 rounded mt-1">
                                  {(fingerprintResults as any).generated_profile.spoofed_attributes?.user_agent?.substring(0, 80)}...
                                </p>
                              </div>
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <span className="text-gray-600">Screen Resolution:</span>
                                  <p className="font-medium">
                                    {(fingerprintResults as any).generated_profile.spoofed_attributes?.screen_resolution?.width} x {(fingerprintResults as any).generated_profile.spoofed_attributes?.screen_resolution?.height}
                                  </p>
                                </div>
                                <div>
                                  <span className="text-gray-600">Timezone:</span>
                                  <p className="font-medium">{(fingerprintResults as any).generated_profile.spoofed_attributes?.timezone}</p>
                                </div>
                              </div>
                            </div>
                          </div>

                          <div className="bg-blue-100 rounded p-3">
                            <h5 className="font-medium text-blue-900 mb-2">🛡️ Protection Features</h5>
                            <div className="grid grid-cols-2 gap-2 text-sm">
                              <div className="flex items-center space-x-2">
                                <CheckCircle className="w-4 h-4 text-green-600" />
                                <span>Canvas Noise Injection</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <CheckCircle className="w-4 h-4 text-green-600" />
                                <span>WebGL Spoofing</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <CheckCircle className="w-4 h-4 text-green-600" />
                                <span>Font Masking</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <CheckCircle className="w-4 h-4 text-green-600" />
                                <span>User-Agent Rotation</span>
                              </div>
                            </div>
                          </div>

                          {(fingerprintResults as any)?.extension_files && (
                            <div className="bg-blue-50 rounded p-3">
                              <h5 className="font-medium text-blue-900 mb-2">📦 Browser Extension Files</h5>
                              <div className="space-y-2">
                                <div className="text-sm text-blue-800">
                                  <strong>Extension Path:</strong> {(fingerprintResults as any).extension_files.extension_path}
                                </div>
                                <div className="flex flex-wrap gap-2">
                                  {(fingerprintResults as any).extension_files.files_created?.map((file: string, index: number) => (
                                    <span key={index} className="px-2 py-1 bg-white rounded text-xs font-mono border">
                                      {file.split('/').pop()}
                                    </span>
                                  ))}
                                </div>
                                <div className="flex space-x-2 mt-3">
                                  <button
                                    onClick={async () => {
                                      try {
                                        await (window as any).electronAPI.openPath((fingerprintResults as any).extension_files.extension_path);
                                      } catch (err) {
                                        console.error('Failed to open path:', err);
                                        alert(`Extension path: ${(fingerprintResults as any).extension_files.extension_path}`);
                                      }
                                    }}
                                    className="flex items-center space-x-1 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                                  >
                                    <FolderOpen className="w-4 h-4" />
                                    <span>Open Extension Folder</span>
                                  </button>
                                  <button
                                    onClick={async () => {
                                      try {
                                        await (window as any).electronAPI.copyToClipboard((fingerprintResults as any).extension_files.extension_path);
                                        alert('Path copied to clipboard!');
                                      } catch (err) {
                                        console.error('Failed to copy to clipboard:', err);
                                        alert(`Extension path: ${(fingerprintResults as any).extension_files.extension_path}`);
                                      }
                                    }}
                                    className="flex items-center space-x-1 px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
                                  >
                                    <Copy className="w-4 h-4" />
                                    <span>Copy Path</span>
                                  </button>
                                </div>
                              </div>
                            </div>
                          )}

                          <div className="bg-yellow-50 rounded p-3">
                            <h5 className="font-medium text-yellow-900 mb-2">📋 Installation Instructions</h5>
                            <ol className="list-decimal list-inside text-sm text-yellow-800 space-y-1">
                              <li>Open Chrome and go to <code className="bg-yellow-100 px-1 rounded">chrome://extensions/</code></li>
                              <li>Enable "Developer mode" (toggle in top-right)</li>
                              <li>Click "Load unpacked" and select the extension folder above</li>
                              <li>The fingerprint spoofing extension will be installed and active</li>
                              <li>Test on <code className="bg-yellow-100 px-1 rounded">browserleaks.com</code> or <code className="bg-yellow-100 px-1 rounded">amiunique.org</code></li>
                            </ol>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {activeTab === 'network-protection' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Network Protection</h3>
                <p className="text-gray-600 mb-6">
                  Real-time network monitoring and traffic analysis for enhanced privacy protection.
                </p>

                <div className="flex space-x-4 mb-6">
                  <button
                    onClick={analyzeNetworkProtection}
                    disabled={isProcessing}
                    className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {isProcessing ? <Loader className="w-4 h-4 animate-spin" /> : <Eye className="w-4 h-4" />}
                    <span>Analyze Network Security</span>
                  </button>

                  {networkResults && (
                    <button
                      onClick={enableNetworkProtection}
                      disabled={isProcessing}
                      className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      {isProcessing ? <Loader className="w-4 h-4 animate-spin" /> : <Shield className="w-4 h-4" />}
                      <span>Enable Network Protection</span>
                    </button>
                  )}
                </div>

                {/* Network Results */}
                {networkResults && (
                  <div className="space-y-4">
                    {networkResults.network_security_assessment && (
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-medium text-gray-900">Network Security Assessment</h4>
                          <div className="flex items-center space-x-2">
                            <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                              networkResults.network_security_assessment.security_level === 'high' ? 'bg-green-100 text-green-800' :
                              networkResults.network_security_assessment.security_level === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {networkResults.network_security_assessment.security_level} security
                            </div>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="text-center p-3 bg-blue-50 rounded">
                            <div className="text-lg font-bold text-blue-600">
                              {networkResults.network_security_assessment.dns_protection?.enabled ? 'ON' : 'OFF'}
                            </div>
                            <div className="text-sm text-gray-600">DNS Protection</div>
                          </div>
                          <div className="text-center p-3 bg-purple-50 rounded">
                            <div className="text-lg font-bold text-purple-600">
                              {networkResults.network_security_assessment.vpn_detection?.active ? 'ACTIVE' : 'INACTIVE'}
                            </div>
                            <div className="text-sm text-gray-600">VPN Detection</div>
                          </div>
                          <div className="text-center p-3 bg-green-50 rounded">
                            <div className="text-lg font-bold text-green-600">
                              {networkResults.network_security_assessment.proxy_detection?.enabled ? 'ON' : 'OFF'}
                            </div>
                            <div className="text-sm text-gray-600">Proxy Detection</div>
                          </div>
                        </div>
                      </div>
                    )}

                    {networkResults.threat_vectors && networkResults.threat_vectors.length > 0 && (
                      <div className="bg-red-50 rounded-lg p-4">
                        <h4 className="font-medium text-gray-900 mb-3">Detected Network Threats</h4>
                        <div className="space-y-2">
                          {networkResults.threat_vectors.map((threat: any, index: number) => (
                            <div key={index} className="flex items-center justify-between p-2 bg-white rounded border">
                              <div>
                                <span className="font-medium text-gray-900">
                                  {threat.threat_type}
                                </span>
                                <p className="text-sm text-gray-600">{threat.description}</p>
                              </div>
                              <span className={`px-2 py-1 text-xs rounded ${
                                threat.severity === 'high' ? 'bg-red-100 text-red-800' :
                                threat.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-blue-100 text-blue-800'
                              }`}>
                                {threat.severity} risk
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {networkResults.protection_recommendations && (
                      <div className="bg-blue-50 rounded-lg p-4">
                        <h4 className="font-medium text-blue-900 mb-3">🛡️ Protection Recommendations</h4>
                        <ul className="space-y-2 text-sm text-blue-800">
                          {networkResults.protection_recommendations.map((rec: string, index: number) => (
                            <li key={index} className="flex items-start space-x-2">
                              <CheckCircle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                              <span>{rec}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                )}

                {!networkResults && (
                  <div className="bg-gray-50 rounded-lg p-6 text-center">
                    <Network className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <h4 className="font-medium text-gray-900 mb-2">Network Analysis Required</h4>
                    <p className="text-gray-600 mb-4">
                      Click "Analyze Network Security" to scan for network vulnerabilities and protection opportunities.
                    </p>
                    <div className="text-sm text-gray-500">
                      Analysis includes:
                      <ul className="mt-2 space-y-1">
                        <li>• DNS leak detection</li>
                        <li>• VPN/Proxy status verification</li>
                        <li>• Network traffic monitoring</li>
                        <li>• Real-time threat detection</li>
                      </ul>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Processing State */}
          {isProcessing && (
            <div className="mt-6 flex items-center justify-center space-x-2 text-blue-600">
              <Loader className="w-5 h-5 animate-spin" />
              <span>Processing... (read-only analysis)</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdvancedAntiFootprintModal;
