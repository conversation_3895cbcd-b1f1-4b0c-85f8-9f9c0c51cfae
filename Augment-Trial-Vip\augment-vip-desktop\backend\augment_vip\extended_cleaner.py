"""
Extended Application Cleaner
Safe cleaning for JetBrains, Docker, Git, and other development tools
"""

import os
import json
import shutil
import platform
from pathlib import Path
from typing import List, Dict, Any, Optional, Set
from datetime import datetime
import xml.etree.ElementTree as ET

from .utils import info, success, error, warning

class ExtendedApplicationCleaner:
    """Safe cleaner for extended development applications"""
    
    def __init__(self):
        self.platform = platform.system().lower()
        self.backup_directory = Path.home() / '.augment-vip' / 'backups' / 'extended'
        self.backup_directory.mkdir(parents=True, exist_ok=True)
        
    def scan_all_applications(self, applications: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Scan all supported applications for telemetry data
        
        Args:
            applications: List of applications to scan
        
        Returns:
            Comprehensive scan results
        """
        if applications is None:
            applications = ['jetbrains', 'docker', 'git', 'npm', 'pip', 'cargo']
        
        scan_results = {
            'scan_metadata': {
                'timestamp': datetime.now().isoformat(),
                'platform': self.platform,
                'applications_scanned': applications,
                'scan_mode': 'safe_read_only'
            },
            'application_results': {},
            'summary': {
                'total_applications_found': 0,
                'total_telemetry_files': 0,
                'total_cache_size': 0,
                'cleanable_data_size': 0
            },
            'recommendations': []
        }
        
        for app in applications:
            try:
                app_result = self._scan_application(app)
                scan_results['application_results'][app] = app_result
                
                if app_result.get('found', False):
                    scan_results['summary']['total_applications_found'] += 1
                    scan_results['summary']['total_telemetry_files'] += app_result.get('telemetry_files', 0)
                    scan_results['summary']['total_cache_size'] += app_result.get('cache_size', 0)
                    scan_results['summary']['cleanable_data_size'] += app_result.get('cleanable_size', 0)
            
            except Exception as e:
                warning(f"Error scanning {app}: {e}")
                scan_results['application_results'][app] = {
                    'found': False,
                    'error': str(e)
                }
        
        # Generate recommendations
        scan_results['recommendations'] = self._generate_cleaning_recommendations(scan_results)
        
        return scan_results
    
    def _scan_application(self, app_name: str) -> Dict[str, Any]:
        """Scan a specific application for telemetry and cache data"""
        scanner_methods = {
            'jetbrains': self._scan_jetbrains,
            'docker': self._scan_docker,
            'git': self._scan_git,
            'npm': self._scan_npm,
            'pip': self._scan_pip,
            'cargo': self._scan_cargo
        }
        
        scanner = scanner_methods.get(app_name)
        if not scanner:
            return {'found': False, 'error': f'Unsupported application: {app_name}'}
        
        return scanner()
    
    def _scan_jetbrains(self) -> Dict[str, Any]:
        """Scan JetBrains IDEs for telemetry and cache data"""
        result = {
            'application': 'jetbrains',
            'found': False,
            'products_found': [],
            'telemetry_files': 0,
            'cache_size': 0,
            'cleanable_size': 0,
            'telemetry_locations': [],
            'cache_locations': [],
            'config_locations': []
        }
        
        # JetBrains products to scan
        jetbrains_products = [
            'IntelliJIdea', 'PyCharm', 'WebStorm', 'PhpStorm', 'RubyMine',
            'CLion', 'DataGrip', 'GoLand', 'Rider', 'AndroidStudio'
        ]
        
        # Get JetBrains directories based on platform
        jetbrains_dirs = self._get_jetbrains_directories()
        
        for base_dir in jetbrains_dirs:
            if not base_dir.exists():
                continue
            
            result['found'] = True
            
            # Scan for each product
            for product in jetbrains_products:
                product_dirs = list(base_dir.glob(f"{product}*"))
                
                for product_dir in product_dirs:
                    if product_dir.is_dir():
                        result['products_found'].append(product_dir.name)
                        
                        # Scan for telemetry files
                        telemetry_data = self._scan_jetbrains_telemetry(product_dir)
                        result['telemetry_files'] += telemetry_data['file_count']
                        result['telemetry_locations'].extend(telemetry_data['locations'])
                        
                        # Scan for cache data
                        cache_data = self._scan_jetbrains_cache(product_dir)
                        result['cache_size'] += cache_data['size']
                        result['cache_locations'].extend(cache_data['locations'])
                        
                        # Scan for config files with telemetry settings
                        config_data = self._scan_jetbrains_config(product_dir)
                        result['config_locations'].extend(config_data['locations'])
        
        result['cleanable_size'] = result['cache_size']  # Cache is safely cleanable
        
        return result
    
    def _get_jetbrains_directories(self) -> List[Path]:
        """Get JetBrains installation and config directories"""
        dirs = []
        
        if self.platform == 'windows':
            # Windows paths
            appdata = Path(os.environ.get('APPDATA', ''))
            localappdata = Path(os.environ.get('LOCALAPPDATA', ''))
            
            dirs.extend([
                appdata / 'JetBrains',
                localappdata / 'JetBrains'
            ])
        
        elif self.platform == 'darwin':  # macOS
            home = Path.home()
            dirs.extend([
                home / 'Library' / 'Application Support' / 'JetBrains',
                home / 'Library' / 'Caches' / 'JetBrains',
                home / 'Library' / 'Logs' / 'JetBrains'
            ])
        
        else:  # Linux
            home = Path.home()
            dirs.extend([
                home / '.config' / 'JetBrains',
                home / '.cache' / 'JetBrains',
                home / '.local' / 'share' / 'JetBrains'
            ])
        
        return dirs
    
    def _scan_jetbrains_telemetry(self, product_dir: Path) -> Dict[str, Any]:
        """Scan JetBrains product directory for telemetry files"""
        telemetry_data = {
            'file_count': 0,
            'locations': []
        }
        
        # Known telemetry file patterns
        telemetry_patterns = [
            '**/statistics/**',
            '**/usage/**',
            '**/telemetry/**',
            '**/analytics/**',
            '**/*statistics*',
            '**/*usage*',
            '**/*telemetry*'
        ]
        
        try:
            for pattern in telemetry_patterns:
                for file_path in product_dir.glob(pattern):
                    if file_path.is_file():
                        telemetry_data['file_count'] += 1
                        telemetry_data['locations'].append({
                            'path': str(file_path),
                            'size': file_path.stat().st_size,
                            'modified': datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()
                        })
        
        except Exception as e:
            warning(f"Error scanning JetBrains telemetry: {e}")
        
        return telemetry_data
    
    def _scan_jetbrains_cache(self, product_dir: Path) -> Dict[str, Any]:
        """Scan JetBrains product directory for cache data"""
        cache_data = {
            'size': 0,
            'locations': []
        }
        
        # Known cache directories
        cache_patterns = [
            '**/caches/**',
            '**/tmp/**',
            '**/temp/**',
            '**/logs/**'
        ]
        
        try:
            for pattern in cache_patterns:
                for cache_path in product_dir.glob(pattern):
                    if cache_path.is_dir():
                        dir_size = self._calculate_directory_size(cache_path)
                        cache_data['size'] += dir_size
                        cache_data['locations'].append({
                            'path': str(cache_path),
                            'size': dir_size,
                            'type': 'directory'
                        })
        
        except Exception as e:
            warning(f"Error scanning JetBrains cache: {e}")
        
        return cache_data
    
    def _scan_jetbrains_config(self, product_dir: Path) -> Dict[str, Any]:
        """Scan JetBrains configuration files for telemetry settings"""
        config_data = {
            'locations': []
        }
        
        try:
            # Look for options files
            options_files = list(product_dir.glob('**/options/*.xml'))
            
            for options_file in options_files:
                if 'statistics' in options_file.name.lower() or 'usage' in options_file.name.lower():
                    config_data['locations'].append({
                        'path': str(options_file),
                        'type': 'telemetry_config',
                        'size': options_file.stat().st_size
                    })
        
        except Exception as e:
            warning(f"Error scanning JetBrains config: {e}")
        
        return config_data
    
    def _scan_docker(self) -> Dict[str, Any]:
        """Scan Docker for telemetry and cache data"""
        result = {
            'application': 'docker',
            'found': False,
            'telemetry_files': 0,
            'cache_size': 0,
            'cleanable_size': 0,
            'docker_root': None,
            'telemetry_locations': [],
            'cache_locations': []
        }
        
        # Docker directories based on platform
        docker_dirs = self._get_docker_directories()
        
        for docker_dir in docker_dirs:
            if docker_dir.exists():
                result['found'] = True
                result['docker_root'] = str(docker_dir)
                
                # Scan for telemetry files
                telemetry_files = [
                    'usage.json', 'analytics.json', 'telemetry.json'
                ]
                
                for telemetry_file in telemetry_files:
                    file_path = docker_dir / telemetry_file
                    if file_path.exists():
                        result['telemetry_files'] += 1
                        result['telemetry_locations'].append({
                            'path': str(file_path),
                            'size': file_path.stat().st_size,
                            'type': 'telemetry_data'
                        })
                
                # Scan for cache directories
                cache_dirs = ['tmp', 'containers', 'image', 'overlay2']
                for cache_dir_name in cache_dirs:
                    cache_dir = docker_dir / cache_dir_name
                    if cache_dir.exists() and cache_dir.is_dir():
                        dir_size = self._calculate_directory_size(cache_dir)
                        result['cache_size'] += dir_size
                        result['cache_locations'].append({
                            'path': str(cache_dir),
                            'size': dir_size,
                            'type': 'cache_directory'
                        })
                
                break  # Use first found Docker directory
        
        result['cleanable_size'] = result['cache_size']  # Docker cache is cleanable
        
        return result
    
    def _get_docker_directories(self) -> List[Path]:
        """Get Docker directories based on platform"""
        dirs = []
        
        if self.platform == 'windows':
            appdata = Path(os.environ.get('APPDATA', ''))
            programdata = Path(os.environ.get('PROGRAMDATA', ''))
            dirs.extend([
                appdata / 'Docker',
                programdata / 'Docker'
            ])
        
        elif self.platform == 'darwin':  # macOS
            home = Path.home()
            dirs.extend([
                home / 'Library' / 'Containers' / 'com.docker.docker',
                home / '.docker'
            ])
        
        else:  # Linux
            dirs.extend([
                Path('/var/lib/docker'),
                Path.home() / '.docker'
            ])
        
        return dirs
    
    def _scan_git(self) -> Dict[str, Any]:
        """Scan Git for configuration and cache data"""
        result = {
            'application': 'git',
            'found': False,
            'telemetry_files': 0,
            'cache_size': 0,
            'cleanable_size': 0,
            'global_config': None,
            'config_locations': []
        }
        
        # Git global configuration
        git_config_paths = [
            Path.home() / '.gitconfig',
            Path.home() / '.config' / 'git' / 'config'
        ]
        
        if self.platform == 'windows':
            git_config_paths.append(Path(os.environ.get('USERPROFILE', '')) / '.gitconfig')
        
        for config_path in git_config_paths:
            if config_path.exists():
                result['found'] = True
                result['global_config'] = str(config_path)
                result['config_locations'].append({
                    'path': str(config_path),
                    'size': config_path.stat().st_size,
                    'type': 'global_config'
                })
                break
        
        return result
    
    def _scan_npm(self) -> Dict[str, Any]:
        """Scan npm for cache and telemetry data"""
        result = {
            'application': 'npm',
            'found': False,
            'telemetry_files': 0,
            'cache_size': 0,
            'cleanable_size': 0,
            'cache_locations': []
        }
        
        # npm cache directories
        npm_cache_dirs = []
        
        if self.platform == 'windows':
            appdata = Path(os.environ.get('APPDATA', ''))
            npm_cache_dirs.extend([
                appdata / 'npm-cache',
                appdata / 'npm'
            ])
        else:
            home = Path.home()
            npm_cache_dirs.extend([
                home / '.npm',
                home / '.npm-cache'
            ])
        
        for cache_dir in npm_cache_dirs:
            if cache_dir.exists():
                result['found'] = True
                dir_size = self._calculate_directory_size(cache_dir)
                result['cache_size'] += dir_size
                result['cache_locations'].append({
                    'path': str(cache_dir),
                    'size': dir_size,
                    'type': 'npm_cache'
                })
        
        result['cleanable_size'] = result['cache_size']
        
        return result
    
    def _scan_pip(self) -> Dict[str, Any]:
        """Scan pip for cache data"""
        result = {
            'application': 'pip',
            'found': False,
            'telemetry_files': 0,
            'cache_size': 0,
            'cleanable_size': 0,
            'cache_locations': []
        }
        
        # pip cache directories
        pip_cache_dirs = []
        
        if self.platform == 'windows':
            localappdata = Path(os.environ.get('LOCALAPPDATA', ''))
            pip_cache_dirs.append(localappdata / 'pip' / 'Cache')
        else:
            home = Path.home()
            pip_cache_dirs.extend([
                home / '.cache' / 'pip',
                home / 'Library' / 'Caches' / 'pip'  # macOS
            ])
        
        for cache_dir in pip_cache_dirs:
            if cache_dir.exists():
                result['found'] = True
                dir_size = self._calculate_directory_size(cache_dir)
                result['cache_size'] += dir_size
                result['cache_locations'].append({
                    'path': str(cache_dir),
                    'size': dir_size,
                    'type': 'pip_cache'
                })
        
        result['cleanable_size'] = result['cache_size']
        
        return result
    
    def _scan_cargo(self) -> Dict[str, Any]:
        """Scan Cargo (Rust) for cache data"""
        result = {
            'application': 'cargo',
            'found': False,
            'telemetry_files': 0,
            'cache_size': 0,
            'cleanable_size': 0,
            'cache_locations': []
        }
        
        # Cargo cache directory
        cargo_home = Path(os.environ.get('CARGO_HOME', Path.home() / '.cargo'))
        
        if cargo_home.exists():
            result['found'] = True
            
            # Cargo cache subdirectories
            cache_subdirs = ['registry', 'git', 'bin']
            
            for subdir_name in cache_subdirs:
                subdir = cargo_home / subdir_name
                if subdir.exists():
                    dir_size = self._calculate_directory_size(subdir)
                    result['cache_size'] += dir_size
                    result['cache_locations'].append({
                        'path': str(subdir),
                        'size': dir_size,
                        'type': 'cargo_cache'
                    })
        
        result['cleanable_size'] = result['cache_size']
        
        return result
    
    def _calculate_directory_size(self, directory: Path) -> int:
        """Calculate total size of a directory"""
        total_size = 0
        try:
            for file_path in directory.rglob('*'):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
        except (PermissionError, OSError):
            pass
        return total_size
    
    def _generate_cleaning_recommendations(self, scan_results: Dict[str, Any]) -> List[str]:
        """Generate cleaning recommendations based on scan results"""
        recommendations = []
        
        summary = scan_results.get('summary', {})
        total_cleanable = summary.get('cleanable_data_size', 0)
        
        if total_cleanable > 1024 * 1024 * 1024:  # > 1GB
            recommendations.append(f"💾 Large amount of cleanable data found: {total_cleanable / (1024**3):.1f}GB")
        
        # Application-specific recommendations
        for app, data in scan_results.get('application_results', {}).items():
            if isinstance(data, dict) and data.get('found', False):
                if app == 'jetbrains' and data.get('telemetry_files', 0) > 0:
                    recommendations.append("🔧 JetBrains telemetry data found - consider disabling in IDE settings")
                
                if app == 'docker' and data.get('cache_size', 0) > 100 * 1024 * 1024:  # > 100MB
                    recommendations.append("🐳 Large Docker cache found - safe to clean with 'docker system prune'")
                
                if app in ['npm', 'pip', 'cargo'] and data.get('cache_size', 0) > 50 * 1024 * 1024:  # > 50MB
                    recommendations.append(f"📦 {app.upper()} cache can be safely cleaned")
        
        # General recommendations
        recommendations.extend([
            "🧹 Regular cache cleaning improves performance",
            "🔒 Review application telemetry settings",
            "💾 Consider automated cache cleanup schedules",
            "🛡️ Monitor for new telemetry data accumulation"
        ])
        
        return recommendations
    
    def preview_cleaning(self, applications: List[str], include_telemetry: bool = True) -> Dict[str, Any]:
        """Preview what would be cleaned without making changes"""
        preview_results = {
            'preview_metadata': {
                'timestamp': datetime.now().isoformat(),
                'applications': applications,
                'include_telemetry': include_telemetry,
                'mode': 'preview_only'
            },
            'cleaning_plan': {},
            'total_files_to_clean': 0,
            'total_size_to_free': 0,
            'safety_warnings': []
        }
        
        scan_results = self.scan_all_applications(applications)
        
        for app, data in scan_results.get('application_results', {}).items():
            if isinstance(data, dict) and data.get('found', False):
                cleaning_plan = {
                    'cache_locations': data.get('cache_locations', []),
                    'telemetry_locations': data.get('telemetry_locations', []) if include_telemetry else [],
                    'estimated_size_freed': data.get('cleanable_size', 0)
                }
                
                if include_telemetry:
                    cleaning_plan['estimated_size_freed'] += sum(
                        loc.get('size', 0) for loc in data.get('telemetry_locations', [])
                    )
                
                preview_results['cleaning_plan'][app] = cleaning_plan
                preview_results['total_size_to_free'] += cleaning_plan['estimated_size_freed']
                preview_results['total_files_to_clean'] += len(cleaning_plan['cache_locations']) + len(cleaning_plan['telemetry_locations'])
        
        # Add safety warnings
        if include_telemetry:
            preview_results['safety_warnings'].append("⚠️ Telemetry data removal may affect application analytics")
        
        preview_results['safety_warnings'].extend([
            "💾 Automatic backups will be created before cleaning",
            "🔄 Applications may need to rebuild caches after cleaning",
            "✅ All operations are reversible with backups"
        ])
        
        return preview_results
