const fs = require("fs");
const path = require("path");
const os = require("os");
const { spawn } = require("child_process");

class DatabaseService {
  constructor() {
    this.supportedDatabases = {
      sqlite: {
        extensions: [".db", ".sqlite", ".sqlite3"],
        commonPaths: ["data", "database", "db", "storage", "var/lib", "tmp"],
      },
      postgresql: {
        dataDir:
          process.platform === "win32"
            ? "C:\\Program Files\\PostgreSQL\\*\\data"
            : "/var/lib/postgresql/*/main",
        configFiles: ["postgresql.conf", "pg_hba.conf"],
      },
      mysql: {
        dataDir:
          process.platform === "win32"
            ? "C:\\ProgramData\\MySQL\\MySQL Server *\\Data"
            : "/var/lib/mysql",
        configFiles: ["my.cnf", "my.ini"],
      },
    };
  }

  async scanForDatabases(progressCallback = null) {
    const results = {
      sqlite: [],
      postgresql: [],
      mysql: [],
      totalSize: 0,
      scanTime: Date.now(),
    };

    try {
      // Scan for SQLite databases
      if (progressCallback)
        progressCallback({
          step: "Scanning for SQLite databases...",
          progress: 10,
        });
      results.sqlite = await this.findSqliteDatabases();

      // Scan for PostgreSQL
      if (progressCallback)
        progressCallback({
          step: "Checking PostgreSQL installations...",
          progress: 40,
        });
      results.postgresql = await this.findPostgreSQLDatabases();

      // Scan for MySQL
      if (progressCallback)
        progressCallback({
          step: "Checking MySQL installations...",
          progress: 70,
        });
      results.mysql = await this.findMySQLDatabases();

      // Calculate total size
      if (progressCallback)
        progressCallback({ step: "Calculating total size...", progress: 90 });
      results.totalSize = this.calculateTotalSize(results);

      if (progressCallback)
        progressCallback({ step: "Scan complete!", progress: 100 });

      return results;
    } catch (error) {
      throw new Error(`Database scan failed: ${error.message}`);
    }
  }

  async findSqliteDatabases() {
    const sqliteFiles = [];
    const searchPaths = [
      os.homedir(),
      path.join(os.homedir(), "Documents"),
      path.join(os.homedir(), "Desktop"),
      "/tmp",
      "/var/tmp",
      process.cwd(),
    ];

    for (const searchPath of searchPaths) {
      if (fs.existsSync(searchPath)) {
        const files = await this.searchSqliteInDirectory(searchPath, 0, 3); // Max depth 3
        sqliteFiles.push(...files);
      }
    }

    return sqliteFiles;
  }

  async searchSqliteInDirectory(directory, currentDepth, maxDepth) {
    if (currentDepth >= maxDepth) return [];

    const files = [];

    try {
      const items = await fs.promises.readdir(directory);

      for (const item of items) {
        // Skip hidden directories and common non-database directories
        if (
          item.startsWith(".") ||
          ["node_modules", "venv", "__pycache__"].includes(item)
        ) {
          continue;
        }

        const fullPath = path.join(directory, item);
        const stats = await fs.promises.stat(fullPath);

        if (stats.isFile()) {
          const ext = path.extname(item).toLowerCase();
          if (this.supportedDatabases.sqlite.extensions.includes(ext)) {
            files.push({
              path: fullPath,
              name: item,
              size: stats.size,
              modified: stats.mtime,
              type: "sqlite",
              canClean: this.isSafeToClean(fullPath),
            });
          }
        } else if (stats.isDirectory()) {
          const subFiles = await this.searchSqliteInDirectory(
            fullPath,
            currentDepth + 1,
            maxDepth
          );
          files.push(...subFiles);
        }
      }
    } catch (error) {
      // Ignore permission errors
    }

    return files;
  }

  async findPostgreSQLDatabases() {
    const pgDatabases = [];

    try {
      // Check if PostgreSQL is installed
      const pgVersion = await this.checkPostgreSQLInstallation();
      if (!pgVersion) return [];

      // Try to connect and list databases
      const databases = await this.listPostgreSQLDatabases();

      for (const db of databases) {
        pgDatabases.push({
          name: db.name,
          size: db.size || 0,
          type: "postgresql",
          canClean:
            db.name !== "postgres" &&
            db.name !== "template0" &&
            db.name !== "template1",
          connectionInfo: db,
        });
      }
    } catch (error) {
      console.log("PostgreSQL scan error:", error.message);
    }

    return pgDatabases;
  }

  async findMySQLDatabases() {
    const mysqlDatabases = [];

    try {
      // Check if MySQL is installed
      const mysqlVersion = await this.checkMySQLInstallation();
      if (!mysqlVersion) return [];

      // Try to connect and list databases
      const databases = await this.listMySQLDatabases();

      for (const db of databases) {
        mysqlDatabases.push({
          name: db.name,
          size: db.size || 0,
          type: "mysql",
          canClean: ![
            "information_schema",
            "performance_schema",
            "mysql",
            "sys",
          ].includes(db.name),
          connectionInfo: db,
        });
      }
    } catch (error) {
      console.log("MySQL scan error:", error.message);
    }

    return mysqlDatabases;
  }

  async checkPostgreSQLInstallation() {
    return new Promise((resolve) => {
      const child = spawn("psql", ["--version"], { stdio: "pipe" });

      let output = "";
      child.stdout.on("data", (data) => {
        output += data.toString();
      });

      child.on("close", (code) => {
        if (code === 0) {
          resolve(output.trim());
        } else {
          resolve(null);
        }
      });

      child.on("error", () => {
        resolve(null);
      });
    });
  }

  async checkMySQLInstallation() {
    return new Promise((resolve) => {
      const child = spawn("mysql", ["--version"], { stdio: "pipe" });

      let output = "";
      child.stdout.on("data", (data) => {
        output += data.toString();
      });

      child.on("close", (code) => {
        if (code === 0) {
          resolve(output.trim());
        } else {
          resolve(null);
        }
      });

      child.on("error", () => {
        resolve(null);
      });
    });
  }

  async listPostgreSQLDatabases() {
    return new Promise((resolve, reject) => {
      const child = spawn(
        "psql",
        [
          "-h",
          "localhost",
          "-U",
          "postgres",
          "-c",
          "SELECT datname, pg_size_pretty(pg_database_size(datname)) as size FROM pg_database WHERE datistemplate = false;",
          "--csv",
        ],
        { stdio: "pipe" }
      );

      let output = "";
      let error = "";

      child.stdout.on("data", (data) => {
        output += data.toString();
      });

      child.stderr.on("data", (data) => {
        error += data.toString();
      });

      child.on("close", (code) => {
        if (code === 0) {
          const databases = this.parseCSVOutput(output);
          resolve(databases);
        } else {
          reject(new Error(error || "Failed to list PostgreSQL databases"));
        }
      });

      child.on("error", (err) => {
        reject(err);
      });
    });
  }

  async listMySQLDatabases() {
    return new Promise((resolve, reject) => {
      const child = spawn(
        "mysql",
        [
          "-e",
          "SELECT SCHEMA_NAME as name, ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS size FROM information_schema.tables GROUP BY SCHEMA_NAME;",
          "--batch",
          "--raw",
        ],
        { stdio: "pipe" }
      );

      let output = "";
      let error = "";

      child.stdout.on("data", (data) => {
        output += data.toString();
      });

      child.stderr.on("data", (data) => {
        error += data.toString();
      });

      child.on("close", (code) => {
        if (code === 0) {
          const databases = this.parseTSVOutput(output);
          resolve(databases);
        } else {
          reject(new Error(error || "Failed to list MySQL databases"));
        }
      });

      child.on("error", (err) => {
        reject(err);
      });
    });
  }

  parseCSVOutput(output) {
    const lines = output.trim().split("\n");
    const databases = [];

    for (let i = 1; i < lines.length; i++) {
      // Skip header
      const [name, size] = lines[i].split(",");
      if (name && name !== "datname") {
        databases.push({
          name: name.replace(/"/g, ""),
          size: size ? size.replace(/"/g, "") : "0",
        });
      }
    }

    return databases;
  }

  parseTSVOutput(output) {
    const lines = output.trim().split("\n");
    const databases = [];

    for (let i = 1; i < lines.length; i++) {
      // Skip header
      const [name, size] = lines[i].split("\t");
      if (name && name !== "name") {
        databases.push({
          name: name,
          size: parseFloat(size) || 0,
        });
      }
    }

    return databases;
  }

  isSafeToClean(filePath) {
    const fileName = path.basename(filePath).toLowerCase();
    const directory = path.dirname(filePath);

    // Don't clean system databases or important application databases
    const unsafePatterns = [
      "system",
      "config",
      "settings",
      "user",
      "profile",
      "keychain",
      "wallet",
    ];

    return !unsafePatterns.some(
      (pattern) => fileName.includes(pattern) || directory.includes(pattern)
    );
  }

  calculateTotalSize(results) {
    let total = 0;

    // SQLite files
    results.sqlite.forEach((file) => {
      total += file.size || 0;
    });

    // PostgreSQL and MySQL sizes are already calculated
    results.postgresql.forEach((db) => {
      total += db.size || 0;
    });

    results.mysql.forEach((db) => {
      total += (db.size || 0) * 1024 * 1024; // Convert MB to bytes
    });

    return total;
  }

  async cleanDatabase(databaseInfo, progressCallback = null) {
    try {
      if (progressCallback)
        progressCallback({ step: "Starting cleanup...", progress: 10 });

      switch (databaseInfo.type) {
        case "sqlite":
          return await this.cleanSqliteDatabase(databaseInfo, progressCallback);
        case "postgresql":
          return await this.cleanPostgreSQLDatabase(
            databaseInfo,
            progressCallback
          );
        case "mysql":
          return await this.cleanMySQLDatabase(databaseInfo, progressCallback);
        default:
          throw new Error(`Unsupported database type: ${databaseInfo.type}`);
      }
    } catch (error) {
      throw new Error(`Failed to clean database: ${error.message}`);
    }
  }

  async cleanSqliteDatabase(databaseInfo, progressCallback = null) {
    if (!databaseInfo.canClean) {
      throw new Error("Database is marked as unsafe to clean");
    }

    if (progressCallback)
      progressCallback({ step: "Creating backup...", progress: 20 });

    // Create backup first
    const backupPath = `${databaseInfo.path}.backup.${Date.now()}`;
    await fs.promises.copyFile(databaseInfo.path, backupPath);

    if (progressCallback)
      progressCallback({ step: "Removing database file...", progress: 60 });

    // Remove the original file
    await fs.promises.unlink(databaseInfo.path);

    if (progressCallback)
      progressCallback({ step: "Cleanup complete!", progress: 100 });

    return {
      cleaned: true,
      backupPath: backupPath,
      originalSize: databaseInfo.size,
      freedSpace: databaseInfo.size,
    };
  }

  async cleanPostgreSQLDatabase(databaseInfo, progressCallback = null) {
    if (!databaseInfo.canClean) {
      throw new Error("Database is marked as unsafe to clean");
    }

    if (progressCallback)
      progressCallback({
        step: "Dropping PostgreSQL database...",
        progress: 50,
      });

    return new Promise((resolve, reject) => {
      const child = spawn(
        "psql",
        [
          "-h",
          "localhost",
          "-U",
          "postgres",
          "-c",
          `DROP DATABASE IF EXISTS "${databaseInfo.name}";`,
        ],
        { stdio: "pipe" }
      );

      let error = "";

      child.stderr.on("data", (data) => {
        error += data.toString();
      });

      child.on("close", (code) => {
        if (code === 0) {
          if (progressCallback)
            progressCallback({
              step: "Database dropped successfully!",
              progress: 100,
            });
          resolve({
            cleaned: true,
            databaseName: databaseInfo.name,
            freedSpace: databaseInfo.size || 0,
          });
        } else {
          reject(new Error(error || "Failed to drop PostgreSQL database"));
        }
      });

      child.on("error", (err) => {
        reject(err);
      });
    });
  }

  async cleanMySQLDatabase(databaseInfo, progressCallback = null) {
    if (!databaseInfo.canClean) {
      throw new Error("Database is marked as unsafe to clean");
    }

    if (progressCallback)
      progressCallback({ step: "Dropping MySQL database...", progress: 50 });

    return new Promise((resolve, reject) => {
      const child = spawn(
        "mysql",
        ["-e", `DROP DATABASE IF EXISTS \`${databaseInfo.name}\`;`],
        { stdio: "pipe" }
      );

      let error = "";

      child.stderr.on("data", (data) => {
        error += data.toString();
      });

      child.on("close", (code) => {
        if (code === 0) {
          if (progressCallback)
            progressCallback({
              step: "Database dropped successfully!",
              progress: 100,
            });
          resolve({
            cleaned: true,
            databaseName: databaseInfo.name,
            freedSpace: (databaseInfo.size || 0) * 1024 * 1024,
          });
        } else {
          reject(new Error(error || "Failed to drop MySQL database"));
        }
      });

      child.on("error", (err) => {
        reject(err);
      });
    });
  }

  async backupDatabase(databaseInfo, backupPath, progressCallback = null) {
    try {
      if (progressCallback)
        progressCallback({ step: "Starting backup...", progress: 10 });

      switch (databaseInfo.type) {
        case "sqlite":
          return await this.backupSqliteDatabase(
            databaseInfo,
            backupPath,
            progressCallback
          );
        case "postgresql":
          return await this.backupPostgreSQLDatabase(
            databaseInfo,
            backupPath,
            progressCallback
          );
        case "mysql":
          return await this.backupMySQLDatabase(
            databaseInfo,
            backupPath,
            progressCallback
          );
        default:
          throw new Error(`Unsupported database type: ${databaseInfo.type}`);
      }
    } catch (error) {
      throw new Error(`Failed to backup database: ${error.message}`);
    }
  }

  async backupSqliteDatabase(
    databaseInfo,
    backupPath,
    progressCallback = null
  ) {
    if (progressCallback)
      progressCallback({ step: "Copying SQLite file...", progress: 50 });

    await fs.promises.copyFile(databaseInfo.path, backupPath);

    if (progressCallback)
      progressCallback({ step: "Backup complete!", progress: 100 });

    return {
      success: true,
      backupPath: backupPath,
      originalSize: databaseInfo.size,
    };
  }

  async backupPostgreSQLDatabase(
    databaseInfo,
    backupPath,
    progressCallback = null
  ) {
    if (progressCallback)
      progressCallback({ step: "Creating PostgreSQL dump...", progress: 30 });

    return new Promise((resolve, reject) => {
      const child = spawn(
        "pg_dump",
        [
          "-h",
          "localhost",
          "-U",
          "postgres",
          "-f",
          backupPath,
          databaseInfo.name,
        ],
        { stdio: "pipe" }
      );

      let error = "";

      child.stderr.on("data", (data) => {
        error += data.toString();
      });

      child.on("close", (code) => {
        if (code === 0) {
          if (progressCallback)
            progressCallback({
              step: "PostgreSQL backup complete!",
              progress: 100,
            });
          resolve({
            success: true,
            backupPath: backupPath,
            databaseName: databaseInfo.name,
          });
        } else {
          reject(new Error(error || "Failed to backup PostgreSQL database"));
        }
      });

      child.on("error", (err) => {
        reject(err);
      });
    });
  }

  async backupMySQLDatabase(databaseInfo, backupPath, progressCallback = null) {
    if (progressCallback)
      progressCallback({ step: "Creating MySQL dump...", progress: 30 });

    return new Promise((resolve, reject) => {
      const child = spawn("mysqldump", [databaseInfo.name], {
        stdio: ["pipe", "pipe", "pipe"],
      });

      const writeStream = fs.createWriteStream(backupPath);
      child.stdout.pipe(writeStream);

      let error = "";

      child.stderr.on("data", (data) => {
        error += data.toString();
      });

      child.on("close", (code) => {
        writeStream.end();
        if (code === 0) {
          if (progressCallback)
            progressCallback({ step: "MySQL backup complete!", progress: 100 });
          resolve({
            success: true,
            backupPath: backupPath,
            databaseName: databaseInfo.name,
          });
        } else {
          reject(new Error(error || "Failed to backup MySQL database"));
        }
      });

      child.on("error", (err) => {
        reject(err);
      });
    });
  }
}

module.exports = DatabaseService;
