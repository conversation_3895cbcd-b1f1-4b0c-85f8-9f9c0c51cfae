const {
  app,
  BrowserWindow,
  ipc<PERSON><PERSON>,
  Tray,
  Menu,
  nativeImage,
  shell,
} = require("electron");
const path = require("path");
const fs = require("fs");
const PythonService = require("./python-service.cjs");
const DatabaseService = require("./database-service.cjs");
const SystemStateAnalyzer = require("./system-state-analyzer.cjs");

// Keep a global reference of the window object
let mainWindow;
let tray = null;
let pythonService = null;
let databaseService = null;
let systemStateAnalyzer = null;

const isDev = process.env.NODE_ENV === "development";

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, "preload.cjs"),
    },
    icon: path.join(__dirname, "assets", "icon.png"),
    titleBarStyle: "default",
    show: false, // Don't show until ready
  });

  // Load the app
  if (isDev) {
    mainWindow.loadURL("http://localhost:5173");
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, "../dist/index.html"));
  }

  // Show window when ready
  mainWindow.once("ready-to-show", () => {
    mainWindow.show();
  });

  // Handle window closed
  mainWindow.on("closed", () => {
    mainWindow = null;
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: "deny" };
  });
}

function createTray() {
  const iconPath = path.join(__dirname, "assets", "tray-icon.png");
  const trayIcon = nativeImage.createFromPath(iconPath);
  tray = new Tray(trayIcon.resize({ width: 16, height: 16 }));

  const contextMenu = Menu.buildFromTemplate([
    {
      label: "Show Augment VIP",
      click: () => {
        if (mainWindow) {
          mainWindow.show();
          mainWindow.focus();
        } else {
          createWindow();
        }
      },
    },
    {
      label: "Quick Clean",
      click: () => {
        // Trigger quick clean operation
        if (mainWindow) {
          mainWindow.webContents.send("quick-clean");
        }
      },
    },
    { type: "separator" },
    {
      label: "Quit",
      click: () => {
        app.quit();
      },
    },
  ]);

  tray.setToolTip("Augment VIP - VS Code Database Manager");
  tray.setContextMenu(contextMenu);

  tray.on("click", () => {
    if (mainWindow) {
      if (mainWindow.isVisible()) {
        mainWindow.hide();
      } else {
        mainWindow.show();
        mainWindow.focus();
      }
    } else {
      createWindow();
    }
  });
}

// App event handlers
app.whenReady().then(async () => {
  // Initialize services
  pythonService = new PythonService();
  await pythonService.initialize();

  databaseService = new DatabaseService();
  systemStateAnalyzer = new SystemStateAnalyzer();

  createWindow();
  createTray();

  app.on("activate", () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
  }
});

// IPC handlers for Python CLI integration
ipcMain.handle("execute-python-command", async (event, command, args = []) => {
  try {
    if (!pythonService) {
      throw new Error("Python service not initialized");
    }

    const result = await pythonService.executeCommand(command, args, (data) => {
      // Send real-time updates to renderer
      event.sender.send("command-output", data);
    });

    return result;
  } catch (error) {
    return {
      success: false,
      stdout: "",
      stderr: error.message,
      exitCode: 1,
    };
  }
});

// Get system information
ipcMain.handle("get-system-info", async () => {
  if (pythonService) {
    return await pythonService.getSystemInfo();
  } else {
    const os = require("os");
    return {
      platform: process.platform,
      arch: process.arch,
      version: os.release(),
      homedir: os.homedir(),
      username: os.userInfo().username,
      pythonPath: null,
      augmentVipPath: null,
      isInitialized: false,
    };
  }
});

// File system operations
ipcMain.handle("check-file-exists", async (event, filePath) => {
  try {
    await fs.promises.access(filePath);
    return true;
  } catch {
    return false;
  }
});

ipcMain.handle("read-file", async (event, filePath) => {
  try {
    const content = await fs.promises.readFile(filePath, "utf8");
    return { success: true, content };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// Open path in system file manager
ipcMain.handle("open-path", async (event, path) => {
  try {
    await shell.openPath(path);
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// Copy text to clipboard
ipcMain.handle("copy-to-clipboard", async (event, text) => {
  try {
    const { clipboard } = require("electron");
    clipboard.writeText(text);
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// VS Code detection
ipcMain.handle("check-vscode-installation", async () => {
  if (pythonService) {
    return await pythonService.checkVsCodeInstallation();
  } else {
    return { found: false, paths: [], platform: process.platform };
  }
});

// Database file detection
ipcMain.handle("find-database-files", async () => {
  if (pythonService) {
    return await pythonService.findDatabaseFiles();
  } else {
    return [];
  }
});

// System state analysis
ipcMain.handle("analyze-system-state", async (event) => {
  try {
    if (!systemStateAnalyzer) {
      throw new Error("System state analyzer not initialized");
    }

    const result = await systemStateAnalyzer.analyzeSystemState((progress) => {
      // Send real-time progress updates to renderer
      event.sender.send("state-analysis-progress", progress);
    });

    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// Enhanced database operations
ipcMain.handle("scan-databases", async (event) => {
  try {
    if (!databaseService) {
      throw new Error("Database service not initialized");
    }

    const result = await databaseService.scanForDatabases((progress) => {
      // Send real-time progress updates to renderer
      event.sender.send("scan-progress", progress);
    });

    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle("clean-database", async (event, databaseInfo) => {
  try {
    if (!databaseService) {
      throw new Error("Database service not initialized");
    }

    // Send progress updates
    event.sender.send("clean-progress", {
      step: `Cleaning ${databaseInfo.name}...`,
      progress: 0,
    });

    const result = await databaseService.cleanDatabase(
      databaseInfo,
      (progress) => {
        event.sender.send("clean-progress", progress);
      }
    );

    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle("backup-database", async (event, databaseInfo, backupPath) => {
  try {
    if (!databaseService) {
      throw new Error("Database service not initialized");
    }

    const result = await databaseService.backupDatabase(
      databaseInfo,
      backupPath,
      (progress) => {
        event.sender.send("backup-progress", progress);
      }
    );

    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// App info
ipcMain.handle("get-app-version", () => {
  return app.getVersion();
});
