{"name": "augment-trial-vip", "version": "1.0.0", "description": "Complete Augment Trial VIP System - Backend + Desktop App", "private": true, "workspaces": ["augment-vip-desktop"], "scripts": {"start": "concurrently --names \"BACKEND,DESKTOP\" --prefix-colors \"blue,green\" \"npm run start:backend\" \"npm run start:desktop\"", "start:backend": "cd augment-vip && python run_server.py", "start:desktop": "cd augment-vip-desktop && npm run electron-dev", "dev": "npm run start", "install:all": "npm run install:desktop && npm run install:backend", "install:desktop": "cd augment-vip-desktop && npm install", "install:backend": "cd augment-vip && pip install -r requirements.txt", "build": "npm run build:desktop", "build:desktop": "cd augment-vip-desktop && npm run build", "test": "npm run test:desktop", "test:desktop": "cd augment-vip-desktop && npm test", "clean": "npm run clean:desktop", "clean:desktop": "cd augment-vip-desktop && rm -rf node_modules dist", "setup": "npm run install:all && echo '✅ Setup complete! Run: npm start'"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=16.0.0", "python": ">=3.8.0"}, "keywords": ["augment", "trial", "vip", "desktop", "electron", "<PERSON><PERSON><PERSON>", "system-management"], "author": "Augment Team", "license": "PRIVATE", "dependencies": {"@esbuild/win32-x64": "^0.25.6", "@rollup/rollup-win32-x64-msvc": "^4.44.2"}}