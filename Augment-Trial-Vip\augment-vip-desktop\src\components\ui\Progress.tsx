import React, { forwardRef } from "react";
import { cn } from "@/utils/cn";

export type ProgressVariant = "default" | "success" | "warning" | "error";
export type ProgressSize = "sm" | "md" | "lg";

export interface ProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Progress value (0-100)
   */
  value: number;
  
  /**
   * Maximum value
   * @default 100
   */
  max?: number;
  
  /**
   * Progress variant
   * @default "default"
   */
  variant?: ProgressVariant;
  
  /**
   * Progress size
   * @default "md"
   */
  size?: ProgressSize;
  
  /**
   * Whether to show the percentage label
   * @default false
   */
  showLabel?: boolean;
  
  /**
   * Custom label text
   */
  label?: string;
  
  /**
   * Whether to animate the progress bar
   * @default true
   */
  animated?: boolean;
  
  /**
   * Whether the progress is indeterminate
   * @default false
   */
  indeterminate?: boolean;
}

/**
 * Progress component for showing completion status
 */
const Progress = forwardRef<HTMLDivElement, ProgressProps>(
  (
    {
      className,
      value,
      max = 100,
      variant = "default",
      size = "md",
      showLabel = false,
      label,
      animated = true,
      indeterminate = false,
      ...props
    },
    ref
  ) => {
    const percentage = Math.min(Math.max((value / max) * 100, 0), 100);

    const variantStyles: Record<ProgressVariant, string> = {
      default: "bg-brand-500 dark:bg-brand-600",
      success: "bg-success-500 dark:bg-success-600",
      warning: "bg-warning-500 dark:bg-warning-600",
      error: "bg-error-500 dark:bg-error-600",
    };

    const sizeStyles: Record<ProgressSize, string> = {
      sm: "h-1",
      md: "h-2",
      lg: "h-3",
    };

    return (
      <div ref={ref} className={cn("w-full", className)} {...props}>
        {(showLabel || label) && (
          <div className="mb-2 flex justify-between text-sm">
            <span className="text-neutral-700 dark:text-neutral-300">
              {label || "Progress"}
            </span>
            {showLabel && (
              <span className="text-neutral-500 dark:text-neutral-400">
                {Math.round(percentage)}%
              </span>
            )}
          </div>
        )}
        
        <div
          className={cn(
            "w-full overflow-hidden rounded-full bg-neutral-200 dark:bg-dark-700",
            sizeStyles[size]
          )}
          role="progressbar"
          aria-valuenow={value}
          aria-valuemax={max}
          aria-valuemin={0}
        >
          <div
            className={cn(
              "h-full rounded-full transition-all duration-300 ease-out",
              variantStyles[variant],
              animated && "transition-transform",
              indeterminate && "animate-pulse"
            )}
            style={{
              width: indeterminate ? "100%" : `${percentage}%`,
              transform: indeterminate ? "translateX(-100%)" : "none",
              animation: indeterminate ? "shimmer 2s linear infinite" : undefined,
            }}
          />
        </div>
      </div>
    );
  }
);

Progress.displayName = "Progress";

/**
 * Circular Progress component
 */
export interface CircularProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Progress value (0-100)
   */
  value: number;
  
  /**
   * Maximum value
   * @default 100
   */
  max?: number;
  
  /**
   * Progress variant
   * @default "default"
   */
  variant?: ProgressVariant;
  
  /**
   * Size in pixels
   * @default 40
   */
  size?: number;
  
  /**
   * Stroke width
   * @default 4
   */
  strokeWidth?: number;
  
  /**
   * Whether to show the percentage label
   * @default false
   */
  showLabel?: boolean;
}

const CircularProgress = forwardRef<HTMLDivElement, CircularProgressProps>(
  (
    {
      className,
      value,
      max = 100,
      variant = "default",
      size = 40,
      strokeWidth = 4,
      showLabel = false,
      ...props
    },
    ref
  ) => {
    const percentage = Math.min(Math.max((value / max) * 100, 0), 100);
    const radius = (size - strokeWidth) / 2;
    const circumference = radius * 2 * Math.PI;
    const strokeDasharray = circumference;
    const strokeDashoffset = circumference - (percentage / 100) * circumference;

    const variantColors: Record<ProgressVariant, string> = {
      default: "stroke-brand-500",
      success: "stroke-success-500",
      warning: "stroke-warning-500",
      error: "stroke-error-500",
    };

    return (
      <div
        ref={ref}
        className={cn("relative inline-flex items-center justify-center", className)}
        {...props}
      >
        <svg
          width={size}
          height={size}
          className="transform -rotate-90"
          aria-hidden="true"
        >
          {/* Background circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="currentColor"
            strokeWidth={strokeWidth}
            fill="none"
            className="text-neutral-200 dark:text-dark-700"
          />
          {/* Progress circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            strokeWidth={strokeWidth}
            fill="none"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className={cn("transition-all duration-300 ease-out", variantColors[variant])}
          />
        </svg>
        
        {showLabel && (
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-xs font-medium text-neutral-700 dark:text-neutral-300">
              {Math.round(percentage)}%
            </span>
          </div>
        )}
      </div>
    );
  }
);

CircularProgress.displayName = "CircularProgress";

export { Progress, CircularProgress };
