"""
Application Metrics and Monitoring
Prometheus metrics collection and performance monitoring
"""

import time
from typing import Dict, Optional
from functools import wraps

from fastapi import Request, Response
from prometheus_client import (
    Counter,
    Histogram,
    Gauge,
    Info,
    generate_latest,
    CONTENT_TYPE_LATEST,
)
import structlog

from .config import get_settings

logger = structlog.get_logger(__name__)
settings = get_settings()

# Application info metric
app_info = Info('augment_vip_app', 'Application information')

# HTTP request metrics
http_requests_total = Counter(
    'augment_vip_http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status_code']
)

http_request_duration_seconds = Histogram(
    'augment_vip_http_request_duration_seconds',
    'HTTP request duration in seconds',
    ['method', 'endpoint']
)

# Database metrics
database_connections_active = Gauge(
    'augment_vip_database_connections_active',
    'Active database connections'
)

database_operations_total = Counter(
    'augment_vip_database_operations_total',
    'Total database operations',
    ['operation', 'table', 'status']
)

database_operation_duration_seconds = Histogram(
    'augment_vip_database_operation_duration_seconds',
    'Database operation duration in seconds',
    ['operation', 'table']
)

# System metrics
system_operations_total = Counter(
    'augment_vip_system_operations_total',
    'Total system operations',
    ['operation', 'status']
)

system_operation_duration_seconds = Histogram(
    'augment_vip_system_operation_duration_seconds',
    'System operation duration in seconds',
    ['operation']
)

# Background task metrics
background_tasks_total = Counter(
    'augment_vip_background_tasks_total',
    'Total background tasks',
    ['task_type', 'status']
)

background_task_duration_seconds = Histogram(
    'augment_vip_background_task_duration_seconds',
    'Background task duration in seconds',
    ['task_type']
)

# WebSocket metrics
websocket_connections_active = Gauge(
    'augment_vip_websocket_connections_active',
    'Active WebSocket connections'
)

websocket_messages_total = Counter(
    'augment_vip_websocket_messages_total',
    'Total WebSocket messages',
    ['direction', 'message_type']
)

# Error metrics
errors_total = Counter(
    'augment_vip_errors_total',
    'Total errors',
    ['error_type', 'endpoint']
)


def setup_metrics():
    """Initialize metrics with application information"""
    if settings.METRICS_ENABLED:
        app_info.info({
            'version': settings.VERSION,
            'environment': settings.ENVIRONMENT,
            'app_name': settings.APP_NAME,
        })
        logger.info("Metrics collection initialized")


async def metrics_middleware(request: Request, call_next):
    """
    Middleware to collect HTTP request metrics
    """
    if not settings.METRICS_ENABLED:
        return await call_next(request)
    
    start_time = time.time()
    
    # Get endpoint pattern for better grouping
    endpoint = request.url.path
    if hasattr(request, 'path_info'):
        endpoint = request.path_info
    
    method = request.method
    
    try:
        response = await call_next(request)
        status_code = str(response.status_code)
        
        # Record successful request
        http_requests_total.labels(
            method=method,
            endpoint=endpoint,
            status_code=status_code
        ).inc()
        
    except Exception as e:
        # Record error
        status_code = "500"
        http_requests_total.labels(
            method=method,
            endpoint=endpoint,
            status_code=status_code
        ).inc()
        
        errors_total.labels(
            error_type=type(e).__name__,
            endpoint=endpoint
        ).inc()
        
        raise
    
    finally:
        # Record request duration
        duration = time.time() - start_time
        http_request_duration_seconds.labels(
            method=method,
            endpoint=endpoint
        ).observe(duration)
    
    return response


def track_database_operation(operation: str, table: str):
    """
    Decorator to track database operations
    
    Usage:
        @track_database_operation("select", "users")
        async def get_user(user_id: int):
            # Database operation here
            pass
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            if not settings.METRICS_ENABLED:
                return await func(*args, **kwargs)
            
            start_time = time.time()
            status = "success"
            
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                status = "error"
                raise
            finally:
                # Record operation
                database_operations_total.labels(
                    operation=operation,
                    table=table,
                    status=status
                ).inc()
                
                # Record duration
                duration = time.time() - start_time
                database_operation_duration_seconds.labels(
                    operation=operation,
                    table=table
                ).observe(duration)
        
        return wrapper
    return decorator


def track_system_operation(operation: str):
    """
    Decorator to track system operations
    
    Usage:
        @track_system_operation("database_cleanup")
        async def cleanup_database():
            # System operation here
            pass
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            if not settings.METRICS_ENABLED:
                return await func(*args, **kwargs)
            
            start_time = time.time()
            status = "success"
            
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                status = "error"
                raise
            finally:
                # Record operation
                system_operations_total.labels(
                    operation=operation,
                    status=status
                ).inc()
                
                # Record duration
                duration = time.time() - start_time
                system_operation_duration_seconds.labels(
                    operation=operation
                ).observe(duration)
        
        return wrapper
    return decorator


def track_background_task(task_type: str):
    """
    Decorator to track background tasks
    
    Usage:
        @track_background_task("email_notification")
        async def send_email():
            # Background task here
            pass
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            if not settings.METRICS_ENABLED:
                return await func(*args, **kwargs)
            
            start_time = time.time()
            status = "success"
            
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                status = "error"
                raise
            finally:
                # Record task
                background_tasks_total.labels(
                    task_type=task_type,
                    status=status
                ).inc()
                
                # Record duration
                duration = time.time() - start_time
                background_task_duration_seconds.labels(
                    task_type=task_type
                ).observe(duration)
        
        return wrapper
    return decorator


class MetricsCollector:
    """Utility class for manual metrics collection"""
    
    @staticmethod
    def increment_websocket_connections():
        """Increment active WebSocket connections"""
        if settings.METRICS_ENABLED:
            websocket_connections_active.inc()
    
    @staticmethod
    def decrement_websocket_connections():
        """Decrement active WebSocket connections"""
        if settings.METRICS_ENABLED:
            websocket_connections_active.dec()
    
    @staticmethod
    def record_websocket_message(direction: str, message_type: str):
        """Record WebSocket message"""
        if settings.METRICS_ENABLED:
            websocket_messages_total.labels(
                direction=direction,
                message_type=message_type
            ).inc()
    
    @staticmethod
    def update_database_connections(count: int):
        """Update active database connections count"""
        if settings.METRICS_ENABLED:
            database_connections_active.set(count)


async def get_metrics() -> str:
    """
    Get Prometheus metrics in text format
    """
    if not settings.METRICS_ENABLED:
        return "# Metrics disabled\n"
    
    return generate_latest().decode('utf-8')


# Export commonly used items
__all__ = [
    'setup_metrics',
    'metrics_middleware',
    'track_database_operation',
    'track_system_operation',
    'track_background_task',
    'MetricsCollector',
    'get_metrics',
]
