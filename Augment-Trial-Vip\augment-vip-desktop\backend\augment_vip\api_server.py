"""
Enterprise API Server for Remote Management
RESTful API for managing operations, monitoring, and reporting
"""

import os
import json
import asyncio
import threading
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime
from dataclasses import asdict

try:
    from flask import Flask, request, jsonify, send_file
    from flask_cors import CORS
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False

from .utils import info, success, error, warning
from .database_manager import DatabaseManager
from .system_analyzer import SystemAnalyzer
from .batch_processor import BatchProcessor, OperationType
from .reporting_engine import ReportingEngine, OperationMetrics, SystemMetrics

class APIServer:
    """Enterprise API server for remote management"""
    
    def __init__(self, host: str = '127.0.0.1', port: int = 8080, debug: bool = False):
        if not FLASK_AVAILABLE:
            raise ImportError("Flask is required for API server. Install with: pip install flask flask-cors")
        
        self.host = host
        self.port = port
        self.debug = debug
        
        # Initialize components
        self.db_manager = DatabaseManager()
        self.system_analyzer = SystemAnalyzer()
        self.batch_processor = BatchProcessor()
        self.reporting_engine = ReportingEngine()
        
        # Initialize Flask app
        self.app = Flask(__name__)
        CORS(self.app)  # Enable CORS for frontend integration
        
        # Register routes
        self._register_routes()
        
        # Server state
        self.server_thread = None
        self.is_running = False
    
    def _register_routes(self):
        """Register all API routes"""
        
        # Health check
        @self.app.route('/health', methods=['GET'])
        def health_check():
            return jsonify({
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'version': '0.1.0'
            })
        
        # System information
        @self.app.route('/api/system/info', methods=['GET'])
        def get_system_info():
            try:
                apps = request.args.getlist('apps') or ['code', 'vscode']
                analysis = self.system_analyzer.analyze_system_state(target_apps=apps)
                return jsonify(analysis)
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        # Database operations
        @self.app.route('/api/databases/scan', methods=['POST'])
        def scan_databases():
            try:
                data = request.get_json() or {}
                apps = data.get('apps', ['vscode'])
                
                scan_results = self.db_manager.scan_all_databases(include_apps=apps)
                return jsonify(scan_results)
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/databases/clean', methods=['POST'])
        def clean_databases():
            try:
                data = request.get_json() or {}
                apps = data.get('apps', ['vscode'])
                patterns = data.get('patterns', ['augment'])
                exclude_patterns = data.get('exclude_patterns', [])
                dry_run = data.get('dry_run', True)
                
                # First scan to get databases
                scan_results = self.db_manager.scan_all_databases(include_apps=apps)
                
                # Collect all databases
                all_databases = []
                for db_type in ['sqlite', 'postgresql', 'mysql']:
                    all_databases.extend(scan_results['databases'][db_type])
                
                # Perform cleaning
                clean_results = self.db_manager.selective_clean(
                    databases=all_databases,
                    patterns=patterns,
                    exclude_patterns=exclude_patterns,
                    dry_run=dry_run
                )
                
                return jsonify(clean_results)
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        # Batch operations
        @self.app.route('/api/batch/jobs', methods=['POST'])
        def create_batch_job():
            try:
                data = request.get_json()
                
                job_id = self.batch_processor.create_batch_job(
                    name=data['name'],
                    description=data.get('description', ''),
                    operations=data['operations'],
                    parallel_execution=data.get('parallel_execution', False),
                    max_workers=data.get('max_workers', 4)
                )
                
                return jsonify({'job_id': job_id}), 201
            except Exception as e:
                return jsonify({'error': str(e)}), 400
        
        @self.app.route('/api/batch/jobs', methods=['GET'])
        def list_batch_jobs():
            try:
                include_history = request.args.get('include_history', 'false').lower() == 'true'
                jobs = self.batch_processor.list_jobs(include_history=include_history)
                return jsonify(jobs)
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/batch/jobs/<job_id>', methods=['GET'])
        def get_batch_job(job_id):
            try:
                job_status = self.batch_processor.get_job_status(job_id)
                return jsonify(job_status)
            except Exception as e:
                return jsonify({'error': str(e)}), 404
        
        @self.app.route('/api/batch/jobs/<job_id>/execute', methods=['POST'])
        def execute_batch_job(job_id):
            try:
                # Execute in background thread to avoid blocking
                def execute_job():
                    try:
                        self.batch_processor.execute_batch_job(job_id)
                    except Exception as e:
                        error(f"Batch job execution failed: {e}")
                
                thread = threading.Thread(target=execute_job)
                thread.start()
                
                return jsonify({'message': 'Job execution started', 'job_id': job_id})
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/batch/jobs/<job_id>/cancel', methods=['POST'])
        def cancel_batch_job(job_id):
            try:
                success = self.batch_processor.cancel_job(job_id)
                if success:
                    return jsonify({'message': 'Job cancelled successfully'})
                else:
                    return jsonify({'error': 'Job not found or cannot be cancelled'}), 404
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        # Reporting
        @self.app.route('/api/reports/operations', methods=['GET'])
        def generate_operation_report():
            try:
                days = int(request.args.get('days', 30))
                operation_types = request.args.getlist('operation_types')
                
                from datetime import timedelta
                end_date = datetime.now()
                start_date = end_date - timedelta(days=days)
                
                report = self.reporting_engine.generate_operation_report(
                    start_date=start_date,
                    end_date=end_date,
                    operation_types=operation_types if operation_types else None
                )
                
                return jsonify(report)
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/reports/system', methods=['GET'])
        def generate_system_report():
            try:
                days = int(request.args.get('days', 7))
                
                from datetime import timedelta
                end_date = datetime.now()
                start_date = end_date - timedelta(days=days)
                
                report = self.reporting_engine.generate_system_report(
                    start_date=start_date,
                    end_date=end_date
                )
                
                return jsonify(report)
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/reports/compliance', methods=['GET'])
        def generate_compliance_report():
            try:
                report = self.reporting_engine.generate_compliance_report()
                return jsonify(report)
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/reports/export', methods=['POST'])
        def export_report():
            try:
                data = request.get_json()
                report = data['report']
                format = data.get('format', 'json')
                filename = data.get('filename')
                
                file_path = self.reporting_engine.export_report(report, format, filename)
                
                return send_file(file_path, as_attachment=True)
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        # Configuration
        @self.app.route('/api/config', methods=['GET'])
        def get_configuration():
            return jsonify({
                'server': {
                    'host': self.host,
                    'port': self.port,
                    'debug': self.debug
                },
                'features': {
                    'batch_operations': True,
                    'system_monitoring': True,
                    'reporting': True,
                    'api_access': True
                },
                'version': '0.1.0'
            })
        
        # Metrics endpoint for monitoring
        @self.app.route('/api/metrics', methods=['GET'])
        def get_metrics():
            try:
                # Get current system state
                system_state = self.system_analyzer.analyze_system_state(['code', 'vscode'])
                
                # Get batch job statistics
                jobs = self.batch_processor.list_jobs(include_history=True)
                
                metrics = {
                    'timestamp': datetime.now().isoformat(),
                    'system': {
                        'cpu_usage': system_state.get('resource_usage', {}).get('cpu', {}).get('percent', 0),
                        'memory_usage': system_state.get('resource_usage', {}).get('memory', {}).get('percent', 0),
                        'disk_usage': max([
                            usage.get('percent', 0) 
                            for usage in system_state.get('resource_usage', {}).get('disk', {}).values()
                        ], default=0),
                        'running_processes': system_state.get('running_processes', {}).get('total_processes', 0),
                        'target_processes': len(system_state.get('running_processes', {}).get('target_processes', []))
                    },
                    'operations': {
                        'active_jobs': len(jobs.get('active_jobs', [])),
                        'total_jobs_history': len(jobs.get('job_history', [])),
                        'server_uptime': 'N/A'  # Would track actual uptime
                    }
                }
                
                return jsonify(metrics)
            except Exception as e:
                return jsonify({'error': str(e)}), 500
    
    def start(self, threaded: bool = True):
        """Start the API server"""
        if self.is_running:
            warning("API server is already running")
            return
        
        info(f"Starting API server on {self.host}:{self.port}")
        
        if threaded:
            self.server_thread = threading.Thread(
                target=self._run_server,
                daemon=True
            )
            self.server_thread.start()
        else:
            self._run_server()
    
    def _run_server(self):
        """Internal method to run the Flask server"""
        try:
            self.is_running = True
            self.app.run(
                host=self.host,
                port=self.port,
                debug=self.debug,
                threaded=True
            )
        except Exception as e:
            error(f"API server failed to start: {e}")
        finally:
            self.is_running = False
    
    def stop(self):
        """Stop the API server"""
        if not self.is_running:
            warning("API server is not running")
            return
        
        info("Stopping API server...")
        self.is_running = False
        
        # Shutdown batch processor
        self.batch_processor.shutdown()
        
        success("API server stopped")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current server status"""
        return {
            'running': self.is_running,
            'host': self.host,
            'port': self.port,
            'debug': self.debug,
            'endpoints': [
                '/health',
                '/api/system/info',
                '/api/databases/scan',
                '/api/databases/clean',
                '/api/batch/jobs',
                '/api/reports/operations',
                '/api/reports/system',
                '/api/reports/compliance',
                '/api/config',
                '/api/metrics'
            ]
        }
