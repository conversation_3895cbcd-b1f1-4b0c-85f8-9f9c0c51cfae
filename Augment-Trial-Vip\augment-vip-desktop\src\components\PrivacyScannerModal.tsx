import React, { useState } from 'react';
import { 
  Shield, 
  Eye, 
  Globe, 
  Monitor, 
  AlertTriangle, 
  CheckCircle, 
  X, 
  Play,
  Loader,
  Database,
  Network,
  HardDrive
} from 'lucide-react';

interface PrivacyScannerModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface ScanResults {
  browser_analysis?: any;
  extended_apps_analysis?: any;
  overall_privacy_score?: number;
  recommendations?: string[];
}

const PrivacyScannerModal: React.FC<PrivacyScannerModalProps> = ({ isOpen, onClose }) => {
  const [isScanning, setIsScanning] = useState(false);
  const [scanResults, setScanResults] = useState<ScanResults | null>(null);
  const [activeTab, setActiveTab] = useState<'browser' | 'apps' | 'network'>('browser');
  const [error, setError] = useState<string | null>(null);

  // Helper function to parse IPC response
  const parseIPCResponse = (result: any): any => {
    if (!result.success) {
      throw new Error(result.stderr || 'Command failed');
    }

    // Handle different response formats
    let outputText = '';
    if (result.stdout) {
      outputText = result.stdout;
    } else if (typeof result.output === 'string') {
      outputText = result.output;
    } else if (result.data) {
      outputText = typeof result.data === 'string' ? result.data : JSON.stringify(result.data);
    } else {
      throw new Error('No output data found in response');
    }

    // Find the JSON part - it starts with { and ends with }
    const jsonStart = outputText.indexOf('{');
    const jsonEnd = outputText.lastIndexOf('}');

    if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
      const jsonText = outputText.substring(jsonStart, jsonEnd + 1);
      try {
        return JSON.parse(jsonText);
      } catch (parseError) {
        console.error('JSON parse error:', parseError);
        console.error('JSON text:', jsonText.substring(0, 200) + '...');
        throw new Error('Failed to parse JSON response');
      }
    } else {
      // Fallback: try to find JSON line by line
      const lines = outputText.split('\n').filter((line: string) => line.trim());
      const jsonLine = lines.find((line: string) => line.trim().startsWith('{'));

      if (jsonLine) {
        try {
          return JSON.parse(jsonLine.trim());
        } catch (parseError) {
          console.error('Line JSON parse error:', parseError);
          throw new Error('Failed to parse JSON line');
        }
      } else {
        console.error('No JSON found in output:', outputText.substring(0, 200) + '...');
        throw new Error('No valid JSON output found');
      }
    }
  };

  const runPrivacyScan = async () => {
    setIsScanning(true);
    setError(null);

    try {
      // Use the proper Electron IPC method
      const result = await (window as any).electronAPI.executePythonCommand('anti-footprint-summary', ['--json']);
      const parsedResult = parseIPCResponse(result);
      setScanResults(parsedResult);
    } catch (err) {
      console.error('Privacy scan error:', err);
      setError('Failed to run privacy scan: ' + (err as Error).message);
    } finally {
      setIsScanning(false);
    }
  };

  const runBrowserScan = async () => {
    setIsScanning(true);
    setError(null);

    try {
      // Use the proper Electron IPC method
      const result = await (window as any).electronAPI.executePythonCommand('scan-browser-telemetry', ['--json']);
      const parsedResult = parseIPCResponse(result);
      setScanResults({ browser_analysis: parsedResult });
    } catch (err) {
      console.error('Browser scan error:', err);
      setError('Failed to run browser scan: ' + (err as Error).message);
    } finally {
      setIsScanning(false);
    }
  };

  const runExtendedAppsScan = async () => {
    setIsScanning(true);
    setError(null);

    try {
      // Use the proper Electron IPC method
      const result = await (window as any).electronAPI.executePythonCommand('scan-extended-apps', ['--json']);
      const parsedResult = parseIPCResponse(result);
      setScanResults({ extended_apps_analysis: parsedResult });
    } catch (err) {
      console.error('Extended apps scan error:', err);
      setError('Failed to run extended apps scan: ' + (err as Error).message);
    } finally {
      setIsScanning(false);
    }
  };

  const getPrivacyRiskColor = (score: number) => {
    if (score >= 70) return 'text-red-600';
    if (score >= 40) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getPrivacyRiskIcon = (score: number) => {
    if (score >= 70) return <AlertTriangle className="w-5 h-5 text-red-600" />;
    if (score >= 40) return <AlertTriangle className="w-5 h-5 text-yellow-600" />;
    return <CheckCircle className="w-5 h-5 text-green-600" />;
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-purple-600 to-blue-600 text-white">
          <div className="flex items-center space-x-3">
            <Shield className="w-6 h-6" />
            <h2 className="text-xl font-semibold">Privacy Scanner</h2>
          </div>
          <button
            onClick={onClose}
            className="text-white hover:text-gray-200 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Scan Controls */}
          <div className="mb-6">
            <div className="flex flex-wrap gap-3 mb-4">
              <button
                onClick={runPrivacyScan}
                disabled={isScanning}
                className="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isScanning ? <Loader className="w-4 h-4 animate-spin" /> : <Shield className="w-4 h-4" />}
                <span>Complete Privacy Scan</span>
              </button>
              
              <button
                onClick={runBrowserScan}
                disabled={isScanning}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isScanning ? <Loader className="w-4 h-4 animate-spin" /> : <Globe className="w-4 h-4" />}
                <span>Browser Scan</span>
              </button>
              
              <button
                onClick={runExtendedAppsScan}
                disabled={isScanning}
                className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isScanning ? <Loader className="w-4 h-4 animate-spin" /> : <HardDrive className="w-4 h-4" />}
                <span>Apps Scan</span>
              </button>
            </div>

            {isScanning && (
              <div className="flex items-center space-x-2 text-blue-600">
                <Loader className="w-4 h-4 animate-spin" />
                <span>Scanning for privacy threats... (read-only, safe)</span>
              </div>
            )}
          </div>

          {/* Error Display */}
          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center space-x-2 text-red-800">
                <AlertTriangle className="w-5 h-5" />
                <span className="font-medium">Scan Error</span>
              </div>
              <p className="mt-1 text-red-700">{error}</p>
            </div>
          )}

          {/* Results Display */}
          {scanResults && (
            <div className="space-y-6">
              {/* Overall Privacy Score */}
              {scanResults.overall_privacy_score !== undefined && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getPrivacyRiskIcon(scanResults.overall_privacy_score)}
                      <div>
                        <h3 className="font-semibold text-gray-900">Overall Privacy Score</h3>
                        <p className="text-sm text-gray-600">Lower scores indicate better privacy</p>
                      </div>
                    </div>
                    <div className={`text-2xl font-bold ${getPrivacyRiskColor(scanResults.overall_privacy_score)}`}>
                      {scanResults.overall_privacy_score}/100
                    </div>
                  </div>
                </div>
              )}

              {/* Tabs */}
              <div className="border-b border-gray-200">
                <nav className="-mb-px flex space-x-8">
                  <button
                    onClick={() => setActiveTab('browser')}
                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'browser'
                        ? 'border-purple-500 text-purple-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <Globe className="w-4 h-4" />
                      <span>Browser Analysis</span>
                    </div>
                  </button>
                  <button
                    onClick={() => setActiveTab('apps')}
                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'apps'
                        ? 'border-purple-500 text-purple-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <HardDrive className="w-4 h-4" />
                      <span>Applications</span>
                    </div>
                  </button>
                  <button
                    onClick={() => setActiveTab('network')}
                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'network'
                        ? 'border-purple-500 text-purple-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <Network className="w-4 h-4" />
                      <span>Network Monitor</span>
                    </div>
                  </button>
                </nav>
              </div>

              {/* Tab Content */}
              <div className="mt-6">
                {activeTab === 'browser' && scanResults.browser_analysis && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900">Browser Telemetry Analysis</h3>
                    
                    {/* Browser Summary */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-blue-50 rounded-lg p-4">
                        <div className="flex items-center space-x-2">
                          <Globe className="w-5 h-5 text-blue-600" />
                          <span className="font-medium text-blue-900">Browsers Found</span>
                        </div>
                        <p className="text-2xl font-bold text-blue-600 mt-1">
                          {scanResults.browser_analysis.summary?.total_browsers_found || 0}
                        </p>
                      </div>
                      
                      <div className="bg-yellow-50 rounded-lg p-4">
                        <div className="flex items-center space-x-2">
                          <Eye className="w-5 h-5 text-yellow-600" />
                          <span className="font-medium text-yellow-900">Tracking Entries</span>
                        </div>
                        <p className="text-2xl font-bold text-yellow-600 mt-1">
                          {scanResults.browser_analysis.summary?.total_tracking_entries || 0}
                        </p>
                      </div>
                      
                      <div className="bg-red-50 rounded-lg p-4">
                        <div className="flex items-center space-x-2">
                          <AlertTriangle className="w-5 h-5 text-red-600" />
                          <span className="font-medium text-red-900">Privacy Risk</span>
                        </div>
                        <p className={`text-2xl font-bold mt-1 ${getPrivacyRiskColor(scanResults.browser_analysis.summary?.privacy_risk_score || 0)}`}>
                          {scanResults.browser_analysis.summary?.privacy_risk_score || 0}/100
                        </p>
                      </div>
                    </div>

                    {/* Browser Details */}
                    {scanResults.browser_analysis.browser_results && (
                      <div className="space-y-3">
                        <h4 className="font-medium text-gray-900">Browser Details</h4>
                        {Object.entries(scanResults.browser_analysis.browser_results).map(([browser, data]: [string, any]) => (
                          data.found && (
                            <div key={browser} className="border border-gray-200 rounded-lg p-4">
                              <div className="flex items-center justify-between mb-2">
                                <h5 className="font-medium text-gray-900 capitalize">{browser}</h5>
                                <span className={`px-2 py-1 rounded text-xs font-medium ${
                                  data.telemetry_domains?.length > 0 ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                                }`}>
                                  {data.telemetry_domains?.length > 0 ? 'Tracking Detected' : 'Clean'}
                                </span>
                              </div>
                              
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                <div>
                                  <span className="text-gray-600">Profiles:</span>
                                  <p className="font-medium">{data.profile_paths?.length || 0}</p>
                                </div>
                                <div>
                                  <span className="text-gray-600">Databases:</span>
                                  <p className="font-medium">{data.databases_found?.length || 0}</p>
                                </div>
                                <div>
                                  <span className="text-gray-600">Tracking Domains:</span>
                                  <p className="font-medium">{data.telemetry_domains?.length || 0}</p>
                                </div>
                                <div>
                                  <span className="text-gray-600">Last Activity:</span>
                                  <p className="font-medium">
                                    {data.last_activity ? new Date(data.last_activity).toLocaleDateString() : 'N/A'}
                                  </p>
                                </div>
                              </div>

                              {data.telemetry_domains?.length > 0 && (
                                <div className="mt-3">
                                  <span className="text-sm text-gray-600">Detected tracking domains:</span>
                                  <div className="mt-1 flex flex-wrap gap-1">
                                    {data.telemetry_domains.slice(0, 5).map((domain: string, index: number) => (
                                      <span key={index} className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded">
                                        {domain}
                                      </span>
                                    ))}
                                    {data.telemetry_domains.length > 5 && (
                                      <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                                        +{data.telemetry_domains.length - 5} more
                                      </span>
                                    )}
                                  </div>
                                </div>
                              )}
                            </div>
                          )
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {activeTab === 'apps' && scanResults.extended_apps_analysis && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900">Application Analysis</h3>
                    
                    {/* Apps Summary */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-green-50 rounded-lg p-4">
                        <div className="flex items-center space-x-2">
                          <HardDrive className="w-5 h-5 text-green-600" />
                          <span className="font-medium text-green-900">Apps Found</span>
                        </div>
                        <p className="text-2xl font-bold text-green-600 mt-1">
                          {scanResults.extended_apps_analysis.summary?.total_applications_found || 0}
                        </p>
                      </div>
                      
                      <div className="bg-blue-50 rounded-lg p-4">
                        <div className="flex items-center space-x-2">
                          <Database className="w-5 h-5 text-blue-600" />
                          <span className="font-medium text-blue-900">Cache Size</span>
                        </div>
                        <p className="text-2xl font-bold text-blue-600 mt-1">
                          {formatBytes(scanResults.extended_apps_analysis.summary?.total_cache_size || 0)}
                        </p>
                      </div>
                      
                      <div className="bg-purple-50 rounded-lg p-4">
                        <div className="flex items-center space-x-2">
                          <AlertTriangle className="w-5 h-5 text-purple-600" />
                          <span className="font-medium text-purple-900">Telemetry Files</span>
                        </div>
                        <p className="text-2xl font-bold text-purple-600 mt-1">
                          {scanResults.extended_apps_analysis.summary?.total_telemetry_files || 0}
                        </p>
                      </div>
                    </div>

                    {/* App Details */}
                    {scanResults.extended_apps_analysis.application_results && (
                      <div className="space-y-3">
                        <h4 className="font-medium text-gray-900">Application Details</h4>
                        {Object.entries(scanResults.extended_apps_analysis.application_results).map(([app, data]: [string, any]) => (
                          data.found && (
                            <div key={app} className="border border-gray-200 rounded-lg p-4">
                              <div className="flex items-center justify-between mb-2">
                                <h5 className="font-medium text-gray-900 capitalize">{app}</h5>
                                <span className={`px-2 py-1 rounded text-xs font-medium ${
                                  data.cleanable_size > 0 ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                                }`}>
                                  {data.cleanable_size > 0 ? 'Cache Found' : 'No Cache'}
                                </span>
                              </div>
                              
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                <div>
                                  <span className="text-gray-600">Cache Size:</span>
                                  <p className="font-medium">{formatBytes(data.cache_size || 0)}</p>
                                </div>
                                <div>
                                  <span className="text-gray-600">Cleanable:</span>
                                  <p className="font-medium">{formatBytes(data.cleanable_size || 0)}</p>
                                </div>
                                <div>
                                  <span className="text-gray-600">Telemetry Files:</span>
                                  <p className="font-medium">{data.telemetry_files || 0}</p>
                                </div>
                                <div>
                                  <span className="text-gray-600">Cache Locations:</span>
                                  <p className="font-medium">{data.cache_locations?.length || 0}</p>
                                </div>
                              </div>
                            </div>
                          )
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {activeTab === 'network' && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900">Network Monitoring</h3>
                    <div className="bg-blue-50 rounded-lg p-6 text-center">
                      <Network className="w-12 h-12 text-blue-600 mx-auto mb-3" />
                      <h4 className="font-medium text-blue-900 mb-2">Network Monitoring Available</h4>
                      <p className="text-blue-700 mb-4">
                        Monitor network traffic for telemetry and tracking in real-time
                      </p>
                      <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        Start Network Monitor
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* Recommendations */}
              {scanResults.recommendations && scanResults.recommendations.length > 0 && (
                <div className="mt-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Privacy Recommendations</h3>
                  <div className="space-y-2">
                    {scanResults.recommendations.map((recommendation, index) => (
                      <div key={index} className="flex items-start space-x-2 p-3 bg-gray-50 rounded-lg">
                        <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">{recommendation}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Initial State */}
          {!scanResults && !isScanning && !error && (
            <div className="text-center py-12">
              <Shield className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Privacy Scanner Ready</h3>
              <p className="text-gray-600 mb-6">
                Scan your system for telemetry, tracking, and privacy threats.<br />
                All scans are read-only and safe - no changes will be made to your system.
              </p>
              <button
                onClick={runPrivacyScan}
                className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                Start Complete Privacy Scan
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PrivacyScannerModal;
