# Development Requirements
# Install with: pip install -r requirements-dev.txt

# Include production requirements
-r requirements.txt

# Development & Testing
pytest>=7.4.3
pytest-asyncio>=0.21.1
pytest-cov>=4.1.0
pytest-mock>=3.12.0
pytest-xdist>=3.5.0  # Parallel test execution
factory-boy>=3.3.0
faker>=20.1.0

# Code Quality & Formatting
black>=23.11.0
isort>=5.12.0
mypy>=1.7.1
flake8>=6.1.0
bandit>=1.7.5  # Security linting
safety>=2.3.5  # Dependency vulnerability checking

# Pre-commit hooks
pre-commit>=3.6.0

# Documentation
mkdocs>=1.5.3
mkdocs-material>=9.4.8
mkdocs-swagger-ui-tag>=0.6.8

# Development Tools
ipython>=8.17.2
jupyter>=1.0.0
watchdog>=3.0.0

# API Documentation & Testing
httpx>=0.25.2
requests>=2.31.0

# Performance Profiling
py-spy>=0.3.14
memory-profiler>=0.61.0
