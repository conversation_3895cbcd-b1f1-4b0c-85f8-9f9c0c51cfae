"""
Browser Fingerprint Spoofing System
Safe browser fingerprint randomization and protection
"""

import os
import json
import random
import platform
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime
import string

from .utils import info, success, error, warning

class BrowserFingerprintSpoofer:
    """Safe browser fingerprint spoofing and randomization"""
    
    def __init__(self):
        self.platform = platform.system().lower()
        self.config_directory = Path.home() / '.augment-vip' / 'fingerprint_configs'
        self.config_directory.mkdir(parents=True, exist_ok=True)
        
        # Load fingerprint databases
        self.user_agents = self._load_user_agent_database()
        self.screen_resolutions = self._load_screen_resolution_database()
        self.timezone_database = self._load_timezone_database()
        
    def analyze_current_fingerprint(self, browser: str = 'chrome') -> Dict[str, Any]:
        """
        Analyze current browser fingerprint exposure
        
        Args:
            browser: Browser to analyze ('chrome', 'firefox', 'edge')
        
        Returns:
            Current fingerprint analysis
        """
        analysis = {
            'analysis_metadata': {
                'timestamp': datetime.now().isoformat(),
                'browser': browser,
                'platform': self.platform,
                'analysis_type': 'fingerprint_exposure'
            },
            'fingerprint_vectors': {
                'user_agent': self._analyze_user_agent(browser),
                'screen_resolution': self._analyze_screen_resolution(),
                'canvas_fingerprint': self._analyze_canvas_fingerprint(browser),
                'webgl_fingerprint': self._analyze_webgl_fingerprint(browser),
                'font_fingerprint': self._analyze_font_fingerprint(),
                'timezone_fingerprint': self._analyze_timezone_fingerprint()
            },
            'privacy_risk_assessment': {
                'overall_risk_score': 0,
                'high_risk_vectors': [],
                'medium_risk_vectors': [],
                'low_risk_vectors': []
            },
            'spoofing_opportunities': [],
            'recommendations': []
        }
        
        # Calculate risk scores for each vector
        risk_scores = {}
        for vector, data in analysis['fingerprint_vectors'].items():
            risk_score = self._calculate_vector_risk(vector, data)
            risk_scores[vector] = risk_score
            
            if risk_score >= 70:
                analysis['privacy_risk_assessment']['high_risk_vectors'].append(vector)
            elif risk_score >= 40:
                analysis['privacy_risk_assessment']['medium_risk_vectors'].append(vector)
            else:
                analysis['privacy_risk_assessment']['low_risk_vectors'].append(vector)
        
        # Calculate overall risk score
        analysis['privacy_risk_assessment']['overall_risk_score'] = sum(risk_scores.values()) / len(risk_scores)
        
        # Generate spoofing opportunities
        analysis['spoofing_opportunities'] = self._identify_spoofing_opportunities(analysis)
        
        # Generate recommendations
        analysis['recommendations'] = self._generate_fingerprint_recommendations(analysis)
        
        return analysis
    
    def generate_spoofed_profile(self, 
                                profile_type: str = 'random',
                                target_browser: str = 'chrome') -> Dict[str, Any]:
        """
        Generate a spoofed browser fingerprint profile
        
        Args:
            profile_type: Type of profile ('random', 'common', 'stealth')
            target_browser: Target browser for the profile
        
        Returns:
            Generated spoofed profile
        """
        profile = {
            'profile_metadata': {
                'timestamp': datetime.now().isoformat(),
                'profile_type': profile_type,
                'target_browser': target_browser,
                'profile_id': self._generate_profile_id()
            },
            'spoofed_attributes': {
                'user_agent': self._generate_spoofed_user_agent(profile_type, target_browser),
                'screen_resolution': self._generate_spoofed_screen_resolution(profile_type),
                'viewport_size': self._generate_spoofed_viewport(profile_type),
                'timezone': self._generate_spoofed_timezone(profile_type),
                'language_settings': self._generate_spoofed_language(profile_type),
                'platform_info': self._generate_spoofed_platform(profile_type),
                'canvas_noise': self._generate_canvas_noise_config(),
                'webgl_noise': self._generate_webgl_noise_config(),
                'font_masking': self._generate_font_masking_config()
            },
            'implementation_methods': {
                'browser_extension': self._generate_extension_config(target_browser),
                'user_script': self._generate_userscript_config(),
                'browser_flags': self._generate_browser_flags(target_browser)
            },
            'profile_consistency': {
                'consistency_score': 0,
                'potential_conflicts': [],
                'validation_status': 'pending'
            }
        }
        
        # Validate profile consistency
        profile['profile_consistency'] = self._validate_profile_consistency(profile)
        
        return profile
    
    def preview_fingerprint_spoofing(self, 
                                   browser: str,
                                   spoofing_methods: List[str]) -> Dict[str, Any]:
        """
        Preview fingerprint spoofing changes
        
        Args:
            browser: Target browser
            spoofing_methods: List of spoofing methods to apply
        
        Returns:
            Preview of spoofing changes
        """
        preview = {
            'preview_metadata': {
                'timestamp': datetime.now().isoformat(),
                'browser': browser,
                'spoofing_methods': spoofing_methods,
                'safe_operation': True
            },
            'current_fingerprint': self.analyze_current_fingerprint(browser),
            'proposed_changes': {},
            'after_spoofing': {},
            'implementation_plan': {
                'method': 'browser_extension',
                'reversible': True,
                'backup_required': False,
                'risk_level': 'low'
            },
            'warnings': []
        }
        
        # Generate proposed changes for each method
        for method in spoofing_methods:
            if method == 'user_agent_spoofing':
                preview['proposed_changes']['user_agent'] = self._preview_user_agent_spoofing()
            elif method == 'canvas_noise':
                preview['proposed_changes']['canvas'] = self._preview_canvas_noise()
            elif method == 'webgl_spoofing':
                preview['proposed_changes']['webgl'] = self._preview_webgl_spoofing()
            elif method == 'font_masking':
                preview['proposed_changes']['fonts'] = self._preview_font_masking()
            elif method == 'timezone_spoofing':
                preview['proposed_changes']['timezone'] = self._preview_timezone_spoofing()
        
        # Calculate after-spoofing fingerprint
        preview['after_spoofing'] = self._calculate_spoofed_fingerprint(
            preview['current_fingerprint'], 
            preview['proposed_changes']
        )
        
        # Add safety warnings
        if 'user_agent_spoofing' in spoofing_methods:
            preview['warnings'].append("ℹ️ User-Agent spoofing may affect website compatibility")
        
        if 'timezone_spoofing' in spoofing_methods:
            preview['warnings'].append("⚠️ Timezone spoofing may affect time-sensitive applications")
        
        preview['warnings'].extend([
            "✅ All spoofing is implemented via browser extensions",
            "🔄 Changes are easily reversible",
            "🛡️ No permanent system modifications"
        ])
        
        return preview
    
    def create_spoofing_extension(self, 
                                 browser: str,
                                 spoofing_profile: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create browser extension for fingerprint spoofing
        
        Args:
            browser: Target browser
            spoofing_profile: Spoofing profile to implement
        
        Returns:
            Extension creation results
        """
        result = {
            'creation_metadata': {
                'timestamp': datetime.now().isoformat(),
                'browser': browser,
                'profile_id': spoofing_profile.get('profile_metadata', {}).get('profile_id'),
                'extension_type': 'fingerprint_spoofer'
            },
            'extension_files': {
                'manifest_path': None,
                'content_script_path': None,
                'background_script_path': None,
                'popup_html_path': None
            },
            'installation_instructions': [],
            'creation_result': {
                'success': False,
                'extension_directory': None,
                'error_message': None
            }
        }
        
        try:
            # Create extension directory
            extension_name = f"augment_vip_fingerprint_spoofer_{browser}"
            extension_dir = self.config_directory / extension_name
            extension_dir.mkdir(exist_ok=True)
            
            # Create manifest file
            manifest_path = self._create_extension_manifest(extension_dir, browser, spoofing_profile)
            result['extension_files']['manifest_path'] = str(manifest_path)
            
            # Create content script
            content_script_path = self._create_content_script(extension_dir, spoofing_profile)
            result['extension_files']['content_script_path'] = str(content_script_path)
            
            # Create background script
            background_script_path = self._create_background_script(extension_dir, spoofing_profile)
            result['extension_files']['background_script_path'] = str(background_script_path)
            
            # Create popup interface
            popup_html_path = self._create_popup_interface(extension_dir, spoofing_profile)
            result['extension_files']['popup_html_path'] = str(popup_html_path)
            
            # Generate installation instructions
            result['installation_instructions'] = self._generate_installation_instructions(browser, extension_dir)
            
            result['creation_result']['success'] = True
            result['creation_result']['extension_directory'] = str(extension_dir)
            
            success(f"Fingerprint spoofing extension created: {extension_dir}")
            
        except Exception as e:
            error_msg = f"Failed to create spoofing extension: {e}"
            result['creation_result']['error_message'] = error_msg
            error(error_msg)
        
        return result
    
    def _load_user_agent_database(self) -> Dict[str, List[str]]:
        """Load database of realistic user agents"""
        return {
            'chrome_windows': [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36'
            ],
            'chrome_macos': [
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36'
            ],
            'firefox_windows': [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0'
            ],
            'edge_windows': [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0'
            ]
        }
    
    def _load_screen_resolution_database(self) -> List[Dict[str, int]]:
        """Load database of common screen resolutions"""
        return [
            {'width': 1920, 'height': 1080, 'popularity': 'very_high'},
            {'width': 1366, 'height': 768, 'popularity': 'high'},
            {'width': 1536, 'height': 864, 'popularity': 'medium'},
            {'width': 1440, 'height': 900, 'popularity': 'medium'},
            {'width': 1280, 'height': 720, 'popularity': 'medium'},
            {'width': 2560, 'height': 1440, 'popularity': 'low'},
            {'width': 3840, 'height': 2160, 'popularity': 'low'}
        ]
    
    def _load_timezone_database(self) -> List[str]:
        """Load database of common timezones"""
        return [
            'America/New_York', 'America/Los_Angeles', 'America/Chicago',
            'Europe/London', 'Europe/Paris', 'Europe/Berlin',
            'Asia/Tokyo', 'Asia/Shanghai', 'Asia/Kolkata',
            'Australia/Sydney', 'America/Toronto', 'Europe/Amsterdam'
        ]
    
    def _analyze_user_agent(self, browser: str) -> Dict[str, Any]:
        """Analyze user agent fingerprint vector"""
        return {
            'vector_type': 'user_agent',
            'current_value': 'detected_from_browser',  # Would be detected in real implementation
            'uniqueness_score': 65,  # Estimated uniqueness
            'spoofing_difficulty': 'easy',
            'detection_methods': ['HTTP headers', 'JavaScript navigator.userAgent'],
            'spoofing_effectiveness': 'high'
        }
    
    def _analyze_screen_resolution(self) -> Dict[str, Any]:
        """Analyze screen resolution fingerprint vector"""
        return {
            'vector_type': 'screen_resolution',
            'current_value': 'detected_from_system',
            'uniqueness_score': 45,
            'spoofing_difficulty': 'medium',
            'detection_methods': ['screen.width/height', 'CSS media queries'],
            'spoofing_effectiveness': 'medium'
        }
    
    def _analyze_canvas_fingerprint(self, browser: str) -> Dict[str, Any]:
        """Analyze canvas fingerprint vector"""
        return {
            'vector_type': 'canvas_fingerprint',
            'current_value': 'unique_hash_detected',
            'uniqueness_score': 85,
            'spoofing_difficulty': 'medium',
            'detection_methods': ['Canvas API rendering', 'toDataURL() hash'],
            'spoofing_effectiveness': 'high'
        }
    
    def _analyze_webgl_fingerprint(self, browser: str) -> Dict[str, Any]:
        """Analyze WebGL fingerprint vector"""
        return {
            'vector_type': 'webgl_fingerprint',
            'current_value': 'gpu_signature_detected',
            'uniqueness_score': 90,
            'spoofing_difficulty': 'hard',
            'detection_methods': ['WebGL renderer info', 'GPU capabilities'],
            'spoofing_effectiveness': 'medium'
        }
    
    def _analyze_font_fingerprint(self) -> Dict[str, Any]:
        """Analyze font fingerprint vector"""
        return {
            'vector_type': 'font_fingerprint',
            'current_value': 'system_fonts_detected',
            'uniqueness_score': 70,
            'spoofing_difficulty': 'medium',
            'detection_methods': ['Font enumeration', 'Canvas text rendering'],
            'spoofing_effectiveness': 'medium'
        }
    
    def _analyze_timezone_fingerprint(self) -> Dict[str, Any]:
        """Analyze timezone fingerprint vector"""
        return {
            'vector_type': 'timezone_fingerprint',
            'current_value': 'system_timezone_detected',
            'uniqueness_score': 30,
            'spoofing_difficulty': 'easy',
            'detection_methods': ['Date.getTimezoneOffset()', 'Intl.DateTimeFormat'],
            'spoofing_effectiveness': 'high'
        }
    
    def _calculate_vector_risk(self, vector: str, data: Dict[str, Any]) -> int:
        """Calculate risk score for a fingerprint vector"""
        uniqueness = data.get('uniqueness_score', 50)
        difficulty = data.get('spoofing_difficulty', 'medium')
        
        # Adjust based on spoofing difficulty
        difficulty_multiplier = {
            'easy': 0.8,
            'medium': 1.0,
            'hard': 1.2
        }.get(difficulty, 1.0)
        
        return min(int(uniqueness * difficulty_multiplier), 100)
    
    def _identify_spoofing_opportunities(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify opportunities for effective fingerprint spoofing"""
        opportunities = []
        
        for vector in analysis['privacy_risk_assessment']['high_risk_vectors']:
            vector_data = analysis['fingerprint_vectors'][vector]
            if vector_data.get('spoofing_effectiveness') in ['high', 'medium']:
                opportunities.append({
                    'vector': vector,
                    'priority': 'high',
                    'effectiveness': vector_data.get('spoofing_effectiveness'),
                    'difficulty': vector_data.get('spoofing_difficulty'),
                    'impact': 'Significantly reduces fingerprint uniqueness'
                })
        
        return opportunities
    
    def _generate_fingerprint_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate fingerprint protection recommendations"""
        recommendations = []
        
        risk_score = analysis['privacy_risk_assessment']['overall_risk_score']
        
        if risk_score > 70:
            recommendations.append("🚨 HIGH FINGERPRINT RISK: Immediate spoofing recommended")
        elif risk_score > 40:
            recommendations.append("⚠️ MODERATE FINGERPRINT RISK: Consider selective spoofing")
        else:
            recommendations.append("✅ LOW FINGERPRINT RISK: Current protection is adequate")
        
        high_risk_vectors = analysis['privacy_risk_assessment']['high_risk_vectors']
        
        if 'canvas_fingerprint' in high_risk_vectors:
            recommendations.append("🎨 Enable canvas fingerprint noise injection")
        
        if 'webgl_fingerprint' in high_risk_vectors:
            recommendations.append("🖥️ Spoof WebGL renderer information")
        
        if 'user_agent' in high_risk_vectors:
            recommendations.append("🌐 Rotate User-Agent strings regularly")
        
        recommendations.extend([
            "🛡️ Use browser extensions for automated spoofing",
            "🔄 Rotate fingerprint profiles regularly",
            "📱 Test fingerprint uniqueness periodically",
            "🎯 Focus on high-impact, low-difficulty vectors first"
        ])
        
        return recommendations
    
    def _generate_profile_id(self) -> str:
        """Generate unique profile ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
        return f"profile_{timestamp}_{random_suffix}"
    
    def _generate_spoofed_user_agent(self, profile_type: str, browser: str) -> str:
        """Generate spoofed user agent string"""
        key = f"{browser}_{self.platform}"
        agents = self.user_agents.get(key, self.user_agents.get(f"{browser}_windows", []))
        
        if not agents:
            return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        
        if profile_type == 'random':
            return random.choice(agents)
        elif profile_type == 'common':
            return agents[0]  # Most common
        else:  # stealth
            return random.choice(agents)
    
    def _generate_spoofed_screen_resolution(self, profile_type: str) -> Dict[str, int]:
        """Generate spoofed screen resolution"""
        if profile_type == 'common':
            # Use most popular resolution
            return {'width': 1920, 'height': 1080}
        elif profile_type == 'random':
            return random.choice(self.screen_resolutions)
        else:  # stealth
            # Use medium popularity resolution
            medium_res = [r for r in self.screen_resolutions if r['popularity'] == 'medium']
            return random.choice(medium_res) if medium_res else {'width': 1366, 'height': 768}
    
    def _generate_spoofed_viewport(self, profile_type: str) -> Dict[str, int]:
        """Generate spoofed viewport size"""
        # Viewport is typically smaller than screen resolution
        screen = self._generate_spoofed_screen_resolution(profile_type)
        return {
            'width': screen['width'] - random.randint(0, 100),
            'height': screen['height'] - random.randint(100, 200)
        }
    
    def _generate_spoofed_timezone(self, profile_type: str) -> str:
        """Generate spoofed timezone"""
        if profile_type == 'common':
            return 'America/New_York'  # Most common
        else:
            return random.choice(self.timezone_database)
    
    def _generate_spoofed_language(self, profile_type: str) -> List[str]:
        """Generate spoofed language preferences"""
        common_languages = ['en-US', 'en-GB', 'en']
        diverse_languages = ['en-US', 'es-ES', 'fr-FR', 'de-DE', 'ja-JP', 'zh-CN']
        
        if profile_type == 'common':
            return common_languages
        else:
            return random.sample(diverse_languages, k=random.randint(1, 3))
    
    def _generate_spoofed_platform(self, profile_type: str) -> str:
        """Generate spoofed platform information"""
        platforms = ['Win32', 'MacIntel', 'Linux x86_64']
        
        if profile_type == 'common':
            return 'Win32'  # Most common
        else:
            return random.choice(platforms)
    
    def _generate_canvas_noise_config(self) -> Dict[str, Any]:
        """Generate canvas noise injection configuration"""
        return {
            'enabled': True,
            'noise_level': random.uniform(0.1, 0.5),
            'randomize_per_session': True,
            'methods': ['pixel_noise', 'color_shift', 'geometric_distortion']
        }
    
    def _generate_webgl_noise_config(self) -> Dict[str, Any]:
        """Generate WebGL noise injection configuration"""
        return {
            'enabled': True,
            'spoof_renderer': True,
            'spoof_vendor': True,
            'add_parameter_noise': True,
            'fake_extensions': random.sample([
                'WEBGL_debug_renderer_info',
                'OES_texture_float',
                'WEBGL_lose_context'
            ], k=2)
        }
    
    def _generate_font_masking_config(self) -> Dict[str, Any]:
        """Generate font masking configuration"""
        return {
            'enabled': True,
            'mask_system_fonts': True,
            'provide_fake_fonts': True,
            'randomize_font_metrics': True,
            'common_fonts_only': True
        }

    def _generate_extension_config(self, browser: str) -> Dict[str, Any]:
        """Generate browser extension configuration"""
        return {
            'browser': browser,
            'extension_type': 'fingerprint_spoofer',
            'permissions': ['activeTab', 'storage'],
            'content_scripts': ['content.js'],
            'background_scripts': ['background.js']
        }

    def _generate_userscript_config(self) -> Dict[str, Any]:
        """Generate userscript configuration"""
        return {
            'script_type': 'userscript',
            'run_at': 'document_start',
            'match_patterns': ['*://*/*'],
            'grant_permissions': ['none']
        }

    def _generate_browser_flags(self, browser: str) -> List[str]:
        """Generate browser command line flags for privacy"""
        common_flags = [
            '--disable-background-timer-throttling',
            '--disable-renderer-backgrounding',
            '--disable-backgrounding-occluded-windows'
        ]

        if browser == 'chrome':
            return common_flags + [
                '--disable-features=VizDisplayCompositor',
                '--disable-gpu-sandbox'
            ]
        elif browser == 'firefox':
            return [
                'privacy.resistFingerprinting=true',
                'privacy.trackingprotection.enabled=true'
            ]
        else:
            return common_flags

    def _validate_profile_consistency(self, profile: Dict[str, Any]) -> Dict[str, Any]:
        """Validate profile consistency"""
        return {
            'consistency_score': 85,
            'potential_conflicts': [],
            'validation_status': 'passed'
        }

    def _preview_user_agent_spoofing(self) -> Dict[str, Any]:
        """Preview user agent spoofing changes"""
        return {
            'current': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'proposed': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'change_type': 'version_randomization'
        }

    def _preview_canvas_noise(self) -> Dict[str, Any]:
        """Preview canvas noise injection"""
        return {
            'method': 'pixel_noise_injection',
            'noise_level': 0.3,
            'randomization': 'per_session'
        }

    def _preview_webgl_spoofing(self) -> Dict[str, Any]:
        """Preview WebGL spoofing changes"""
        return {
            'renderer_spoofing': True,
            'vendor_spoofing': True,
            'parameter_noise': True
        }

    def _preview_font_masking(self) -> Dict[str, Any]:
        """Preview font masking changes"""
        return {
            'system_fonts_hidden': True,
            'fake_fonts_provided': True,
            'metrics_randomized': True
        }

    def _preview_timezone_spoofing(self) -> Dict[str, Any]:
        """Preview timezone spoofing changes"""
        return {
            'current_timezone': 'America/New_York',
            'spoofed_timezone': 'Europe/London',
            'offset_change': '+5 hours'
        }

    def _calculate_spoofed_fingerprint(self, current: Dict[str, Any], changes: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate fingerprint after spoofing"""
        spoofed = current.copy()

        # Simulate risk reduction from spoofing
        risk_reduction = len(changes) * 15  # 15% reduction per spoofed vector
        new_risk = max(0, spoofed['privacy_risk_assessment']['overall_risk_score'] - risk_reduction)

        spoofed['privacy_risk_assessment']['overall_risk_score'] = new_risk
        spoofed['spoofing_applied'] = True
        spoofed['spoofed_vectors'] = list(changes.keys())

        return spoofed

    def _create_extension_manifest(self, extension_dir: Path, browser: str, profile: Dict[str, Any]) -> Path:
        """Create extension manifest file"""
        manifest_content = {
            "manifest_version": 3 if browser == 'chrome' else 2,
            "name": "Augment VIP Fingerprint Spoofer",
            "version": "1.0.0",
            "description": "Privacy protection through fingerprint spoofing",
            "permissions": ["activeTab", "storage"],
            "content_scripts": [{
                "matches": ["*://*/*"],
                "js": ["content.js"],
                "run_at": "document_start"
            }],
            "background": {
                "scripts": ["background.js"] if browser != 'chrome' else None,
                "service_worker": "background.js" if browser == 'chrome' else None
            },
            "action": {
                "default_popup": "popup.html",
                "default_title": "Augment VIP Privacy"
            }
        }

        manifest_path = extension_dir / 'manifest.json'
        with open(manifest_path, 'w') as f:
            json.dump(manifest_content, f, indent=2)

        return manifest_path

    def _create_content_script(self, extension_dir: Path, profile: Dict[str, Any]) -> Path:
        """Create content script for fingerprint spoofing"""
        script_content = '''
// Augment VIP Fingerprint Spoofer Content Script
(function() {
    'use strict';

    // User Agent Spoofing
    Object.defineProperty(navigator, 'userAgent', {
        get: function() { return 'SPOOFED_USER_AGENT'; }
    });

    // Canvas Fingerprint Protection
    const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
    HTMLCanvasElement.prototype.toDataURL = function() {
        // Add noise to canvas data
        const context = this.getContext('2d');
        const imageData = context.getImageData(0, 0, this.width, this.height);

        // Add minimal noise
        for (let i = 0; i < imageData.data.length; i += 4) {
            imageData.data[i] += Math.random() * 2 - 1; // Red
            imageData.data[i + 1] += Math.random() * 2 - 1; // Green
            imageData.data[i + 2] += Math.random() * 2 - 1; // Blue
        }

        context.putImageData(imageData, 0, 0);
        return originalToDataURL.apply(this, arguments);
    };

    // WebGL Fingerprint Protection
    const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
    WebGLRenderingContext.prototype.getParameter = function(parameter) {
        if (parameter === this.RENDERER) {
            return 'Intel Iris OpenGL Engine';
        }
        if (parameter === this.VENDOR) {
            return 'Intel Inc.';
        }
        return originalGetParameter.apply(this, arguments);
    };

    console.log('Augment VIP: Fingerprint protection active');
})();
        '''.replace('SPOOFED_USER_AGENT', profile['spoofed_attributes']['user_agent'])

        script_path = extension_dir / 'content.js'
        with open(script_path, 'w') as f:
            f.write(script_content)

        return script_path

    def _create_background_script(self, extension_dir: Path, profile: Dict[str, Any]) -> Path:
        """Create background script for extension"""
        script_content = '''
// Augment VIP Background Script
chrome.runtime.onInstalled.addListener(() => {
    console.log('Augment VIP Fingerprint Spoofer installed');
});

// Store spoofing configuration
chrome.storage.local.set({
    'spoofingEnabled': true,
    'profileId': 'PROFILE_ID',
    'lastUpdate': Date.now()
});
        '''.replace('PROFILE_ID', profile['profile_metadata']['profile_id'])

        script_path = extension_dir / 'background.js'
        with open(script_path, 'w') as f:
            f.write(script_content)

        return script_path

    def _create_popup_interface(self, extension_dir: Path, profile: Dict[str, Any]) -> Path:
        """Create popup HTML interface"""
        popup_content = '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body { width: 300px; padding: 10px; font-family: Arial, sans-serif; }
        .header { background: #6366f1; color: white; padding: 10px; margin: -10px -10px 10px -10px; }
        .status { padding: 10px; background: #f0f9ff; border-radius: 5px; margin: 10px 0; }
        .toggle { margin: 10px 0; }
        button { background: #6366f1; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; }
        button:hover { background: #4f46e5; }
    </style>
</head>
<body>
    <div class="header">
        <h3>🛡️ Augment VIP Privacy</h3>
    </div>

    <div class="status">
        <strong>Status:</strong> <span id="status">Active</span><br>
        <strong>Profile:</strong> PROFILE_TYPE<br>
        <strong>Vectors:</strong> <span id="vectors">5 protected</span>
    </div>

    <div class="toggle">
        <button id="toggleBtn">Disable Protection</button>
    </div>

    <div style="font-size: 12px; color: #666; margin-top: 10px;">
        Fingerprint spoofing active. Your privacy is protected.
    </div>

    <script>
        document.getElementById('toggleBtn').addEventListener('click', function() {
            // Toggle spoofing on/off
            console.log('Toggle spoofing');
        });
    </script>
</body>
</html>
        '''.replace('PROFILE_TYPE', profile['profile_metadata']['profile_type'])

        popup_path = extension_dir / 'popup.html'
        with open(popup_path, 'w') as f:
            f.write(popup_content)

        return popup_path

    def _generate_installation_instructions(self, browser: str, extension_dir: Path) -> List[str]:
        """Generate installation instructions for the extension"""
        if browser == 'chrome':
            return [
                "1. Open Chrome and go to chrome://extensions/",
                "2. Enable 'Developer mode' in the top right",
                "3. Click 'Load unpacked' and select the extension directory:",
                f"   {extension_dir}",
                "4. The extension will be installed and active",
                "5. Look for the Augment VIP icon in the toolbar"
            ]
        elif browser == 'firefox':
            return [
                "1. Open Firefox and go to about:debugging",
                "2. Click 'This Firefox' in the sidebar",
                "3. Click 'Load Temporary Add-on'",
                f"4. Navigate to {extension_dir} and select manifest.json",
                "5. The extension will be loaded temporarily",
                "6. For permanent installation, the extension needs to be signed"
            ]
        else:
            return [
                "1. Open your browser's extension management page",
                "2. Enable developer mode",
                "3. Load the unpacked extension from:",
                f"   {extension_dir}",
                "4. Follow browser-specific instructions"
            ]
