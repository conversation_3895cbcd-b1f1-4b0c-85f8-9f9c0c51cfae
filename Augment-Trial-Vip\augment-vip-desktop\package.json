{"name": "augment-vip-desktop", "private": true, "version": "2.0.0", "description": "Professional Privacy Protection Suite - Advanced anti-footprint capabilities with real-time monitoring", "author": "Augment VIP <<EMAIL>>", "type": "module", "main": "electron/main.cjs", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "preview": "vite preview", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && cross-env NODE_ENV=development electron .\"", "build-electron": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never", "package:win": "npm run build && electron-builder --win --publish=never", "package:mac": "npm run build && electron-builder --mac --publish=never", "package:linux": "npm run build && electron-builder --linux --publish=never", "package:all": "npm run build && electron-builder --win --mac --linux --publish=never", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.5", "@radix-ui/react-tooltip": "^1.1.8", "@radix-ui/react-dropdown-menu": "^2.1.8", "@radix-ui/react-select": "^2.1.8", "@radix-ui/react-alert-dialog": "^1.1.8", "@radix-ui/react-popover": "^1.1.8", "@tanstack/react-query": "^5.59.0", "axios": "^1.7.9", "framer-motion": "^12.0.0", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.54.0", "react-router-dom": "^7.1.1", "recharts": "^2.15.0", "socket.io-client": "^4.8.1", "zod": "^3.24.1", "zustand": "^5.0.6", "immer": "^10.1.1", "date-fns": "^4.1.0", "clsx": "^2.1.1", "tailwind-merge": "^2.5.5"}, "devDependencies": {"@eslint/js": "^9.29.0", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.15", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "@vitejs/plugin-react-swc": "^3.7.2", "autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "electron": "^37.2.0", "electron-builder": "^26.0.12", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0", "wait-on": "^8.0.3", "@storybook/react": "^8.4.7", "@storybook/react-vite": "^8.4.7", "@testing-library/react": "^16.1.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/user-event": "^14.5.2", "vitest": "^2.1.8", "jsdom": "^26.0.0", "msw": "^2.6.8", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9"}, "build": {"appId": "com.augment.vip.desktop", "productName": "Augment VIP Desktop", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "electron/**/*", "node_modules/**/*"], "extraResources": [{"from": "../augment-vip", "to": "augment-vip", "filter": ["**/*", "!**/.git/**/*", "!**/node_modules/**/*"]}], "win": {"target": [{"target": "portable", "arch": ["x64"]}], "verifyUpdateCodeSignature": false, "signAndEditExecutable": false}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "category": "public.app-category.utilities", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "electron/entitlements.mac.plist", "entitlementsInherit": "electron/entitlements.mac.plist"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}, {"target": "rpm", "arch": ["x64"]}], "category": "Utility"}}}