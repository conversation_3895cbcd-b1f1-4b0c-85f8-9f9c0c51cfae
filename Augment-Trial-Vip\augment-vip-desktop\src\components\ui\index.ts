/**
 * UI Component Library
 *
 * Modern, accessible, and themeable components built with Tailwind CSS
 */

// Base components
export { Button, type ButtonProps, type ButtonVariant } from "./Button";
export {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
  type CardProps
} from "./Card";
export { Badge, type BadgeProps, type BadgeVariant, type BadgeSize } from "./Badge";
export {
  Progress,
  CircularProgress,
  type ProgressProps,
  type CircularProgressProps,
  type ProgressVariant,
  type ProgressSize
} from "./Progress";

// Advanced components removed for compatibility
// Toast, Modal, Loading, Input, and Dropdown components are not available in this version

// Utility exports
export { cn } from "@/utils/cn";
export { colors, sizes, type Size } from "@/utils/theme";
