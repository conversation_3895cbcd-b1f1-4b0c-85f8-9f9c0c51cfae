const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const os = require('os');

class PythonService {
  constructor() {
    this.pythonPath = null;
    this.augmentVipPath = null;
    this.isInitialized = false;
  }

  async initialize() {
    if (this.isInitialized) return true;

    try {
      // Find the augment-vip directory
      const possiblePaths = [
        path.join(__dirname, '../../augment-vip'),
        path.join(process.cwd(), '../augment-vip'),
        path.join(os.homedir(), 'augment-vip')
      ];

      for (const augmentPath of possiblePaths) {
        if (fs.existsSync(augmentPath)) {
          this.augmentVipPath = augmentPath;
          break;
        }
      }

      if (!this.augmentVipPath) {
        throw new Error('Augment VIP Python package not found');
      }

      // Check for virtual environment
      const venvPaths = [
        path.join(this.augmentVipPath, '.venv', 'Scripts', 'python.exe'), // Windows
        path.join(this.augmentVipPath, '.venv', 'bin', 'python'), // Unix
      ];

      for (const pythonPath of venvPaths) {
        if (fs.existsSync(pythonPath)) {
          this.pythonPath = pythonPath;
          break;
        }
      }

      // Fallback to system Python
      if (!this.pythonPath) {
        this.pythonPath = process.platform === 'win32' ? 'python' : 'python3';
      }

      // Test the Python installation
      await this.testPythonInstallation();
      this.isInitialized = true;
      return true;

    } catch (error) {
      console.error('Failed to initialize Python service:', error);
      return false;
    }
  }

  async testPythonInstallation() {
    return new Promise((resolve, reject) => {
      const child = spawn(this.pythonPath, ['-m', 'augment_vip.cli', '--version'], {
        cwd: this.augmentVipPath,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';

      child.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', (code) => {
        if (code === 0) {
          resolve({ success: true, version: stdout.trim() });
        } else {
          reject(new Error(`Python test failed: ${stderr}`));
        }
      });

      child.on('error', (error) => {
        reject(error);
      });
    });
  }

  async executeCommand(command, args = [], onProgress = null) {
    if (!this.isInitialized) {
      const initialized = await this.initialize();
      if (!initialized) {
        throw new Error('Python service not initialized');
      }
    }

    return new Promise((resolve, reject) => {
      const fullArgs = ['-m', 'augment_vip.cli', command, ...args];
      
      console.log(`Executing: ${this.pythonPath} ${fullArgs.join(' ')}`);
      
      const child = spawn(this.pythonPath, fullArgs, {
        cwd: this.augmentVipPath,
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env, PYTHONUNBUFFERED: '1' }
      });

      let stdout = '';
      let stderr = '';

      child.stdout.on('data', (data) => {
        const text = data.toString();
        stdout += text;
        if (onProgress) {
          onProgress({ type: 'stdout', data: text });
        }
      });

      child.stderr.on('data', (data) => {
        const text = data.toString();
        stderr += text;
        if (onProgress) {
          onProgress({ type: 'stderr', data: text });
        }
      });

      child.on('close', (code) => {
        resolve({
          success: code === 0,
          stdout: stdout.trim(),
          stderr: stderr.trim(),
          exitCode: code
        });
      });

      child.on('error', (error) => {
        reject(error);
      });
    });
  }

  async getSystemInfo() {
    return {
      platform: process.platform,
      arch: process.arch,
      version: os.release(),
      homedir: os.homedir(),
      username: os.userInfo().username,
      pythonPath: this.pythonPath,
      augmentVipPath: this.augmentVipPath,
      isInitialized: this.isInitialized
    };
  }

  async checkVsCodeInstallation() {
    const platform = process.platform;
    let vscodePaths = [];

    switch (platform) {
      case 'win32':
        vscodePaths = [
          path.join(os.homedir(), 'AppData', 'Roaming', 'Code'),
          path.join(os.homedir(), 'AppData', 'Local', 'Programs', 'Microsoft VS Code'),
          path.join(process.env.LOCALAPPDATA || '', 'Programs', 'Microsoft VS Code'),
          path.join(process.env.APPDATA || '', 'Code')
        ];
        break;
      case 'darwin':
        vscodePaths = [
          '/Applications/Visual Studio Code.app',
          path.join(os.homedir(), 'Applications', 'Visual Studio Code.app'),
          path.join(os.homedir(), 'Library', 'Application Support', 'Code')
        ];
        break;
      case 'linux':
        vscodePaths = [
          path.join(os.homedir(), '.config', 'Code'),
          '/usr/share/code',
          '/opt/visual-studio-code',
          '/snap/code'
        ];
        break;
    }

    const foundPaths = [];
    for (const vscodePath of vscodePaths) {
      try {
        if (fs.existsSync(vscodePath)) {
          const stats = fs.statSync(vscodePath);
          foundPaths.push({
            path: vscodePath,
            type: stats.isDirectory() ? 'directory' : 'file',
            size: stats.size,
            modified: stats.mtime
          });
        }
      } catch (error) {
        // Ignore access errors
      }
    }

    return {
      platform,
      found: foundPaths.length > 0,
      paths: foundPaths,
      totalPaths: foundPaths.length
    };
  }

  async findDatabaseFiles() {
    const vsCodeInfo = await this.checkVsCodeInstallation();
    const dbFiles = [];

    for (const pathInfo of vsCodeInfo.paths) {
      if (pathInfo.type === 'directory') {
        try {
          const searchPaths = [
            path.join(pathInfo.path, 'User', 'workspaceStorage'),
            path.join(pathInfo.path, 'logs'),
            path.join(pathInfo.path, 'CachedExtensions')
          ];

          for (const searchPath of searchPaths) {
            if (fs.existsSync(searchPath)) {
              const files = this.findSqliteFiles(searchPath);
              dbFiles.push(...files);
            }
          }
        } catch (error) {
          console.error(`Error searching ${pathInfo.path}:`, error);
        }
      }
    }

    return dbFiles;
  }

  findSqliteFiles(directory) {
    const files = [];
    
    try {
      const items = fs.readdirSync(directory);
      
      for (const item of items) {
        const fullPath = path.join(directory, item);
        const stats = fs.statSync(fullPath);
        
        if (stats.isFile() && (item.endsWith('.db') || item.endsWith('.sqlite'))) {
          files.push({
            path: fullPath,
            name: item,
            size: stats.size,
            modified: stats.mtime
          });
        } else if (stats.isDirectory() && !item.startsWith('.')) {
          // Recursively search subdirectories (limit depth to avoid infinite loops)
          const subFiles = this.findSqliteFiles(fullPath);
          files.push(...subFiles);
        }
      }
    } catch (error) {
      // Ignore permission errors
    }
    
    return files;
  }
}

module.exports = PythonService;
