export interface ElectronAPI {
  // Python CLI operations
  executePythonCommand: (
    command: string,
    args?: string[]
  ) => Promise<{
    success: boolean;
    stdout: string;
    stderr: string;
    exitCode: number;
  }>;

  // System information
  getSystemInfo: () => Promise<{
    platform: string;
    arch: string;
    version: string;
    homedir: string;
    username: string;
    pythonPath: string | null;
    augmentVipPath: string | null;
    isInitialized: boolean;
  }>;

  getAppVersion: () => Promise<string>;

  // System state analysis
  analyzeSystemState: () => Promise<{
    success: boolean;
    data?: {
      timestamp: Date;
      platform: string;
      processes: {
        running: Array<{ name: string; pid: number; command: string }>;
        critical: Array<{ name: string; pid: number; command: string }>;
        databases: Array<{ name: string; pid: number; command: string }>;
      };
      connections: {
        database: any[];
        network: any[];
      };
      filesystem: {
        locks: any[];
        permissions: Array<{
          path: string;
          readable: boolean;
          writable: boolean;
          error?: string;
        }>;
        diskSpace: {
          total: number;
          free: number;
          used: number;
          freePercentage: number;
        };
      };
      vscode: {
        running: boolean;
        instances: Array<{ name: string; pid: number; command: string }>;
        workspaces: any[];
      };
      databases: {
        postgresql: { running: boolean; connections: number };
        mysql: { running: boolean; connections: number };
        sqlite: { openFiles: any[] };
      };
      risks: Array<{
        level: string;
        category: string;
        message: string;
        impact: string;
        [key: string]: any;
      }>;
      recommendations: Array<{
        action: string;
        reason: string;
        priority: string;
      }>;
      safeToOperate: boolean;
    };
    error?: string;
  }>;

  // VS Code detection
  checkVsCodeInstallation: () => Promise<{
    platform: string;
    found: boolean;
    paths: Array<{
      path: string;
      type: "directory" | "file";
      size: number;
      modified: Date;
    }>;
    totalPaths: number;
  }>;

  // Database operations
  findDatabaseFiles: () => Promise<
    Array<{
      path: string;
      name: string;
      size: number;
      modified: Date;
    }>
  >;

  scanDatabases: () => Promise<{
    success: boolean;
    data?: {
      sqlite: Array<{
        path: string;
        name: string;
        size: number;
        modified: Date;
        type: string;
        canClean: boolean;
      }>;
      postgresql: Array<{
        name: string;
        size: number;
        type: string;
        canClean: boolean;
        connectionInfo: any;
      }>;
      mysql: Array<{
        name: string;
        size: number;
        type: string;
        canClean: boolean;
        connectionInfo: any;
      }>;
      totalSize: number;
      scanTime: number;
    };
    error?: string;
  }>;

  cleanDatabase: (databaseInfo: any) => Promise<{
    success: boolean;
    data?: any;
    error?: string;
  }>;

  backupDatabase: (
    databaseInfo: any,
    backupPath: string
  ) => Promise<{
    success: boolean;
    data?: any;
    error?: string;
  }>;

  // File system operations
  checkFileExists: (filePath: string) => Promise<boolean>;
  readFile: (filePath: string) => Promise<{
    success: boolean;
    content?: string;
    error?: string;
  }>;
  openPath: (path: string) => Promise<{
    success: boolean;
    error?: string;
  }>;
  copyToClipboard: (text: string) => Promise<{
    success: boolean;
    error?: string;
  }>;

  // Event listeners
  onCommandOutput: (
    callback: (data: { type: "stdout" | "stderr"; data: string }) => void
  ) => void;

  onQuickClean: (callback: () => void) => void;

  onScanProgress: (
    callback: (data: { step: string; progress: number }) => void
  ) => void;

  onCleanProgress: (
    callback: (data: { step: string; progress: number }) => void
  ) => void;

  onBackupProgress: (
    callback: (data: { step: string; progress: number }) => void
  ) => void;

  onStateAnalysisProgress: (
    callback: (data: { step: string; progress: number }) => void
  ) => void;

  // Remove listeners
  removeAllListeners: (channel: string) => void;
}

export interface ThemeAPI {
  setTheme: (theme: "light" | "dark") => void;
  getTheme: () => "light" | "dark";
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
    themeAPI: ThemeAPI;
  }
}
