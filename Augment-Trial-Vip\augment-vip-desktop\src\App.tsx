import React, { useEffect } from "react";
import Layout from "./components/Layout";
import Dashboard from "./components/Dashboard";
import Operations from "./components/Operations";
import useAppStore from "./store/useAppStore";

function App() {
  const { activeTab, theme, setTheme } = useAppStore();

  useEffect(() => {
    // Initialize theme from system/storage
    if (window.themeAPI) {
      const savedTheme = window.themeAPI.getTheme();
      setTheme(savedTheme);
    }

    // Set up command output listener
    if (window.electronAPI) {
      window.electronAPI.onCommandOutput((data) => {
        console.log("Command output:", data);
      });

      window.electronAPI.onQuickClean(() => {
        // Handle quick clean from system tray
        console.log("Quick clean triggered from system tray");
      });
    }

    // Cleanup listeners on unmount
    return () => {
      if (window.electronAPI) {
        window.electronAPI.removeAllListeners("command-output");
        window.electronAPI.removeAllListeners("quick-clean");
      }
    };
  }, [setTheme]);

  const renderActiveTab = () => {
    switch (activeTab) {
      case "dashboard":
        return <Dashboard />;
      case "operations":
        return <Operations />;
      case "backups":
        return <div className="p-6">Backup Management - Coming Soon</div>;
      case "settings":
        return <div className="p-6">Settings Panel - Coming Soon</div>;
      default:
        return <Dashboard />;
    }
  };

  return (
    <div className={theme === "dark" ? "dark" : ""}>
      <Layout>{renderActiveTab()}</Layout>
    </div>
  );
}

export default App;
