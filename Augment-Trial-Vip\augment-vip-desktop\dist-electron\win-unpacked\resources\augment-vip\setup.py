from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="augment-vip",
    version="2.0.0",
    packages=find_packages(),
    include_package_data=True,
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.4.3",
            "pytest-asyncio>=0.21.1",
            "pytest-cov>=4.1.0",
            "black>=23.11.0",
            "isort>=5.12.0",
            "mypy>=1.7.1",
            "pre-commit>=3.6.0",
        ],
        "postgresql": ["asyncpg>=0.29.0"],
        "mysql": ["aiomysql>=0.2.0"],
        "redis": ["redis>=5.0.1"],
        "monitoring": ["sentry-sdk[fastapi]>=1.38.0", "prometheus-client>=0.19.0"],
    },
    entry_points={
        "console_scripts": [
            "augment-vip=augment_vip.cli:cli",
            "augment-vip-server=augment_vip.main:app",
        ],
    },
    python_requires=">=3.8",
    author="Augment VIP Team",
    author_email="<EMAIL>",
    description="Enterprise-grade backend for Augment Trial VIP system",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/augment-vip/augment-trial-vip",
    project_urls={
        "Bug Tracker": "https://github.com/augment-vip/augment-trial-vip/issues",
        "Documentation": "https://docs.augmentvip.com",
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Topic :: Software Development :: Libraries :: Application Frameworks",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Framework :: FastAPI",
    ],
    keywords="fastapi, database, cleanup, vscode, trial, management",
)
