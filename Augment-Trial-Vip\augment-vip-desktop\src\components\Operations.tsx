import React, { useState, useEffect } from 'react';
import {
  Database,
  Shield,
  Play,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  Trash2,
  Eye,
  Scan,
  Activity
} from 'lucide-react';
import useAppStore from '../store/useAppStore';
import ProgressModal from './ProgressModal';
import { VSCodeCleanupModal } from './VSCodeCleanupModal';
import { ModifyIDsModal } from './ModifyIDsModal';
import PrivacyScannerModal from './PrivacyScannerModal';
import AdvancedAntiFootprintModal from './AdvancedAntiFootprintModal';
import RealTimePrivacyMonitor from './RealTimePrivacyMonitor';

const Operations: React.FC = () => {
  const {
    isOperationRunning,
    operationLogs,
    startOperation,
    finishOperation,
    updateOperationProgress,
    clearOperationLogs
  } = useAppStore();

  const [showProgressModal, setShowProgressModal] = useState(false);
  const [showCleanupPreview, setShowCleanupPreview] = useState(false);
  const [showModifyIDsPreview, setShowModifyIDsPreview] = useState(false);
  const [showPrivacyScanner, setShowPrivacyScanner] = useState(false);
  const [showAdvancedAntiFootprint, setShowAdvancedAntiFootprint] = useState(false);
  const [showRealTimeMonitor, setShowRealTimeMonitor] = useState(false);
  const [selectedOperation, setSelectedOperation] = useState<'clean' | 'modify-ids' | 'all' | 'privacy-scan' | 'advanced-anti-footprint'>('clean');
  const [databaseFiles, setDatabaseFiles] = useState<Array<{
    path: string;
    name: string;
    size: number;
    modified: Date;
  }>>([]);

  useEffect(() => {
    // Load database files when component mounts
    const loadDatabaseFiles = async () => {
      if (window.electronAPI) {
        try {
          const files = await window.electronAPI.findDatabaseFiles();
          setDatabaseFiles(files);
        } catch (error) {
          console.error('Failed to load database files:', error);
        }
      }
    };

    loadDatabaseFiles();
  }, []);

  const handlePreviewOperation = (operation: 'clean' | 'modify-ids' | 'all' | 'privacy-scan' | 'advanced-anti-footprint' | 'real-time-monitor') => {
    if (operation === 'clean') {
      setShowCleanupPreview(true);
    } else if (operation === 'modify-ids') {
      setShowModifyIDsPreview(true);
    } else if (operation === 'all') {
      // For 'all', show cleanup preview first, then modify IDs
      setShowCleanupPreview(true);
    } else if (operation === 'privacy-scan') {
      setShowPrivacyScanner(true);
    } else if (operation === 'advanced-anti-footprint') {
      setShowAdvancedAntiFootprint(true);
    } else if (operation === 'real-time-monitor') {
      setShowRealTimeMonitor(true);
    }
  };

  const handleOperation = async (operation: 'clean' | 'modify-ids' | 'all' | 'privacy-scan' | 'advanced-anti-footprint' | 'real-time-monitor') => {
    if (isOperationRunning) return;

    try {
      startOperation(operation);
      setShowProgressModal(true);

      // Set up progress simulation
      let progress = 0;
      const progressInterval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 85) progress = 85;
        updateOperationProgress(progress);
      }, 800);

      // Execute the actual operation
      const result = await window.electronAPI.executePythonCommand(operation);

      clearInterval(progressInterval);
      updateOperationProgress(100);

      setTimeout(() => {
        finishOperation(
          result.success,
          result.success ? `${operation} completed successfully` : `${operation} failed`,
          result.stderr || result.stdout
        );
      }, 500);

    } catch (error) {
      finishOperation(false, `Failed to execute ${operation}`, error instanceof Error ? error.message : 'Unknown error');
    }
  };

  const handleCleanupConfirm = () => {
    setShowCleanupPreview(false);
    handleOperation('clean');
  };

  const handleModifyIDsConfirm = () => {
    setShowModifyIDsPreview(false);
    handleOperation('modify-ids');
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const operations = [
    {
      id: 'clean' as const,
      title: 'Clean Databases',
      description: 'Remove Augment-related entries from VS Code databases',
      icon: Database,
      color: 'blue',
      details: 'This operation will scan VS Code database files and remove any entries containing "augment". Backups will be created automatically.'
    },
    {
      id: 'modify-ids' as const,
      title: 'Modify Telemetry IDs',
      description: 'Generate new random telemetry IDs for enhanced privacy',
      icon: Shield,
      color: 'green',
      details: 'This will generate new random machineId and devDeviceId values in VS Code\'s storage.json file.'
    },
    {
      id: 'all' as const,
      title: 'Run All Operations',
      description: 'Execute both database cleaning and ID modification',
      icon: Play,
      color: 'purple',
      details: 'This will run both the database cleaning and telemetry ID modification operations in sequence.'
    },
    {
      id: 'privacy-scan' as const,
      title: 'Privacy Scanner',
      description: 'Scan for telemetry, tracking, and privacy threats',
      icon: Scan,
      color: 'indigo',
      details: 'Advanced privacy scanner that detects browser telemetry, application tracking, and cache data. All scans are read-only and safe.'
    },
    {
      id: 'advanced-anti-footprint' as const,
      title: 'Advanced Anti-Footprint',
      description: 'Domain blocking, fingerprint spoofing, and network protection',
      icon: Shield,
      color: 'violet',
      details: 'Phase 5A features: Smart domain blocking with automatic backups, browser fingerprint spoofing, and advanced privacy protection.'
    },
    {
      id: 'real-time-monitor' as const,
      title: 'Real-Time Privacy Monitor',
      description: 'Live threat detection and privacy score monitoring',
      icon: Activity,
      color: 'blue',
      details: 'Real-time privacy monitoring with live threat detection, privacy score tracking, and automatic protection status updates.'
    }
  ];

  return (
    <div className="p-6 space-y-6">
      {/* Operation Selection */}
      <div className="bg-white dark:bg-dark-800 rounded-lg p-6 border border-gray-200 dark:border-dark-700">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Available Operations
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {operations.map((operation) => {
            const Icon = operation.icon;
            const isSelected = selectedOperation === operation.id;
            
            const getColorClasses = (color: string, isSelected: boolean) => {
              const colorMap = {
                blue: {
                  border: isSelected ? 'border-blue-500' : 'border-gray-200 dark:border-dark-700',
                  bg: isSelected ? 'bg-blue-50 dark:bg-blue-900/20' : '',
                  iconBg: 'bg-blue-100 dark:bg-blue-900',
                  iconText: 'text-blue-600 dark:text-blue-400'
                },
                green: {
                  border: isSelected ? 'border-green-500' : 'border-gray-200 dark:border-dark-700',
                  bg: isSelected ? 'bg-green-50 dark:bg-green-900/20' : '',
                  iconBg: 'bg-green-100 dark:bg-green-900',
                  iconText: 'text-green-600 dark:text-green-400'
                },
                purple: {
                  border: isSelected ? 'border-purple-500' : 'border-gray-200 dark:border-dark-700',
                  bg: isSelected ? 'bg-purple-50 dark:bg-purple-900/20' : '',
                  iconBg: 'bg-purple-100 dark:bg-purple-900',
                  iconText: 'text-purple-600 dark:text-purple-400'
                },
                indigo: {
                  border: isSelected ? 'border-indigo-500' : 'border-gray-200 dark:border-dark-700',
                  bg: isSelected ? 'bg-indigo-50 dark:bg-indigo-900/20' : '',
                  iconBg: 'bg-indigo-100 dark:bg-indigo-900',
                  iconText: 'text-indigo-600 dark:text-indigo-400'
                },
                violet: {
                  border: isSelected ? 'border-violet-500' : 'border-gray-200 dark:border-dark-700',
                  bg: isSelected ? 'bg-violet-50 dark:bg-violet-900/20' : '',
                  iconBg: 'bg-violet-100 dark:bg-violet-900',
                  iconText: 'text-violet-600 dark:text-violet-400'
                }
              };
              return colorMap[color as keyof typeof colorMap] || colorMap.blue;
            };

            const colorClasses = getColorClasses(operation.color, isSelected);

            return (
              <div
                key={operation.id}
                className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${colorClasses.border} ${colorClasses.bg} ${
                  !isSelected ? 'hover:border-gray-300 dark:hover:border-dark-600' : ''
                }`}
                onClick={() => setSelectedOperation(operation.id)}
              >
                <div className="flex items-center space-x-3 mb-3">
                  <div className={`p-2 rounded-lg ${colorClasses.iconBg}`}>
                    <Icon className={`w-5 h-5 ${colorClasses.iconText}`} />
                  </div>
                  <h3 className="font-medium text-gray-900 dark:text-gray-100">
                    {operation.title}
                  </h3>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  {operation.description}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-500">
                  {operation.details}
                </p>
              </div>
            );
          })}
        </div>
        
        <div className="mt-6 flex items-center justify-between">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Selected: <span className="font-medium">{operations.find(op => op.id === selectedOperation)?.title}</span>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={() => handlePreviewOperation(selectedOperation)}
              disabled={isOperationRunning}
              className="px-6 py-2 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300
                       rounded-lg hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors
                       font-medium disabled:opacity-50 disabled:cursor-not-allowed
                       flex items-center space-x-2"
            >
              <Eye className="w-4 h-4" />
              <span>Preview Changes</span>
            </button>
            <button
              onClick={() => handleOperation(selectedOperation)}
              disabled={isOperationRunning}
              className="px-6 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600
                       transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed
                       flex items-center space-x-2"
            >
              <Play className="w-4 h-4" />
              <span>Run Operation</span>
            </button>
          </div>
        </div>
      </div>

      {/* Database Files */}
      <div className="bg-white dark:bg-dark-800 rounded-lg p-6 border border-gray-200 dark:border-dark-700">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Detected Database Files
          </h2>
          <button
            onClick={() => window.electronAPI?.findDatabaseFiles().then(setDatabaseFiles)}
            className="px-3 py-1 text-sm bg-gray-100 dark:bg-dark-700 text-gray-700 dark:text-gray-300 
                     rounded-lg hover:bg-gray-200 dark:hover:bg-dark-600 transition-colors"
          >
            Refresh
          </button>
        </div>
        
        {databaseFiles.length > 0 ? (
          <div className="space-y-2">
            {databaseFiles.map((file, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-700 rounded-lg">
                <div className="flex items-center space-x-3">
                  <FileText className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {file.name}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {file.path}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {formatFileSize(file.size)}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-500">
                    {new Date(file.modified).toLocaleDateString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500 dark:text-gray-400 text-center py-8">
            No database files detected. Make sure VS Code is installed and has been used.
          </p>
        )}
      </div>

      {/* Operation History */}
      <div className="bg-white dark:bg-dark-800 rounded-lg p-6 border border-gray-200 dark:border-dark-700">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Operation History
          </h2>
          {operationLogs.length > 0 && (
            <button
              onClick={clearOperationLogs}
              className="px-3 py-1 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 
                       dark:hover:bg-red-900/20 rounded-lg transition-colors flex items-center space-x-1"
            >
              <Trash2 className="w-3 h-3" />
              <span>Clear</span>
            </button>
          )}
        </div>
        
        {operationLogs.length > 0 ? (
          <div className="space-y-3">
            {operationLogs.slice(0, 10).map((log) => (
              <div key={log.id} className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-dark-700 rounded-lg">
                <div className={`p-2 rounded-full ${
                  log.status === 'success' 
                    ? 'bg-green-100 dark:bg-green-900' 
                    : log.status === 'error'
                    ? 'bg-red-100 dark:bg-red-900'
                    : 'bg-yellow-100 dark:bg-yellow-900'
                }`}>
                  {log.status === 'success' ? (
                    <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400" />
                  ) : log.status === 'error' ? (
                    <AlertCircle className="w-4 h-4 text-red-600 dark:text-red-400" />
                  ) : (
                    <Clock className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
                  )}
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{log.message}</p>
                  <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                    <span>{log.timestamp.toLocaleString()}</span>
                    <span className="capitalize">{log.operation}</span>
                    <span className={`px-2 py-1 rounded-full ${
                      log.status === 'success' 
                        ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                        : log.status === 'error'
                        ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
                        : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
                    }`}>
                      {log.status}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500 dark:text-gray-400 text-center py-8">
            No operations have been performed yet.
          </p>
        )}
      </div>

      {/* Progress Modal */}
      <ProgressModal
        isOpen={showProgressModal}
        onClose={() => setShowProgressModal(false)}
      />

      {/* Preview Modals */}
      <VSCodeCleanupModal
        isOpen={showCleanupPreview}
        onClose={() => setShowCleanupPreview(false)}
        onConfirm={handleCleanupConfirm}
      />

      <ModifyIDsModal
        isOpen={showModifyIDsPreview}
        onClose={() => setShowModifyIDsPreview(false)}
        onConfirm={handleModifyIDsConfirm}
      />

      <PrivacyScannerModal
        isOpen={showPrivacyScanner}
        onClose={() => setShowPrivacyScanner(false)}
      />

      <AdvancedAntiFootprintModal
        isOpen={showAdvancedAntiFootprint}
        onClose={() => setShowAdvancedAntiFootprint(false)}
      />

      <RealTimePrivacyMonitor
        isOpen={showRealTimeMonitor}
        onClose={() => setShowRealTimeMonitor(false)}
      />
    </div>
  );
};

export default Operations;
