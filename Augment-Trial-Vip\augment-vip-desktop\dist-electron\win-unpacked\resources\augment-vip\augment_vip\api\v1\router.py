"""
Main API Router for v1
Combines all API endpoints into a single router
"""

from fastapi import APIRouter

from .endpoints import (
    auth,
    system,
    database,
    operations,
    monitoring,
    websocket,
)

# Create main API router
api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["Authentication"]
)

api_router.include_router(
    system.router,
    prefix="/system",
    tags=["System Management"]
)

api_router.include_router(
    database.router,
    prefix="/database",
    tags=["Database Operations"]
)

api_router.include_router(
    operations.router,
    prefix="/operations",
    tags=["System Operations"]
)

api_router.include_router(
    monitoring.router,
    prefix="/monitoring",
    tags=["Monitoring & Metrics"]
)

api_router.include_router(
    websocket.router,
    prefix="/ws",
    tags=["WebSocket"]
)
