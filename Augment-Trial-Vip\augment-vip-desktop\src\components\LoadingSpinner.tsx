import React from 'react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  color?: 'primary' | 'white' | 'gray';
  text?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  color = 'primary',
  text 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  const colorClasses = {
    primary: 'text-blue-600',
    white: 'text-white',
    gray: 'text-gray-600'
  };

  return (
    <div className="flex flex-col items-center justify-center space-y-3">
      <div className="relative">
        {/* Outer ring */}
        <div className={`${sizeClasses[size]} rounded-full border-2 border-gray-200 dark:border-gray-700`}></div>
        
        {/* Spinning ring */}
        <div className={`
          ${sizeClasses[size]} rounded-full border-2 border-transparent 
          border-t-blue-500 border-r-purple-500 animate-spin absolute top-0 left-0
        `}></div>
        
        {/* Inner glow */}
        <div className={`
          ${sizeClasses[size]} rounded-full bg-gradient-to-r from-blue-500/20 to-purple-500/20 
          absolute top-0 left-0 animate-pulse
        `}></div>
      </div>
      
      {text && (
        <p className={`text-sm font-medium ${colorClasses[color]} dark:text-gray-300 animate-pulse`}>
          {text}
        </p>
      )}
    </div>
  );
};

export default LoadingSpinner;
