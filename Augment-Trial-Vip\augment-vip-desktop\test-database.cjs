const DatabaseService = require('./electron/database-service.cjs');
const fs = require('fs');
const path = require('path');
const os = require('os');

async function testDatabaseService() {
  console.log('🧪 Testing Database Service...\n');
  
  const dbService = new DatabaseService();
  
  try {
    // Create a test SQLite database
    const testDbPath = path.join(os.tmpdir(), 'test-database.db');
    console.log(`📁 Creating test database at: ${testDbPath}`);
    
    // Create a simple SQLite database file
    fs.writeFileSync(testDbPath, 'SQLite format 3\x00');
    
    console.log('✅ Test database created\n');
    
    // Test database scanning
    console.log('🔍 Testing database scan...');
    
    const scanResult = await dbService.scanForDatabases((progress) => {
      console.log(`   Progress: ${progress.step} (${progress.progress}%)`);
    });
    
    console.log('\n📊 Scan Results:');
    console.log(`   SQLite databases found: ${scanResult.sqlite.length}`);
    console.log(`   PostgreSQL databases found: ${scanResult.postgresql.length}`);
    console.log(`   MySQL databases found: ${scanResult.mysql.length}`);
    console.log(`   Total size: ${formatBytes(scanResult.totalSize)}`);
    
    // Check if our test database was found
    const testDbFound = scanResult.sqlite.find(db => db.path === testDbPath);
    if (testDbFound) {
      console.log('✅ Test database found in scan results');
      console.log(`   Name: ${testDbFound.name}`);
      console.log(`   Size: ${formatBytes(testDbFound.size)}`);
      console.log(`   Can clean: ${testDbFound.canClean}`);
    } else {
      console.log('❌ Test database not found in scan results');
    }
    
    // Test database cleaning (if found and safe)
    if (testDbFound && testDbFound.canClean) {
      console.log('\n🧹 Testing database cleanup...');
      
      const cleanResult = await dbService.cleanDatabase(testDbFound, (progress) => {
        console.log(`   Cleanup: ${progress.step} (${progress.progress}%)`);
      });
      
      if (cleanResult.cleaned) {
        console.log('✅ Database cleanup successful');
        console.log(`   Backup created at: ${cleanResult.backupPath}`);
        console.log(`   Space freed: ${formatBytes(cleanResult.freedSpace)}`);
        
        // Verify the original file is gone
        if (!fs.existsSync(testDbPath)) {
          console.log('✅ Original database file removed');
        } else {
          console.log('❌ Original database file still exists');
        }
        
        // Verify backup exists
        if (fs.existsSync(cleanResult.backupPath)) {
          console.log('✅ Backup file exists');
          // Clean up backup
          fs.unlinkSync(cleanResult.backupPath);
          console.log('🧹 Backup file cleaned up');
        } else {
          console.log('❌ Backup file not found');
        }
      } else {
        console.log('❌ Database cleanup failed');
      }
    }
    
    console.log('\n🎉 Database service test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    // Clean up test files
    const testDbPath = path.join(os.tmpdir(), 'test-database.db');
    if (fs.existsSync(testDbPath)) {
      fs.unlinkSync(testDbPath);
      console.log('🧹 Test database cleaned up');
    }
  }
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Run the test
if (require.main === module) {
  testDatabaseService().catch(console.error);
}

module.exports = { testDatabaseService };
