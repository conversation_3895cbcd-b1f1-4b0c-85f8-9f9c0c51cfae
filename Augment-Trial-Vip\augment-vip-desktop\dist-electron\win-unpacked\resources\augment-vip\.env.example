# Environment Configuration Example
# Copy this file to .env and modify the values as needed

# Application Settings
APP_NAME=Augment VIP API
VERSION=2.0.0
ENVIRONMENT=development
DEBUG=true

# Server Configuration
HOST=127.0.0.1
PORT=8000
WORKERS=1

# Security
SECRET_KEY=your-secret-key-here-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
PASSWORD_MIN_LENGTH=8

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000", "http://localhost:5173", "http://127.0.0.1:3000"]
ALLOWED_HOSTS=["localhost", "127.0.0.1"]

# Database Configuration
DATABASE_URL=sqlite+aiosqlite:///./augment_vip.db
DATABASE_ECHO=false
DATABASE_POOL_SIZE=5
DATABASE_MAX_OVERFLOW=10

# Redis Configuration (optional)
REDIS_URL=redis://localhost:6379/0
REDIS_EXPIRE_SECONDS=3600

# Celery Configuration (optional)
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=./logs/augment_vip.log

# Monitoring & Observability
SENTRY_DSN=
SENTRY_TRACES_SAMPLE_RATE=0.1
METRICS_ENABLED=true

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# File Upload Configuration
MAX_UPLOAD_SIZE=10485760
UPLOAD_DIR=./uploads

# WebSocket Configuration
WEBSOCKET_ENABLED=true
WEBSOCKET_HEARTBEAT_INTERVAL=30

# Background Tasks
BACKGROUND_TASKS_ENABLED=true
TASK_QUEUE_MAX_SIZE=1000
