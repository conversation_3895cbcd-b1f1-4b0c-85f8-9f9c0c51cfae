import{r as wt,g as Le,a as xt}from"./vendor-DJG_os-6.js";function kt(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const a=Object.getOwnPropertyDescriptor(r,o);a&&Object.defineProperty(e,o,a.get?a:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var ne={exports:{}},W={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ge;function St(){if(ge)return W;ge=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.fragment");function n(r,o,a){var c=null;if(a!==void 0&&(c=""+a),o.key!==void 0&&(c=""+o.key),"key"in o){a={};for(var i in o)i!=="key"&&(a[i]=o[i])}else a=o;return o=a.ref,{$$typeof:e,type:r,key:c,ref:o!==void 0?o:null,props:a}}return W.Fragment=t,W.jsx=n,W.jsxs=n,W}var Ee;function Nt(){return Ee||(Ee=1,ne.exports=St()),ne.exports}var y=Nt(),s=wt();const Rt=Le(s),Fe=kt({__proto__:null,default:Rt},[s]);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mt=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),_t=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,n,r)=>r?r.toUpperCase():n.toLowerCase()),be=e=>{const t=_t(e);return t.charAt(0).toUpperCase()+t.slice(1)},je=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim(),Pt=e=>{for(const t in e)if(t.startsWith("aria-")||t==="role"||t==="title")return!0};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var At={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dt=s.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:a,iconNode:c,...i},d)=>s.createElement("svg",{ref:d,...At,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:je("lucide",o),...!a&&!Pt(i)&&{"aria-hidden":"true"},...i},[...c.map(([u,f])=>s.createElement(u,f)),...Array.isArray(a)?a:[a]]));/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const g=(e,t)=>{const n=s.forwardRef(({className:r,...o},a)=>s.createElement(Dt,{ref:a,iconNode:t,className:je(`lucide-${Mt(be(e))}`,`lucide-${e}`,r),...o}));return n.displayName=be(e),n};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ot=[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]],eo=g("activity",Ot);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tt=[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]],to=g("archive",Tt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const It=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]],no=g("bell",It);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lt=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],ro=g("calendar",Lt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ft=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],oo=g("circle-alert",Ft);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jt=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],ao=g("circle-check-big",jt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $t=[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],so=g("clock",$t);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wt=[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]],io=g("database",Wt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bt=[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]],co=g("download",Bt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vt=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],uo=g("file-text",Vt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ut=[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]],lo=g("folder-open",Ut);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ht=[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]],fo=g("folder",Ht);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zt=[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]],vo=g("hard-drive",zt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qt=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]],po=g("house",qt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gt=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]],ho=g("info",Gt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yt=[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]],mo=g("loader-circle",Yt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zt=[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]],yo=g("loader",Zt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xt=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],go=g("menu",Xt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kt=[["path",{d:"m14 10 7-7",key:"oa77jy"}],["path",{d:"M20 10h-6V4",key:"mjg0md"}],["path",{d:"m3 21 7-7",key:"tjx5ai"}],["path",{d:"M4 14h6v6",key:"rmj7iw"}]],Eo=g("minimize-2",Kt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jt=[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]],bo=g("monitor",Jt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qt=[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]],Co=g("moon",Qt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const en=[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]],wo=g("pause",en);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tn=[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]],xo=g("play",tn);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nn=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],ko=g("refresh-cw",nn);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rn=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]],So=g("rotate-ccw",rn);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const on=[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]],No=g("save",on);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const an=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Ro=g("settings",an);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sn=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]],Mo=g("shield",sn);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cn=[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]],_o=g("sun",cn);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const un=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],Po=g("trash-2",un);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ln=[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]],Ao=g("triangle-alert",ln);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dn=[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]],Do=g("upload",dn);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fn=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Oo=g("x",fn);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vn=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]],To=g("zap",vn);function D(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e?.(o),n===!1||!o.defaultPrevented)return t?.(o)}}function Ce(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function $e(...e){return t=>{let n=!1;const r=e.map(o=>{const a=Ce(o,t);return!n&&typeof a=="function"&&(n=!0),a});if(n)return()=>{for(let o=0;o<r.length;o++){const a=r[o];typeof a=="function"?a():Ce(e[o],null)}}}}function T(...e){return s.useCallback($e(...e),e)}function pn(e,t){const n=s.createContext(t),r=a=>{const{children:c,...i}=a,d=s.useMemo(()=>i,Object.values(i));return y.jsx(n.Provider,{value:d,children:c})};r.displayName=e+"Provider";function o(a){const c=s.useContext(n);if(c)return c;if(t!==void 0)return t;throw new Error(`\`${a}\` must be used within \`${e}\``)}return[r,o]}function We(e,t=[]){let n=[];function r(a,c){const i=s.createContext(c),d=n.length;n=[...n,c];const u=v=>{const{scope:p,children:m,...k}=v,l=p?.[e]?.[d]||i,h=s.useMemo(()=>k,Object.values(k));return y.jsx(l.Provider,{value:h,children:m})};u.displayName=a+"Provider";function f(v,p){const m=p?.[e]?.[d]||i,k=s.useContext(m);if(k)return k;if(c!==void 0)return c;throw new Error(`\`${v}\` must be used within \`${a}\``)}return[u,f]}const o=()=>{const a=n.map(c=>s.createContext(c));return function(i){const d=i?.[e]||a;return s.useMemo(()=>({[`__scope${e}`]:{...i,[e]:d}}),[i,d])}};return o.scopeName=e,[r,hn(o,...t)]}function hn(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(a){const c=r.reduce((i,{useScope:d,scopeName:u})=>{const v=d(a)[`__scope${u}`];return{...i,...v}},{});return s.useMemo(()=>({[`__scope${t.scopeName}`]:c}),[c])}};return n.scopeName=t.scopeName,n}var B=globalThis?.document?s.useLayoutEffect:()=>{},mn=Fe[" useId ".trim().toString()]||(()=>{}),yn=0;function re(e){const[t,n]=s.useState(mn());return B(()=>{n(r=>r??String(yn++))},[e]),e||(t?`radix-${t}`:"")}var gn=Fe[" useInsertionEffect ".trim().toString()]||B;function En({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,a,c]=bn({defaultProp:t,onChange:n}),i=e!==void 0,d=i?e:o;{const f=s.useRef(e!==void 0);s.useEffect(()=>{const v=f.current;v!==i&&console.warn(`${r} is changing from ${v?"controlled":"uncontrolled"} to ${i?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),f.current=i},[i,r])}const u=s.useCallback(f=>{if(i){const v=Cn(f)?f(e):f;v!==e&&c.current?.(v)}else a(f)},[i,e,a,c]);return[d,u]}function bn({defaultProp:e,onChange:t}){const[n,r]=s.useState(e),o=s.useRef(n),a=s.useRef(t);return gn(()=>{a.current=t},[t]),s.useEffect(()=>{o.current!==n&&(a.current?.(n),o.current=n)},[n,o]),[n,r,a]}function Cn(e){return typeof e=="function"}var Be=xt();const wn=Le(Be);function Ve(e){const t=xn(e),n=s.forwardRef((r,o)=>{const{children:a,...c}=r,i=s.Children.toArray(a),d=i.find(Sn);if(d){const u=d.props.children,f=i.map(v=>v===d?s.Children.count(u)>1?s.Children.only(null):s.isValidElement(u)?u.props.children:null:v);return y.jsx(t,{...c,ref:o,children:s.isValidElement(u)?s.cloneElement(u,void 0,f):null})}return y.jsx(t,{...c,ref:o,children:a})});return n.displayName=`${e}.Slot`,n}function xn(e){const t=s.forwardRef((n,r)=>{const{children:o,...a}=n;if(s.isValidElement(o)){const c=Rn(o),i=Nn(a,o.props);return o.type!==s.Fragment&&(i.ref=r?$e(r,c):c),s.cloneElement(o,i)}return s.Children.count(o)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var kn=Symbol("radix.slottable");function Sn(e){return s.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===kn}function Nn(e,t){const n={...t};for(const r in t){const o=e[r],a=t[r];/^on[A-Z]/.test(r)?o&&a?n[r]=(...i)=>{const d=a(...i);return o(...i),d}:o&&(n[r]=o):r==="style"?n[r]={...o,...a}:r==="className"&&(n[r]=[o,a].filter(Boolean).join(" "))}return{...e,...n}}function Rn(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Mn=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],N=Mn.reduce((e,t)=>{const n=Ve(`Primitive.${t}`),r=s.forwardRef((o,a)=>{const{asChild:c,...i}=o,d=c?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),y.jsx(d,{...i,ref:a})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function _n(e,t){e&&Be.flushSync(()=>e.dispatchEvent(t))}function V(e){const t=s.useRef(e);return s.useEffect(()=>{t.current=e}),s.useMemo(()=>(...n)=>t.current?.(...n),[])}function Pn(e,t=globalThis?.document){const n=V(e);s.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var An="DismissableLayer",de="dismissableLayer.update",Dn="dismissableLayer.pointerDownOutside",On="dismissableLayer.focusOutside",we,Ue=s.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),He=s.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:a,onInteractOutside:c,onDismiss:i,...d}=e,u=s.useContext(Ue),[f,v]=s.useState(null),p=f?.ownerDocument??globalThis?.document,[,m]=s.useState({}),k=T(t,b=>v(b)),l=Array.from(u.layers),[h]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),E=l.indexOf(h),C=f?l.indexOf(f):-1,w=u.layersWithOutsidePointerEventsDisabled.size>0,x=C>=E,S=Ln(b=>{const P=b.target,$=[...u.branches].some(te=>te.contains(P));!x||$||(o?.(b),c?.(b),b.defaultPrevented||i?.())},p),_=Fn(b=>{const P=b.target;[...u.branches].some(te=>te.contains(P))||(a?.(b),c?.(b),b.defaultPrevented||i?.())},p);return Pn(b=>{C===u.layers.size-1&&(r?.(b),!b.defaultPrevented&&i&&(b.preventDefault(),i()))},p),s.useEffect(()=>{if(f)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(we=p.body.style.pointerEvents,p.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(f)),u.layers.add(f),xe(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(p.body.style.pointerEvents=we)}},[f,p,n,u]),s.useEffect(()=>()=>{f&&(u.layers.delete(f),u.layersWithOutsidePointerEventsDisabled.delete(f),xe())},[f,u]),s.useEffect(()=>{const b=()=>m({});return document.addEventListener(de,b),()=>document.removeEventListener(de,b)},[]),y.jsx(N.div,{...d,ref:k,style:{pointerEvents:w?x?"auto":"none":void 0,...e.style},onFocusCapture:D(e.onFocusCapture,_.onFocusCapture),onBlurCapture:D(e.onBlurCapture,_.onBlurCapture),onPointerDownCapture:D(e.onPointerDownCapture,S.onPointerDownCapture)})});He.displayName=An;var Tn="DismissableLayerBranch",In=s.forwardRef((e,t)=>{const n=s.useContext(Ue),r=s.useRef(null),o=T(t,r);return s.useEffect(()=>{const a=r.current;if(a)return n.branches.add(a),()=>{n.branches.delete(a)}},[n.branches]),y.jsx(N.div,{...e,ref:o})});In.displayName=Tn;function Ln(e,t=globalThis?.document){const n=V(e),r=s.useRef(!1),o=s.useRef(()=>{});return s.useEffect(()=>{const a=i=>{if(i.target&&!r.current){let d=function(){ze(Dn,n,u,{discrete:!0})};const u={originalEvent:i};i.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=d,t.addEventListener("click",o.current,{once:!0})):d()}else t.removeEventListener("click",o.current);r.current=!1},c=window.setTimeout(()=>{t.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(c),t.removeEventListener("pointerdown",a),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Fn(e,t=globalThis?.document){const n=V(e),r=s.useRef(!1);return s.useEffect(()=>{const o=a=>{a.target&&!r.current&&ze(On,n,{originalEvent:a},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function xe(){const e=new CustomEvent(de);document.dispatchEvent(e)}function ze(e,t,n,{discrete:r}){const o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?_n(o,a):o.dispatchEvent(a)}var oe="focusScope.autoFocusOnMount",ae="focusScope.autoFocusOnUnmount",ke={bubbles:!1,cancelable:!0},jn="FocusScope",qe=s.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...c}=e,[i,d]=s.useState(null),u=V(o),f=V(a),v=s.useRef(null),p=T(t,l=>d(l)),m=s.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;s.useEffect(()=>{if(r){let l=function(w){if(m.paused||!i)return;const x=w.target;i.contains(x)?v.current=x:A(v.current,{select:!0})},h=function(w){if(m.paused||!i)return;const x=w.relatedTarget;x!==null&&(i.contains(x)||A(v.current,{select:!0}))},E=function(w){if(document.activeElement===document.body)for(const S of w)S.removedNodes.length>0&&A(i)};document.addEventListener("focusin",l),document.addEventListener("focusout",h);const C=new MutationObserver(E);return i&&C.observe(i,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",l),document.removeEventListener("focusout",h),C.disconnect()}}},[r,i,m.paused]),s.useEffect(()=>{if(i){Ne.add(m);const l=document.activeElement;if(!i.contains(l)){const E=new CustomEvent(oe,ke);i.addEventListener(oe,u),i.dispatchEvent(E),E.defaultPrevented||($n(Hn(Ge(i)),{select:!0}),document.activeElement===l&&A(i))}return()=>{i.removeEventListener(oe,u),setTimeout(()=>{const E=new CustomEvent(ae,ke);i.addEventListener(ae,f),i.dispatchEvent(E),E.defaultPrevented||A(l??document.body,{select:!0}),i.removeEventListener(ae,f),Ne.remove(m)},0)}}},[i,u,f,m]);const k=s.useCallback(l=>{if(!n&&!r||m.paused)return;const h=l.key==="Tab"&&!l.altKey&&!l.ctrlKey&&!l.metaKey,E=document.activeElement;if(h&&E){const C=l.currentTarget,[w,x]=Wn(C);w&&x?!l.shiftKey&&E===x?(l.preventDefault(),n&&A(w,{select:!0})):l.shiftKey&&E===w&&(l.preventDefault(),n&&A(x,{select:!0})):E===C&&l.preventDefault()}},[n,r,m.paused]);return y.jsx(N.div,{tabIndex:-1,...c,ref:p,onKeyDown:k})});qe.displayName=jn;function $n(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(A(r,{select:t}),document.activeElement!==n)return}function Wn(e){const t=Ge(e),n=Se(t,e),r=Se(t.reverse(),e);return[n,r]}function Ge(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Se(e,t){for(const n of e)if(!Bn(n,{upTo:t}))return n}function Bn(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Vn(e){return e instanceof HTMLInputElement&&"select"in e}function A(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&Vn(e)&&t&&e.select()}}var Ne=Un();function Un(){let e=[];return{add(t){const n=e[0];t!==n&&n?.pause(),e=Re(e,t),e.unshift(t)},remove(t){e=Re(e,t),e[0]?.resume()}}}function Re(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function Hn(e){return e.filter(t=>t.tagName!=="A")}var zn="Portal",Ye=s.forwardRef((e,t)=>{const{container:n,...r}=e,[o,a]=s.useState(!1);B(()=>a(!0),[]);const c=n||o&&globalThis?.document?.body;return c?wn.createPortal(y.jsx(N.div,{...r,ref:t}),c):null});Ye.displayName=zn;function qn(e,t){return s.useReducer((n,r)=>t[n][r]??n,e)}var J=e=>{const{present:t,children:n}=e,r=Gn(t),o=typeof n=="function"?n({present:r.isPresent}):s.Children.only(n),a=T(r.ref,Yn(o));return typeof n=="function"||r.isPresent?s.cloneElement(o,{ref:a}):null};J.displayName="Presence";function Gn(e){const[t,n]=s.useState(),r=s.useRef(null),o=s.useRef(e),a=s.useRef("none"),c=e?"mounted":"unmounted",[i,d]=qn(c,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return s.useEffect(()=>{const u=U(r.current);a.current=i==="mounted"?u:"none"},[i]),B(()=>{const u=r.current,f=o.current;if(f!==e){const p=a.current,m=U(u);e?d("MOUNT"):m==="none"||u?.display==="none"?d("UNMOUNT"):d(f&&p!==m?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,d]),B(()=>{if(t){let u;const f=t.ownerDocument.defaultView??window,v=m=>{const l=U(r.current).includes(m.animationName);if(m.target===t&&l&&(d("ANIMATION_END"),!o.current)){const h=t.style.animationFillMode;t.style.animationFillMode="forwards",u=f.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=h)})}},p=m=>{m.target===t&&(a.current=U(r.current))};return t.addEventListener("animationstart",p),t.addEventListener("animationcancel",v),t.addEventListener("animationend",v),()=>{f.clearTimeout(u),t.removeEventListener("animationstart",p),t.removeEventListener("animationcancel",v),t.removeEventListener("animationend",v)}}else d("ANIMATION_END")},[t,d]),{isPresent:["mounted","unmountSuspended"].includes(i),ref:s.useCallback(u=>{r.current=u?getComputedStyle(u):null,n(u)},[])}}function U(e){return e?.animationName||"none"}function Yn(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var se=0;function Zn(){s.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Me()),document.body.insertAdjacentElement("beforeend",e[1]??Me()),se++,()=>{se===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),se--}},[])}function Me(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var M=function(){return M=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(t[a]=n[a])}return t},M.apply(this,arguments)};function Ze(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function Xn(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,a;r<o;r++)(a||!(r in t))&&(a||(a=Array.prototype.slice.call(t,0,r)),a[r]=t[r]);return e.concat(a||Array.prototype.slice.call(t))}var Y="right-scroll-bar-position",Z="width-before-scroll-bar",Kn="with-scroll-bars-hidden",Jn="--removed-body-scroll-bar-size";function ie(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function Qn(e,t){var n=s.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var er=typeof window<"u"?s.useLayoutEffect:s.useEffect,_e=new WeakMap;function tr(e,t){var n=Qn(null,function(r){return e.forEach(function(o){return ie(o,r)})});return er(function(){var r=_e.get(n);if(r){var o=new Set(r),a=new Set(e),c=n.current;o.forEach(function(i){a.has(i)||ie(i,null)}),a.forEach(function(i){o.has(i)||ie(i,c)})}_e.set(n,e)},[e]),n}function nr(e){return e}function rr(e,t){t===void 0&&(t=nr);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(a){var c=t(a,r);return n.push(c),function(){n=n.filter(function(i){return i!==c})}},assignSyncMedium:function(a){for(r=!0;n.length;){var c=n;n=[],c.forEach(a)}n={push:function(i){return a(i)},filter:function(){return n}}},assignMedium:function(a){r=!0;var c=[];if(n.length){var i=n;n=[],i.forEach(a),c=n}var d=function(){var f=c;c=[],f.forEach(a)},u=function(){return Promise.resolve().then(d)};u(),n={push:function(f){c.push(f),u()},filter:function(f){return c=c.filter(f),n}}}};return o}function or(e){e===void 0&&(e={});var t=rr(null);return t.options=M({async:!0,ssr:!1},e),t}var Xe=function(e){var t=e.sideCar,n=Ze(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return s.createElement(r,M({},n))};Xe.isSideCarExport=!0;function ar(e,t){return e.useMedium(t),Xe}var Ke=or(),ce=function(){},Q=s.forwardRef(function(e,t){var n=s.useRef(null),r=s.useState({onScrollCapture:ce,onWheelCapture:ce,onTouchMoveCapture:ce}),o=r[0],a=r[1],c=e.forwardProps,i=e.children,d=e.className,u=e.removeScrollBar,f=e.enabled,v=e.shards,p=e.sideCar,m=e.noRelative,k=e.noIsolation,l=e.inert,h=e.allowPinchZoom,E=e.as,C=E===void 0?"div":E,w=e.gapMode,x=Ze(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),S=p,_=tr([n,t]),b=M(M({},x),o);return s.createElement(s.Fragment,null,f&&s.createElement(S,{sideCar:Ke,removeScrollBar:u,shards:v,noRelative:m,noIsolation:k,inert:l,setCallbacks:a,allowPinchZoom:!!h,lockRef:n,gapMode:w}),c?s.cloneElement(s.Children.only(i),M(M({},b),{ref:_})):s.createElement(C,M({},b,{className:d,ref:_}),i))});Q.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Q.classNames={fullWidth:Z,zeroRight:Y};var sr=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function ir(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=sr();return t&&e.setAttribute("nonce",t),e}function cr(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function ur(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var lr=function(){var e=0,t=null;return{add:function(n){e==0&&(t=ir())&&(cr(t,n),ur(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},dr=function(){var e=lr();return function(t,n){s.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Je=function(){var e=dr(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},fr={left:0,top:0,right:0,gap:0},ue=function(e){return parseInt(e||"",10)||0},vr=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[ue(n),ue(r),ue(o)]},pr=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return fr;var t=vr(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},hr=Je(),j="data-scroll-locked",mr=function(e,t,n,r){var o=e.left,a=e.top,c=e.right,i=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(Kn,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(i,"px ").concat(r,`;
  }
  body[`).concat(j,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(a,`px;
    padding-right: `).concat(c,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(i,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(i,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Y,` {
    right: `).concat(i,"px ").concat(r,`;
  }
  
  .`).concat(Z,` {
    margin-right: `).concat(i,"px ").concat(r,`;
  }
  
  .`).concat(Y," .").concat(Y,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(Z," .").concat(Z,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(j,`] {
    `).concat(Jn,": ").concat(i,`px;
  }
`)},Pe=function(){var e=parseInt(document.body.getAttribute(j)||"0",10);return isFinite(e)?e:0},yr=function(){s.useEffect(function(){return document.body.setAttribute(j,(Pe()+1).toString()),function(){var e=Pe()-1;e<=0?document.body.removeAttribute(j):document.body.setAttribute(j,e.toString())}},[])},gr=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;yr();var a=s.useMemo(function(){return pr(o)},[o]);return s.createElement(hr,{styles:mr(a,!t,o,n?"":"!important")})},fe=!1;if(typeof window<"u")try{var H=Object.defineProperty({},"passive",{get:function(){return fe=!0,!0}});window.addEventListener("test",H,H),window.removeEventListener("test",H,H)}catch{fe=!1}var I=fe?{passive:!1}:!1,Er=function(e){return e.tagName==="TEXTAREA"},Qe=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Er(e)&&n[t]==="visible")},br=function(e){return Qe(e,"overflowY")},Cr=function(e){return Qe(e,"overflowX")},Ae=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=et(e,r);if(o){var a=tt(e,r),c=a[1],i=a[2];if(c>i)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},wr=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},xr=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},et=function(e,t){return e==="v"?br(t):Cr(t)},tt=function(e,t){return e==="v"?wr(t):xr(t)},kr=function(e,t){return e==="h"&&t==="rtl"?-1:1},Sr=function(e,t,n,r,o){var a=kr(e,window.getComputedStyle(t).direction),c=a*r,i=n.target,d=t.contains(i),u=!1,f=c>0,v=0,p=0;do{if(!i)break;var m=tt(e,i),k=m[0],l=m[1],h=m[2],E=l-h-a*k;(k||E)&&et(e,i)&&(v+=E,p+=k);var C=i.parentNode;i=C&&C.nodeType===Node.DOCUMENT_FRAGMENT_NODE?C.host:C}while(!d&&i!==document.body||d&&(t.contains(i)||t===i));return(f&&Math.abs(v)<1||!f&&Math.abs(p)<1)&&(u=!0),u},z=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},De=function(e){return[e.deltaX,e.deltaY]},Oe=function(e){return e&&"current"in e?e.current:e},Nr=function(e,t){return e[0]===t[0]&&e[1]===t[1]},Rr=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},Mr=0,L=[];function _r(e){var t=s.useRef([]),n=s.useRef([0,0]),r=s.useRef(),o=s.useState(Mr++)[0],a=s.useState(Je)[0],c=s.useRef(e);s.useEffect(function(){c.current=e},[e]),s.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var l=Xn([e.lockRef.current],(e.shards||[]).map(Oe),!0).filter(Boolean);return l.forEach(function(h){return h.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),l.forEach(function(h){return h.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var i=s.useCallback(function(l,h){if("touches"in l&&l.touches.length===2||l.type==="wheel"&&l.ctrlKey)return!c.current.allowPinchZoom;var E=z(l),C=n.current,w="deltaX"in l?l.deltaX:C[0]-E[0],x="deltaY"in l?l.deltaY:C[1]-E[1],S,_=l.target,b=Math.abs(w)>Math.abs(x)?"h":"v";if("touches"in l&&b==="h"&&_.type==="range")return!1;var P=Ae(b,_);if(!P)return!0;if(P?S=b:(S=b==="v"?"h":"v",P=Ae(b,_)),!P)return!1;if(!r.current&&"changedTouches"in l&&(w||x)&&(r.current=S),!S)return!0;var $=r.current||S;return Sr($,h,l,$==="h"?w:x)},[]),d=s.useCallback(function(l){var h=l;if(!(!L.length||L[L.length-1]!==a)){var E="deltaY"in h?De(h):z(h),C=t.current.filter(function(S){return S.name===h.type&&(S.target===h.target||h.target===S.shadowParent)&&Nr(S.delta,E)})[0];if(C&&C.should){h.cancelable&&h.preventDefault();return}if(!C){var w=(c.current.shards||[]).map(Oe).filter(Boolean).filter(function(S){return S.contains(h.target)}),x=w.length>0?i(h,w[0]):!c.current.noIsolation;x&&h.cancelable&&h.preventDefault()}}},[]),u=s.useCallback(function(l,h,E,C){var w={name:l,delta:h,target:E,should:C,shadowParent:Pr(E)};t.current.push(w),setTimeout(function(){t.current=t.current.filter(function(x){return x!==w})},1)},[]),f=s.useCallback(function(l){n.current=z(l),r.current=void 0},[]),v=s.useCallback(function(l){u(l.type,De(l),l.target,i(l,e.lockRef.current))},[]),p=s.useCallback(function(l){u(l.type,z(l),l.target,i(l,e.lockRef.current))},[]);s.useEffect(function(){return L.push(a),e.setCallbacks({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:p}),document.addEventListener("wheel",d,I),document.addEventListener("touchmove",d,I),document.addEventListener("touchstart",f,I),function(){L=L.filter(function(l){return l!==a}),document.removeEventListener("wheel",d,I),document.removeEventListener("touchmove",d,I),document.removeEventListener("touchstart",f,I)}},[]);var m=e.removeScrollBar,k=e.inert;return s.createElement(s.Fragment,null,k?s.createElement(a,{styles:Rr(o)}):null,m?s.createElement(gr,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function Pr(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const Ar=ar(Ke,_r);var nt=s.forwardRef(function(e,t){return s.createElement(Q,M({},e,{ref:t,sideCar:Ar}))});nt.classNames=Q.classNames;var Dr=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},F=new WeakMap,q=new WeakMap,G={},le=0,rt=function(e){return e&&(e.host||rt(e.parentNode))},Or=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=rt(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},Tr=function(e,t,n,r){var o=Or(t,Array.isArray(e)?e:[e]);G[n]||(G[n]=new WeakMap);var a=G[n],c=[],i=new Set,d=new Set(o),u=function(v){!v||i.has(v)||(i.add(v),u(v.parentNode))};o.forEach(u);var f=function(v){!v||d.has(v)||Array.prototype.forEach.call(v.children,function(p){if(i.has(p))f(p);else try{var m=p.getAttribute(r),k=m!==null&&m!=="false",l=(F.get(p)||0)+1,h=(a.get(p)||0)+1;F.set(p,l),a.set(p,h),c.push(p),l===1&&k&&q.set(p,!0),h===1&&p.setAttribute(n,"true"),k||p.setAttribute(r,"true")}catch(E){console.error("aria-hidden: cannot operate on ",p,E)}})};return f(t),i.clear(),le++,function(){c.forEach(function(v){var p=F.get(v)-1,m=a.get(v)-1;F.set(v,p),a.set(v,m),p||(q.has(v)||v.removeAttribute(r),q.delete(v)),m||v.removeAttribute(n)}),le--,le||(F=new WeakMap,F=new WeakMap,q=new WeakMap,G={})}},Ir=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=Dr(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),Tr(r,o,n,"aria-hidden")):function(){return null}},ee="Dialog",[ot,Io]=We(ee),[Lr,R]=ot(ee),at=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:a,modal:c=!0}=e,i=s.useRef(null),d=s.useRef(null),[u,f]=En({prop:r,defaultProp:o??!1,onChange:a,caller:ee});return y.jsx(Lr,{scope:t,triggerRef:i,contentRef:d,contentId:re(),titleId:re(),descriptionId:re(),open:u,onOpenChange:f,onOpenToggle:s.useCallback(()=>f(v=>!v),[f]),modal:c,children:n})};at.displayName=ee;var st="DialogTrigger",Fr=s.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=R(st,n),a=T(t,o.triggerRef);return y.jsx(N.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":he(o.open),...r,ref:a,onClick:D(e.onClick,o.onOpenToggle)})});Fr.displayName=st;var ve="DialogPortal",[jr,it]=ot(ve,{forceMount:void 0}),ct=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,a=R(ve,t);return y.jsx(jr,{scope:t,forceMount:n,children:s.Children.map(r,c=>y.jsx(J,{present:n||a.open,children:y.jsx(Ye,{asChild:!0,container:o,children:c})}))})};ct.displayName=ve;var X="DialogOverlay",ut=s.forwardRef((e,t)=>{const n=it(X,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=R(X,e.__scopeDialog);return a.modal?y.jsx(J,{present:r||a.open,children:y.jsx(Wr,{...o,ref:t})}):null});ut.displayName=X;var $r=Ve("DialogOverlay.RemoveScroll"),Wr=s.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=R(X,n);return y.jsx(nt,{as:$r,allowPinchZoom:!0,shards:[o.contentRef],children:y.jsx(N.div,{"data-state":he(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),O="DialogContent",lt=s.forwardRef((e,t)=>{const n=it(O,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=R(O,e.__scopeDialog);return y.jsx(J,{present:r||a.open,children:a.modal?y.jsx(Br,{...o,ref:t}):y.jsx(Vr,{...o,ref:t})})});lt.displayName=O;var Br=s.forwardRef((e,t)=>{const n=R(O,e.__scopeDialog),r=s.useRef(null),o=T(t,n.contentRef,r);return s.useEffect(()=>{const a=r.current;if(a)return Ir(a)},[]),y.jsx(dt,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:D(e.onCloseAutoFocus,a=>{a.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:D(e.onPointerDownOutside,a=>{const c=a.detail.originalEvent,i=c.button===0&&c.ctrlKey===!0;(c.button===2||i)&&a.preventDefault()}),onFocusOutside:D(e.onFocusOutside,a=>a.preventDefault())})}),Vr=s.forwardRef((e,t)=>{const n=R(O,e.__scopeDialog),r=s.useRef(!1),o=s.useRef(!1);return y.jsx(dt,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{e.onCloseAutoFocus?.(a),a.defaultPrevented||(r.current||n.triggerRef.current?.focus(),a.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:a=>{e.onInteractOutside?.(a),a.defaultPrevented||(r.current=!0,a.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const c=a.target;n.triggerRef.current?.contains(c)&&a.preventDefault(),a.detail.originalEvent.type==="focusin"&&o.current&&a.preventDefault()}})}),dt=s.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:a,...c}=e,i=R(O,n),d=s.useRef(null),u=T(t,d);return Zn(),y.jsxs(y.Fragment,{children:[y.jsx(qe,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:a,children:y.jsx(He,{role:"dialog",id:i.contentId,"aria-describedby":i.descriptionId,"aria-labelledby":i.titleId,"data-state":he(i.open),...c,ref:u,onDismiss:()=>i.onOpenChange(!1)})}),y.jsxs(y.Fragment,{children:[y.jsx(Hr,{titleId:i.titleId}),y.jsx(qr,{contentRef:d,descriptionId:i.descriptionId})]})]})}),pe="DialogTitle",ft=s.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=R(pe,n);return y.jsx(N.h2,{id:o.titleId,...r,ref:t})});ft.displayName=pe;var vt="DialogDescription",Ur=s.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=R(vt,n);return y.jsx(N.p,{id:o.descriptionId,...r,ref:t})});Ur.displayName=vt;var pt="DialogClose",ht=s.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=R(pt,n);return y.jsx(N.button,{type:"button",...r,ref:t,onClick:D(e.onClick,()=>o.onOpenChange(!1))})});ht.displayName=pt;function he(e){return e?"open":"closed"}var mt="DialogTitleWarning",[Lo,yt]=pn(mt,{contentName:O,titleName:pe,docsSlug:"dialog"}),Hr=({titleId:e})=>{const t=yt(mt),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return s.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},zr="DialogDescriptionWarning",qr=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${yt(zr).contentName}}.`;return s.useEffect(()=>{const o=e.current?.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},Fo=at,jo=ct,$o=ut,Wo=lt,Bo=ft,Vo=ht,me="Progress",ye=100,[Gr,Uo]=We(me),[Yr,Zr]=Gr(me),gt=s.forwardRef((e,t)=>{const{__scopeProgress:n,value:r=null,max:o,getValueLabel:a=Xr,...c}=e;(o||o===0)&&!Te(o)&&console.error(Kr(`${o}`,"Progress"));const i=Te(o)?o:ye;r!==null&&!Ie(r,i)&&console.error(Jr(`${r}`,"Progress"));const d=Ie(r,i)?r:null,u=K(d)?a(d,i):void 0;return y.jsx(Yr,{scope:n,value:d,max:i,children:y.jsx(N.div,{"aria-valuemax":i,"aria-valuemin":0,"aria-valuenow":K(d)?d:void 0,"aria-valuetext":u,role:"progressbar","data-state":Ct(d,i),"data-value":d??void 0,"data-max":i,...c,ref:t})})});gt.displayName=me;var Et="ProgressIndicator",bt=s.forwardRef((e,t)=>{const{__scopeProgress:n,...r}=e,o=Zr(Et,n);return y.jsx(N.div,{"data-state":Ct(o.value,o.max),"data-value":o.value??void 0,"data-max":o.max,...r,ref:t})});bt.displayName=Et;function Xr(e,t){return`${Math.round(e/t*100)}%`}function Ct(e,t){return e==null?"indeterminate":e===t?"complete":"loading"}function K(e){return typeof e=="number"}function Te(e){return K(e)&&!isNaN(e)&&e>0}function Ie(e,t){return K(e)&&!isNaN(e)&&e<=t&&e>=0}function Kr(e,t){return`Invalid prop \`max\` of value \`${e}\` supplied to \`${t}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${ye}\`.`}function Jr(e,t){return`Invalid prop \`value\` of value \`${e}\` supplied to \`${t}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${ye} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var Ho=gt,zo=bt;export{to as A,no as B,Wo as C,io as D,fo as E,uo as F,co as G,po as H,zo as I,mo as L,go as M,$o as O,jo as P,Rt as R,Ro as S,Bo as T,Do as U,Oo as X,To as Z,Co as a,_o as b,Eo as c,Fo as d,Vo as e,Ho as f,ao as g,oo as h,yo as i,y as j,vo as k,Po as l,Ao as m,eo as n,so as o,ho as p,Mo as q,s as r,xo as s,No as t,ro as u,So as v,wo as w,lo as x,ko as y,bo as z};
//# sourceMappingURL=ui-ZHYupI7r.js.map
