"""
Enterprise Reporting and Analytics Engine
Generates detailed reports, statistics, and operation analytics
"""

import os
import json
import platform
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import statistics

from .utils import info, success, error, warning

@dataclass
class OperationMetrics:
    """Metrics for a single operation"""
    operation_id: str
    operation_type: str
    start_time: datetime
    end_time: datetime
    duration: float
    success: bool
    entries_processed: int = 0
    data_size_processed: int = 0
    error_message: Optional[str] = None

@dataclass
class SystemMetrics:
    """System-level metrics"""
    timestamp: datetime
    platform: str
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    running_processes: int
    target_processes: int

class ReportingEngine:
    """Enterprise reporting and analytics engine"""
    
    def __init__(self, data_directory: Optional[Path] = None):
        if data_directory is None:
            data_directory = Path.home() / '.augment-vip' / 'reports'
        
        self.data_directory = data_directory
        self.data_directory.mkdir(parents=True, exist_ok=True)
        
        self.metrics_file = self.data_directory / 'metrics.json'
        self.operations_file = self.data_directory / 'operations.json'
        self.system_metrics_file = self.data_directory / 'system_metrics.json'
        
        # Initialize files if they don't exist
        for file_path in [self.metrics_file, self.operations_file, self.system_metrics_file]:
            if not file_path.exists():
                with open(file_path, 'w') as f:
                    json.dump([], f)
    
    def record_operation(self, operation_metrics: OperationMetrics):
        """Record metrics for a completed operation"""
        try:
            # Load existing operations
            with open(self.operations_file, 'r') as f:
                operations = json.load(f)
            
            # Add new operation
            operation_dict = asdict(operation_metrics)
            operation_dict['start_time'] = operation_metrics.start_time.isoformat()
            operation_dict['end_time'] = operation_metrics.end_time.isoformat()
            
            operations.append(operation_dict)
            
            # Keep only last 1000 operations
            if len(operations) > 1000:
                operations = operations[-1000:]
            
            # Save back to file
            with open(self.operations_file, 'w') as f:
                json.dump(operations, f, indent=2)
            
            info(f"Recorded operation metrics: {operation_metrics.operation_type}")
            
        except Exception as e:
            warning(f"Failed to record operation metrics: {e}")
    
    def record_system_metrics(self, system_metrics: SystemMetrics):
        """Record system-level metrics"""
        try:
            # Load existing metrics
            with open(self.system_metrics_file, 'r') as f:
                metrics = json.load(f)
            
            # Add new metrics
            metrics_dict = asdict(system_metrics)
            metrics_dict['timestamp'] = system_metrics.timestamp.isoformat()
            
            metrics.append(metrics_dict)
            
            # Keep only last 10000 entries (about 1 week of hourly data)
            if len(metrics) > 10000:
                metrics = metrics[-10000:]
            
            # Save back to file
            with open(self.system_metrics_file, 'w') as f:
                json.dump(metrics, f, indent=2)
            
        except Exception as e:
            warning(f"Failed to record system metrics: {e}")
    
    def generate_operation_report(self, 
                                 start_date: Optional[datetime] = None,
                                 end_date: Optional[datetime] = None,
                                 operation_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Generate comprehensive operation report
        
        Args:
            start_date: Start date for report (default: 30 days ago)
            end_date: End date for report (default: now)
            operation_types: Filter by operation types
        
        Returns:
            Detailed operation report
        """
        if end_date is None:
            end_date = datetime.now()
        if start_date is None:
            start_date = end_date - timedelta(days=30)
        
        try:
            # Load operations data
            with open(self.operations_file, 'r') as f:
                all_operations = json.load(f)
            
            # Filter operations by date and type
            filtered_operations = []
            for op in all_operations:
                op_time = datetime.fromisoformat(op['start_time'])
                
                if start_date <= op_time <= end_date:
                    if operation_types is None or op['operation_type'] in operation_types:
                        filtered_operations.append(op)
            
            # Generate report
            report = {
                'report_metadata': {
                    'generated_at': datetime.now().isoformat(),
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'total_operations': len(filtered_operations),
                    'platform': platform.system()
                },
                'summary_statistics': self._calculate_summary_statistics(filtered_operations),
                'operation_breakdown': self._calculate_operation_breakdown(filtered_operations),
                'performance_metrics': self._calculate_performance_metrics(filtered_operations),
                'error_analysis': self._analyze_errors(filtered_operations),
                'trends': self._analyze_trends(filtered_operations),
                'recommendations': self._generate_recommendations(filtered_operations)
            }
            
            return report
            
        except Exception as e:
            error(f"Failed to generate operation report: {e}")
            return {'error': str(e)}
    
    def generate_system_report(self, 
                              start_date: Optional[datetime] = None,
                              end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """
        Generate system performance report
        
        Args:
            start_date: Start date for report
            end_date: End date for report
        
        Returns:
            System performance report
        """
        if end_date is None:
            end_date = datetime.now()
        if start_date is None:
            start_date = end_date - timedelta(days=7)
        
        try:
            # Load system metrics
            with open(self.system_metrics_file, 'r') as f:
                all_metrics = json.load(f)
            
            # Filter by date
            filtered_metrics = []
            for metric in all_metrics:
                metric_time = datetime.fromisoformat(metric['timestamp'])
                if start_date <= metric_time <= end_date:
                    filtered_metrics.append(metric)
            
            if not filtered_metrics:
                return {
                    'error': 'No system metrics found for the specified date range',
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                }
            
            # Generate system report
            report = {
                'report_metadata': {
                    'generated_at': datetime.now().isoformat(),
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'total_data_points': len(filtered_metrics),
                    'platform': platform.system()
                },
                'resource_utilization': self._analyze_resource_utilization(filtered_metrics),
                'performance_trends': self._analyze_system_trends(filtered_metrics),
                'capacity_analysis': self._analyze_capacity(filtered_metrics),
                'recommendations': self._generate_system_recommendations(filtered_metrics)
            }
            
            return report
            
        except Exception as e:
            error(f"Failed to generate system report: {e}")
            return {'error': str(e)}
    
    def generate_compliance_report(self) -> Dict[str, Any]:
        """Generate compliance and audit report"""
        try:
            # Load all data
            with open(self.operations_file, 'r') as f:
                operations = json.load(f)
            
            # Generate compliance report
            report = {
                'report_metadata': {
                    'generated_at': datetime.now().isoformat(),
                    'report_type': 'compliance_audit',
                    'platform': platform.system(),
                    'total_operations_audited': len(operations)
                },
                'data_handling': self._audit_data_handling(operations),
                'security_compliance': self._audit_security_compliance(),
                'backup_compliance': self._audit_backup_compliance(operations),
                'operation_integrity': self._audit_operation_integrity(operations),
                'recommendations': self._generate_compliance_recommendations(operations)
            }
            
            return report
            
        except Exception as e:
            error(f"Failed to generate compliance report: {e}")
            return {'error': str(e)}
    
    def export_report(self, report: Dict[str, Any], format: str = 'json', filename: Optional[str] = None) -> str:
        """
        Export report to file
        
        Args:
            report: Report data to export
            format: Export format ('json', 'csv', 'html')
            filename: Optional custom filename
        
        Returns:
            Path to exported file
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"augment_vip_report_{timestamp}.{format}"
        
        export_path = self.data_directory / filename
        
        try:
            if format == 'json':
                with open(export_path, 'w') as f:
                    json.dump(report, f, indent=2)
            elif format == 'html':
                html_content = self._generate_html_report(report)
                with open(export_path, 'w') as f:
                    f.write(html_content)
            elif format == 'csv':
                csv_content = self._generate_csv_report(report)
                with open(export_path, 'w') as f:
                    f.write(csv_content)
            else:
                raise ValueError(f"Unsupported export format: {format}")
            
            success(f"Report exported to: {export_path}")
            return str(export_path)
            
        except Exception as e:
            error(f"Failed to export report: {e}")
            raise
    
    def _calculate_summary_statistics(self, operations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate summary statistics for operations"""
        if not operations:
            return {}
        
        successful_ops = [op for op in operations if op['success']]
        failed_ops = [op for op in operations if not op['success']]
        
        durations = [op['duration'] for op in operations if op['duration'] > 0]
        entries_processed = [op['entries_processed'] for op in operations if op['entries_processed'] > 0]
        
        return {
            'total_operations': len(operations),
            'successful_operations': len(successful_ops),
            'failed_operations': len(failed_ops),
            'success_rate': (len(successful_ops) / len(operations)) * 100 if operations else 0,
            'average_duration': statistics.mean(durations) if durations else 0,
            'median_duration': statistics.median(durations) if durations else 0,
            'total_entries_processed': sum(entries_processed),
            'average_entries_per_operation': statistics.mean(entries_processed) if entries_processed else 0
        }
    
    def _calculate_operation_breakdown(self, operations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Break down operations by type"""
        breakdown = {}
        
        for op in operations:
            op_type = op['operation_type']
            if op_type not in breakdown:
                breakdown[op_type] = {
                    'count': 0,
                    'successful': 0,
                    'failed': 0,
                    'total_duration': 0,
                    'total_entries': 0
                }
            
            breakdown[op_type]['count'] += 1
            if op['success']:
                breakdown[op_type]['successful'] += 1
            else:
                breakdown[op_type]['failed'] += 1
            
            breakdown[op_type]['total_duration'] += op['duration']
            breakdown[op_type]['total_entries'] += op['entries_processed']
        
        # Calculate averages
        for op_type in breakdown:
            data = breakdown[op_type]
            data['success_rate'] = (data['successful'] / data['count']) * 100 if data['count'] > 0 else 0
            data['average_duration'] = data['total_duration'] / data['count'] if data['count'] > 0 else 0
            data['average_entries'] = data['total_entries'] / data['count'] if data['count'] > 0 else 0
        
        return breakdown
    
    def _calculate_performance_metrics(self, operations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate performance metrics"""
        if not operations:
            return {}
        
        durations = [op['duration'] for op in operations if op['duration'] > 0]
        
        if not durations:
            return {}
        
        return {
            'fastest_operation': min(durations),
            'slowest_operation': max(durations),
            'duration_std_dev': statistics.stdev(durations) if len(durations) > 1 else 0,
            'operations_per_hour': len(operations) / ((max(durations) - min(durations)) / 3600) if len(durations) > 1 else 0,
            'performance_consistency': 1 - (statistics.stdev(durations) / statistics.mean(durations)) if len(durations) > 1 else 1
        }
    
    def _analyze_errors(self, operations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze error patterns"""
        failed_ops = [op for op in operations if not op['success']]
        
        if not failed_ops:
            return {'total_errors': 0, 'error_patterns': {}}
        
        error_patterns = {}
        for op in failed_ops:
            error_msg = op.get('error_message', 'Unknown error')
            # Categorize errors
            if 'permission' in error_msg.lower():
                category = 'permission_errors'
            elif 'file' in error_msg.lower() and 'lock' in error_msg.lower():
                category = 'file_lock_errors'
            elif 'network' in error_msg.lower() or 'connection' in error_msg.lower():
                category = 'network_errors'
            elif 'disk' in error_msg.lower() or 'space' in error_msg.lower():
                category = 'disk_space_errors'
            else:
                category = 'other_errors'
            
            if category not in error_patterns:
                error_patterns[category] = 0
            error_patterns[category] += 1
        
        return {
            'total_errors': len(failed_ops),
            'error_rate': (len(failed_ops) / len(operations)) * 100,
            'error_patterns': error_patterns,
            'most_common_error': max(error_patterns.items(), key=lambda x: x[1])[0] if error_patterns else None
        }
    
    def _analyze_trends(self, operations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze operation trends over time"""
        if len(operations) < 2:
            return {}
        
        # Sort by time
        sorted_ops = sorted(operations, key=lambda x: x['start_time'])
        
        # Calculate daily statistics
        daily_stats = {}
        for op in sorted_ops:
            date = op['start_time'][:10]  # Extract date part
            if date not in daily_stats:
                daily_stats[date] = {'count': 0, 'successful': 0, 'total_duration': 0}
            
            daily_stats[date]['count'] += 1
            if op['success']:
                daily_stats[date]['successful'] += 1
            daily_stats[date]['total_duration'] += op['duration']
        
        # Calculate trends
        dates = sorted(daily_stats.keys())
        if len(dates) < 2:
            return daily_stats
        
        # Simple trend calculation (comparing first half vs second half)
        mid_point = len(dates) // 2
        first_half = dates[:mid_point]
        second_half = dates[mid_point:]
        
        first_half_avg = statistics.mean([daily_stats[date]['count'] for date in first_half])
        second_half_avg = statistics.mean([daily_stats[date]['count'] for date in second_half])
        
        trend = 'increasing' if second_half_avg > first_half_avg else 'decreasing' if second_half_avg < first_half_avg else 'stable'
        
        return {
            'daily_statistics': daily_stats,
            'trend_direction': trend,
            'trend_magnitude': abs(second_half_avg - first_half_avg),
            'peak_activity_date': max(daily_stats.items(), key=lambda x: x[1]['count'])[0]
        }
    
    def _generate_recommendations(self, operations: List[Dict[str, Any]]) -> List[str]:
        """Generate recommendations based on operation analysis"""
        recommendations = []
        
        if not operations:
            return ["No operations data available for analysis"]
        
        # Analyze success rate
        success_rate = sum(1 for op in operations if op['success']) / len(operations) * 100
        
        if success_rate < 90:
            recommendations.append(f"Success rate is {success_rate:.1f}%. Consider investigating common failure causes.")
        
        # Analyze performance
        durations = [op['duration'] for op in operations if op['duration'] > 0]
        if durations:
            avg_duration = statistics.mean(durations)
            if avg_duration > 30:  # 30 seconds
                recommendations.append("Operations are taking longer than expected. Consider optimizing performance.")
        
        # Analyze error patterns
        failed_ops = [op for op in operations if not op['success']]
        if failed_ops:
            error_messages = [op.get('error_message', '') for op in failed_ops]
            if any('permission' in msg.lower() for msg in error_messages):
                recommendations.append("Permission errors detected. Ensure proper access rights.")
            if any('lock' in msg.lower() for msg in error_messages):
                recommendations.append("File lock errors detected. Close applications before operations.")
        
        # General recommendations
        recommendations.append("Regular monitoring of operation metrics is recommended.")
        recommendations.append("Consider scheduling operations during low-activity periods.")
        
        return recommendations

    def _analyze_resource_utilization(self, metrics: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze system resource utilization"""
        if not metrics:
            return {}

        cpu_values = [m['cpu_usage'] for m in metrics if 'cpu_usage' in m]
        memory_values = [m['memory_usage'] for m in metrics if 'memory_usage' in m]
        disk_values = [m['disk_usage'] for m in metrics if 'disk_usage' in m]

        return {
            'cpu': {
                'average': statistics.mean(cpu_values) if cpu_values else 0,
                'peak': max(cpu_values) if cpu_values else 0,
                'minimum': min(cpu_values) if cpu_values else 0
            },
            'memory': {
                'average': statistics.mean(memory_values) if memory_values else 0,
                'peak': max(memory_values) if memory_values else 0,
                'minimum': min(memory_values) if memory_values else 0
            },
            'disk': {
                'average': statistics.mean(disk_values) if disk_values else 0,
                'peak': max(disk_values) if disk_values else 0,
                'minimum': min(disk_values) if disk_values else 0
            }
        }

    def _analyze_system_trends(self, metrics: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze system performance trends"""
        if len(metrics) < 2:
            return {}

        # Sort by timestamp
        sorted_metrics = sorted(metrics, key=lambda x: x['timestamp'])

        # Calculate hourly averages
        hourly_stats = {}
        for metric in sorted_metrics:
            hour = metric['timestamp'][:13]  # Extract hour part
            if hour not in hourly_stats:
                hourly_stats[hour] = {
                    'cpu_total': 0, 'memory_total': 0, 'disk_total': 0, 'count': 0
                }

            hourly_stats[hour]['cpu_total'] += metric.get('cpu_usage', 0)
            hourly_stats[hour]['memory_total'] += metric.get('memory_usage', 0)
            hourly_stats[hour]['disk_total'] += metric.get('disk_usage', 0)
            hourly_stats[hour]['count'] += 1

        # Calculate averages
        for hour in hourly_stats:
            stats = hourly_stats[hour]
            count = stats['count']
            stats['cpu_avg'] = stats['cpu_total'] / count
            stats['memory_avg'] = stats['memory_total'] / count
            stats['disk_avg'] = stats['disk_total'] / count

        return {
            'hourly_statistics': hourly_stats,
            'data_points': len(metrics),
            'time_span_hours': len(hourly_stats)
        }

    def _analyze_capacity(self, metrics: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze system capacity and thresholds"""
        if not metrics:
            return {}

        cpu_values = [m['cpu_usage'] for m in metrics if 'cpu_usage' in m]
        memory_values = [m['memory_usage'] for m in metrics if 'memory_usage' in m]
        disk_values = [m['disk_usage'] for m in metrics if 'disk_usage' in m]

        # Define thresholds
        cpu_threshold = 80
        memory_threshold = 85
        disk_threshold = 90

        return {
            'threshold_violations': {
                'cpu_violations': sum(1 for v in cpu_values if v > cpu_threshold),
                'memory_violations': sum(1 for v in memory_values if v > memory_threshold),
                'disk_violations': sum(1 for v in disk_values if v > disk_threshold)
            },
            'capacity_warnings': {
                'cpu_warning': max(cpu_values) > cpu_threshold if cpu_values else False,
                'memory_warning': max(memory_values) > memory_threshold if memory_values else False,
                'disk_warning': max(disk_values) > disk_threshold if disk_values else False
            },
            'recommended_thresholds': {
                'cpu_threshold': cpu_threshold,
                'memory_threshold': memory_threshold,
                'disk_threshold': disk_threshold
            }
        }

    def _generate_system_recommendations(self, metrics: List[Dict[str, Any]]) -> List[str]:
        """Generate system-specific recommendations"""
        recommendations = []

        if not metrics:
            return ["No system metrics available for analysis"]

        cpu_values = [m['cpu_usage'] for m in metrics if 'cpu_usage' in m]
        memory_values = [m['memory_usage'] for m in metrics if 'memory_usage' in m]
        disk_values = [m['disk_usage'] for m in metrics if 'disk_usage' in m]

        # CPU recommendations
        if cpu_values and max(cpu_values) > 80:
            recommendations.append("High CPU usage detected. Consider closing unnecessary applications.")

        # Memory recommendations
        if memory_values and max(memory_values) > 85:
            recommendations.append("High memory usage detected. Consider increasing system RAM.")

        # Disk recommendations
        if disk_values and max(disk_values) > 90:
            recommendations.append("Disk space is critically low. Clean up unnecessary files.")

        # General recommendations
        recommendations.append("Monitor system resources regularly during operations.")
        recommendations.append("Schedule intensive operations during low-usage periods.")

        return recommendations

    def _audit_data_handling(self, operations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Audit data handling practices"""
        return {
            'total_operations_audited': len(operations),
            'data_backup_compliance': sum(1 for op in operations if 'backup' in op.get('operation_type', '')),
            'data_deletion_operations': sum(1 for op in operations if 'clean' in op.get('operation_type', '')),
            'data_modification_operations': sum(1 for op in operations if 'modify' in op.get('operation_type', '')),
            'compliance_score': 95  # Placeholder - would be calculated based on actual compliance rules
        }

    def _audit_security_compliance(self) -> Dict[str, Any]:
        """Audit security compliance"""
        return {
            'encryption_enabled': True,  # Placeholder
            'access_control_enabled': True,  # Placeholder
            'audit_logging_enabled': True,  # Placeholder
            'secure_backup_practices': True,  # Placeholder
            'compliance_score': 98  # Placeholder
        }

    def _audit_backup_compliance(self, operations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Audit backup compliance"""
        destructive_ops = [op for op in operations if 'clean' in op.get('operation_type', '') or 'modify' in op.get('operation_type', '')]

        return {
            'total_destructive_operations': len(destructive_ops),
            'operations_with_backups': len(destructive_ops),  # Assuming all have backups
            'backup_compliance_rate': 100.0,  # Placeholder
            'backup_verification_rate': 95.0,  # Placeholder
            'compliance_score': 99
        }

    def _audit_operation_integrity(self, operations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Audit operation integrity"""
        return {
            'total_operations': len(operations),
            'successful_operations': sum(1 for op in operations if op['success']),
            'failed_operations': sum(1 for op in operations if not op['success']),
            'integrity_score': (sum(1 for op in operations if op['success']) / len(operations) * 100) if operations else 100,
            'data_consistency_checks': len(operations),  # Placeholder
            'rollback_capability': True  # Placeholder
        }

    def _generate_compliance_recommendations(self, operations: List[Dict[str, Any]]) -> List[str]:
        """Generate compliance recommendations"""
        recommendations = [
            "Maintain regular backup schedules for all operations",
            "Implement automated compliance monitoring",
            "Regular security audits are recommended",
            "Document all data handling procedures",
            "Ensure proper access controls are in place"
        ]

        # Add specific recommendations based on operations
        if operations:
            failed_ops = sum(1 for op in operations if not op['success'])
            if failed_ops > 0:
                recommendations.append("Investigate and document all failed operations")

        return recommendations

    def _generate_html_report(self, report: Dict[str, Any]) -> str:
        """Generate HTML version of report"""
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Augment VIP Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
                .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
                .metric { display: inline-block; margin: 10px; padding: 10px; background-color: #e9e9e9; border-radius: 3px; }
                table { width: 100%; border-collapse: collapse; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Augment VIP Operation Report</h1>
                <p>Generated: {generated_at}</p>
            </div>
            <div class="section">
                <h2>Summary</h2>
                <p>Report content would be dynamically generated here based on the report data.</p>
            </div>
        </body>
        </html>
        """.format(
            generated_at=report.get('report_metadata', {}).get('generated_at', 'Unknown')
        )

        return html_template

    def _generate_csv_report(self, report: Dict[str, Any]) -> str:
        """Generate CSV version of report"""
        csv_content = "Report Type,Generated At,Total Operations,Success Rate\n"

        metadata = report.get('report_metadata', {})
        summary = report.get('summary_statistics', {})

        csv_content += f"Operation Report,{metadata.get('generated_at', '')},{summary.get('total_operations', 0)},{summary.get('success_rate', 0)}\n"

        return csv_content
