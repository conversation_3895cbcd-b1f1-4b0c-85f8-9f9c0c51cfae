import React, { useState, useEffect } from "react";
import {
  Save,
  Download,
  Upload,
  Clock,
  Calendar,
  Settings,
  Trash2,
  <PERSON>freshCw,
  CheckCircle,
  AlertCircle,
  HardDrive,
  FolderOpen,
  Archive,
  Shield,
  Database,
  FileText,
  Zap,
  Play,
  Pause,
  RotateCcw,
} from "lucide-react";
import useAppStore from "../store/useAppStore";
import { <PERSON><PERSON>, Card, CardContent, Badge } from "./ui";

interface BackupItem {
  id: string;
  name: string;
  type: 'full' | 'incremental' | 'differential';
  size: number;
  createdAt: Date;
  status: 'completed' | 'failed' | 'in-progress';
  location: string;
  description?: string;
  includedItems: string[];
}

interface BackupSchedule {
  id: string;
  name: string;
  frequency: 'daily' | 'weekly' | 'monthly' | 'custom';
  time: string;
  enabled: boolean;
  lastRun?: Date;
  nextRun: Date;
  backupType: 'full' | 'incremental';
  includedPaths: string[];
}

const Backup: React.FC = () => {
  const {
    isOperationRunning,
    currentOperation,
    operationProgress,
    startOperation,
    finishOperation,
    updateOperationProgress,
    addOperationLog,
  } = useAppStore();

  const [activeTab, setActiveTab] = useState<'backups' | 'schedules' | 'restore' | 'settings'>('backups');
  const [backups, setBackups] = useState<BackupItem[]>([]);
  const [schedules, setSchedules] = useState<BackupSchedule[]>([]);
  const [selectedBackup, setSelectedBackup] = useState<BackupItem | null>(null);
  const [showCreateBackup, setShowCreateBackup] = useState(false);
  const [showCreateSchedule, setShowCreateSchedule] = useState(false);
  const [backupSettings, setBackupSettings] = useState({
    defaultLocation: '/Users/<USER>/AugmentVIP/Backups',
    compression: true,
    encryption: false,
    maxBackups: 10,
    autoCleanup: true,
  });

  // Sample data initialization
  useEffect(() => {
    const sampleBackups: BackupItem[] = [
      {
        id: '1',
        name: 'System Full Backup',
        type: 'full',
        size: 2.4 * 1024 * 1024 * 1024, // 2.4 GB
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
        status: 'completed',
        location: '/Users/<USER>/AugmentVIP/Backups/system_full_20240706.avb',
        description: 'Complete system state and application data backup',
        includedItems: ['System Settings', 'Application Data', 'User Preferences', 'Database Files'],
      },
      {
        id: '2',
        name: 'Database Backup',
        type: 'incremental',
        size: 156 * 1024 * 1024, // 156 MB
        createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
        status: 'completed',
        location: '/Users/<USER>/AugmentVIP/Backups/database_inc_20240707.avb',
        description: 'Incremental backup of database changes',
        includedItems: ['SQLite Databases', 'Configuration Files'],
      },
      {
        id: '3',
        name: 'Quick Backup',
        type: 'differential',
        size: 45 * 1024 * 1024, // 45 MB
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
        status: 'completed',
        location: '/Users/<USER>/AugmentVIP/Backups/quick_diff_20240708.avb',
        description: 'Quick differential backup of recent changes',
        includedItems: ['Recent Changes', 'Temporary Files'],
      },
    ];

    const sampleSchedules: BackupSchedule[] = [
      {
        id: '1',
        name: 'Daily System Backup',
        frequency: 'daily',
        time: '02:00',
        enabled: true,
        lastRun: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
        nextRun: new Date(Date.now() + 6 * 60 * 60 * 1000),
        backupType: 'incremental',
        includedPaths: ['/System', '/Applications/AugmentVIP'],
      },
      {
        id: '2',
        name: 'Weekly Full Backup',
        frequency: 'weekly',
        time: '01:00',
        enabled: true,
        lastRun: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        nextRun: new Date(Date.now() + 6 * 24 * 60 * 60 * 1000),
        backupType: 'full',
        includedPaths: ['/System', '/Applications', '/Users/<USER>/AugmentVIP'],
      },
    ];

    setBackups(sampleBackups);
    setSchedules(sampleSchedules);
  }, []);

  const formatFileSize = (bytes: number): string => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const getStatusIcon = (status: BackupItem['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'in-progress':
        return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />;
      default:
        return null;
    }
  };

  const getTypeColor = (type: BackupItem['type']) => {
    switch (type) {
      case 'full':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'incremental':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'differential':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const handleCreateBackup = async (type: 'full' | 'incremental' | 'quick') => {
    if (isOperationRunning) return;

    const operationName = `${type.charAt(0).toUpperCase() + type.slice(1)} Backup`;
    startOperation(operationName);

    try {
      // Simulate backup creation progress
      for (let i = 0; i <= 100; i += 10) {
        await new Promise(resolve => setTimeout(resolve, 300));
        updateOperationProgress(i);
      }

      // Create new backup item
      const newBackup: BackupItem = {
        id: Date.now().toString(),
        name: `${operationName} - ${new Date().toLocaleDateString()}`,
        type: type === 'quick' ? 'differential' : type,
        size: Math.random() * 1024 * 1024 * 1024, // Random size
        createdAt: new Date(),
        status: 'completed',
        location: `${backupSettings.defaultLocation}/${type}_${Date.now()}.avb`,
        description: `${operationName} created on ${new Date().toLocaleString()}`,
        includedItems: type === 'full' 
          ? ['System Settings', 'Application Data', 'User Preferences', 'Database Files']
          : ['Recent Changes', 'Modified Files'],
      };

      setBackups(prev => [newBackup, ...prev]);
      finishOperation(true, `${operationName} completed successfully`, `Backup saved to ${newBackup.location}`);
      addOperationLog({
        operation: 'backup' as any,
        status: 'success',
        message: `${operationName} completed`,
        details: `Size: ${formatFileSize(newBackup.size)}`,
      });
    } catch (error) {
      finishOperation(false, `${operationName} failed`, error instanceof Error ? error.message : 'Unknown error');
    }
  };

  const handleRestoreBackup = async (backup: BackupItem) => {
    if (isOperationRunning) return;

    startOperation('Restore Backup');

    try {
      // Simulate restore progress
      for (let i = 0; i <= 100; i += 15) {
        await new Promise(resolve => setTimeout(resolve, 400));
        updateOperationProgress(i);
      }

      finishOperation(true, 'Backup restored successfully', `Restored from ${backup.name}`);
      addOperationLog({
        operation: 'restore' as any,
        status: 'success',
        message: 'Backup restored',
        details: `Restored from: ${backup.name}`,
      });
    } catch (error) {
      finishOperation(false, 'Restore failed', error instanceof Error ? error.message : 'Unknown error');
    }
  };

  const handleDeleteBackup = (backupId: string) => {
    setBackups(prev => prev.filter(b => b.id !== backupId));
    addOperationLog({
      operation: 'delete' as any,
      status: 'success',
      message: 'Backup deleted',
      details: `Backup ID: ${backupId}`,
    });
  };

  const toggleSchedule = (scheduleId: string) => {
    setSchedules(prev => prev.map(s => 
      s.id === scheduleId ? { ...s, enabled: !s.enabled } : s
    ));
  };

  const renderBackupsTab = () => (
    <div className="space-y-6">
      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Button
          onClick={() => handleCreateBackup('full')}
          disabled={isOperationRunning}
          className="h-20 flex flex-col items-center justify-center space-y-2"
        >
          <Database className="w-6 h-6" />
          <span>Full Backup</span>
        </Button>
        <Button
          onClick={() => handleCreateBackup('incremental')}
          disabled={isOperationRunning}
          variant="outline"
          className="h-20 flex flex-col items-center justify-center space-y-2"
        >
          <Archive className="w-6 h-6" />
          <span>Incremental</span>
        </Button>
        <Button
          onClick={() => handleCreateBackup('quick')}
          disabled={isOperationRunning}
          variant="outline"
          className="h-20 flex flex-col items-center justify-center space-y-2"
        >
          <Zap className="w-6 h-6" />
          <span>Quick Backup</span>
        </Button>
      </div>

      {/* Backup List */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Recent Backups</h3>
        {backups.map((backup) => (
          <Card key={backup.id} className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  {getStatusIcon(backup.status)}
                  <div>
                    <h4 className="font-medium">{backup.name}</h4>
                    <p className="text-sm text-gray-500">{backup.description}</p>
                  </div>
                </div>
                <Badge className={getTypeColor(backup.type)}>
                  {backup.type}
                </Badge>
              </div>
              <div className="flex items-center space-x-4">
                <div className="text-right text-sm">
                  <p className="font-medium">{formatFileSize(backup.size)}</p>
                  <p className="text-gray-500">{backup.createdAt.toLocaleDateString()}</p>
                </div>
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleRestoreBackup(backup)}
                    disabled={isOperationRunning}
                  >
                    <RotateCcw className="w-4 h-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleDeleteBackup(backup.id)}
                    disabled={isOperationRunning}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderSchedulesTab = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Backup Schedules</h3>
        <Button onClick={() => setShowCreateSchedule(true)}>
          <Calendar className="w-4 h-4 mr-2" />
          New Schedule
        </Button>
      </div>

      <div className="space-y-4">
        {schedules.map((schedule) => (
          <Card key={schedule.id} className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Clock className="w-5 h-5 text-blue-500" />
                  <div>
                    <h4 className="font-medium">{schedule.name}</h4>
                    <p className="text-sm text-gray-500">
                      {schedule.frequency} at {schedule.time} • {schedule.backupType} backup
                    </p>
                  </div>
                </div>
                <Badge variant={schedule.enabled ? "success" : "outline"}>
                  {schedule.enabled ? "Active" : "Disabled"}
                </Badge>
              </div>
              <div className="flex items-center space-x-4">
                <div className="text-right text-sm">
                  <p className="font-medium">
                    Next: {schedule.nextRun.toLocaleDateString()}
                  </p>
                  <p className="text-gray-500">
                    Last: {schedule.lastRun?.toLocaleDateString() || 'Never'}
                  </p>
                </div>
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => toggleSchedule(schedule.id)}
                  >
                    {schedule.enabled ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {/* Edit schedule */}}
                  >
                    <Settings className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderRestoreTab = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">Restore from Backup</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6">
          <h4 className="font-medium mb-4">Select Backup to Restore</h4>
          <div className="space-y-3">
            {backups.filter(b => b.status === 'completed').map((backup) => (
              <div
                key={backup.id}
                className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                  selectedBackup?.id === backup.id
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedBackup(backup)}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <p className="font-medium">{backup.name}</p>
                    <p className="text-sm text-gray-500">{backup.createdAt.toLocaleDateString()}</p>
                  </div>
                  <Badge className={getTypeColor(backup.type)}>
                    {backup.type}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </Card>

        <Card className="p-6">
          <h4 className="font-medium mb-4">Restore Options</h4>
          {selectedBackup ? (
            <div className="space-y-4">
              <div>
                <h5 className="font-medium text-sm mb-2">Selected Backup:</h5>
                <p className="text-sm">{selectedBackup.name}</p>
                <p className="text-xs text-gray-500">{formatFileSize(selectedBackup.size)}</p>
              </div>

              <div>
                <h5 className="font-medium text-sm mb-2">Included Items:</h5>
                <div className="space-y-1">
                  {selectedBackup.includedItems.map((item, index) => (
                    <div key={index} className="flex items-center space-x-2 text-sm">
                      <CheckCircle className="w-3 h-3 text-green-500" />
                      <span>{item}</span>
                    </div>
                  ))}
                </div>
              </div>

              <Button
                onClick={() => handleRestoreBackup(selectedBackup)}
                disabled={isOperationRunning}
                className="w-full"
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                Restore Backup
              </Button>
            </div>
          ) : (
            <p className="text-gray-500 text-sm">Select a backup to see restore options</p>
          )}
        </Card>
      </div>
    </div>
  );

  const renderSettingsTab = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">Backup Settings</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6">
          <h4 className="font-medium mb-4">General Settings</h4>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Default Backup Location</label>
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={backupSettings.defaultLocation}
                  onChange={(e) => setBackupSettings(prev => ({ ...prev, defaultLocation: e.target.value }))}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                />
                <Button size="sm" variant="outline">
                  <FolderOpen className="w-4 h-4" />
                </Button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Maximum Backups to Keep</label>
              <input
                type="number"
                value={backupSettings.maxBackups}
                onChange={(e) => setBackupSettings(prev => ({ ...prev, maxBackups: parseInt(e.target.value) }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                min="1"
                max="100"
              />
            </div>

            <div className="space-y-3">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={backupSettings.compression}
                  onChange={(e) => setBackupSettings(prev => ({ ...prev, compression: e.target.checked }))}
                  className="rounded"
                />
                <span className="text-sm">Enable compression</span>
              </label>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={backupSettings.encryption}
                  onChange={(e) => setBackupSettings(prev => ({ ...prev, encryption: e.target.checked }))}
                  className="rounded"
                />
                <span className="text-sm">Enable encryption</span>
              </label>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={backupSettings.autoCleanup}
                  onChange={(e) => setBackupSettings(prev => ({ ...prev, autoCleanup: e.target.checked }))}
                  className="rounded"
                />
                <span className="text-sm">Auto-cleanup old backups</span>
              </label>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h4 className="font-medium mb-4">Storage Information</h4>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm">Total Backups:</span>
              <span className="font-medium">{backups.length}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Total Size:</span>
              <span className="font-medium">
                {formatFileSize(backups.reduce((total, backup) => total + backup.size, 0))}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Available Space:</span>
              <span className="font-medium text-green-600">2.1 TB</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Active Schedules:</span>
              <span className="font-medium">{schedules.filter(s => s.enabled).length}</span>
            </div>
          </div>

          <div className="mt-6 pt-4 border-t">
            <Button variant="outline" className="w-full">
              <Shield className="w-4 h-4 mr-2" />
              Verify All Backups
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">Backup Management</h1>
        <p className="text-gray-600">Create, schedule, and manage your system backups</p>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 mb-6 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
        {[
          { id: 'backups', label: 'Backups', icon: Save },
          { id: 'schedules', label: 'Schedules', icon: Calendar },
          { id: 'restore', label: 'Restore', icon: RotateCcw },
          { id: 'settings', label: 'Settings', icon: Settings },
        ].map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
              }`}
            >
              <Icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          );
        })}
      </div>

      {/* Tab Content */}
      {activeTab === 'backups' && renderBackupsTab()}
      {activeTab === 'schedules' && renderSchedulesTab()}
      {activeTab === 'restore' && renderRestoreTab()}
      {activeTab === 'settings' && renderSettingsTab()}
    </div>
  );
};

export default Backup;
