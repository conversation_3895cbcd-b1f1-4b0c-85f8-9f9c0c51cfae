"""
Application Configuration Management
Environment-based configuration with validation and secrets management
"""

import os
import secrets
from functools import lru_cache
from typing import List, Optional, Union

from pydantic import Field, validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """
    Application settings with environment variable support
    """
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=True,
        extra="ignore"
    )
    
    # Application Info
    APP_NAME: str = "Augment VIP API"
    VERSION: str = "2.0.0"
    DESCRIPTION: str = "Enterprise-grade backend for Augment Trial VIP system"
    
    # Environment
    ENVIRONMENT: str = Field(default="development", description="Environment: development, staging, production")
    DEBUG: bool = Field(default=True, description="Enable debug mode")
    
    # Server Configuration
    HOST: str = Field(default="127.0.0.1", description="Server host")
    PORT: int = Field(default=8000, description="Server port")
    WORKERS: int = Field(default=1, description="Number of worker processes")
    
    # Security
    SECRET_KEY: str = Field(default_factory=lambda: secrets.token_urlsafe(32))
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, description="JWT token expiration in minutes")
    REFRESH_TOKEN_EXPIRE_DAYS: int = Field(default=7, description="Refresh token expiration in days")
    PASSWORD_MIN_LENGTH: int = Field(default=8, description="Minimum password length")
    
    # CORS Configuration
    CORS_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:5173", "http://127.0.0.1:3000"],
        description="Allowed CORS origins"
    )
    ALLOWED_HOSTS: List[str] = Field(
        default=["localhost", "127.0.0.1"],
        description="Allowed hosts for TrustedHostMiddleware"
    )
    
    # Database Configuration
    DATABASE_URL: str = Field(
        default="sqlite+aiosqlite:///./augment_vip.db",
        description="Database connection URL"
    )
    DATABASE_ECHO: bool = Field(default=False, description="Enable SQLAlchemy query logging")
    DATABASE_POOL_SIZE: int = Field(default=5, description="Database connection pool size")
    DATABASE_MAX_OVERFLOW: int = Field(default=10, description="Database connection pool overflow")
    
    # Redis Configuration (for caching and sessions)
    REDIS_URL: str = Field(default="redis://localhost:6379/0", description="Redis connection URL")
    REDIS_EXPIRE_SECONDS: int = Field(default=3600, description="Default Redis key expiration")
    
    # Celery Configuration (for background tasks)
    CELERY_BROKER_URL: str = Field(default="redis://localhost:6379/1", description="Celery broker URL")
    CELERY_RESULT_BACKEND: str = Field(default="redis://localhost:6379/2", description="Celery result backend")
    
    # Logging Configuration
    LOG_LEVEL: str = Field(default="INFO", description="Logging level")
    LOG_FORMAT: str = Field(default="json", description="Log format: json or console")
    LOG_FILE: Optional[str] = Field(default=None, description="Log file path")
    
    # Monitoring & Observability
    SENTRY_DSN: Optional[str] = Field(default=None, description="Sentry DSN for error tracking")
    SENTRY_TRACES_SAMPLE_RATE: float = Field(default=0.1, description="Sentry traces sample rate")
    METRICS_ENABLED: bool = Field(default=True, description="Enable Prometheus metrics")
    
    # Rate Limiting
    RATE_LIMIT_ENABLED: bool = Field(default=True, description="Enable rate limiting")
    RATE_LIMIT_REQUESTS: int = Field(default=100, description="Rate limit requests per window")
    RATE_LIMIT_WINDOW: int = Field(default=60, description="Rate limit window in seconds")
    
    # File Upload Configuration
    MAX_UPLOAD_SIZE: int = Field(default=10 * 1024 * 1024, description="Max file upload size in bytes (10MB)")
    UPLOAD_DIR: str = Field(default="./uploads", description="Upload directory")
    
    # WebSocket Configuration
    WEBSOCKET_ENABLED: bool = Field(default=True, description="Enable WebSocket support")
    WEBSOCKET_HEARTBEAT_INTERVAL: int = Field(default=30, description="WebSocket heartbeat interval in seconds")
    
    # Background Tasks
    BACKGROUND_TASKS_ENABLED: bool = Field(default=True, description="Enable background task processing")
    TASK_QUEUE_MAX_SIZE: int = Field(default=1000, description="Maximum task queue size")
    
    @validator("ENVIRONMENT")
    def validate_environment(cls, v):
        """Validate environment setting"""
        allowed = ["development", "staging", "production"]
        if v not in allowed:
            raise ValueError(f"Environment must be one of: {allowed}")
        return v
    
    @validator("LOG_LEVEL")
    def validate_log_level(cls, v):
        """Validate log level setting"""
        allowed = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in allowed:
            raise ValueError(f"Log level must be one of: {allowed}")
        return v.upper()
    
    @validator("LOG_FORMAT")
    def validate_log_format(cls, v):
        """Validate log format setting"""
        allowed = ["json", "console"]
        if v not in allowed:
            raise ValueError(f"Log format must be one of: {allowed}")
        return v
    
    @property
    def is_development(self) -> bool:
        """Check if running in development environment"""
        return self.ENVIRONMENT == "development"
    
    @property
    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self.ENVIRONMENT == "production"
    
    @property
    def database_url_sync(self) -> str:
        """Get synchronous database URL (for Alembic migrations)"""
        return self.DATABASE_URL.replace("+aiosqlite", "").replace("+asyncpg", "+psycopg2")


@lru_cache()
def get_settings() -> Settings:
    """
    Get cached application settings
    """
    return Settings()


# Export settings instance for convenience
settings = get_settings()
