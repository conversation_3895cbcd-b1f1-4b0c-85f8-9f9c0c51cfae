{"version": 3, "file": "npmNodeModulesCollector.js", "sourceRoot": "", "sources": ["../../src/node-module-collector/npmNodeModulesCollector.ts"], "names": [], "mappings": ";;;AAAA,uCAA+B;AAC/B,iEAA6D;AAE7D,+CAAkC;AAElC,MAAa,uBAAwB,SAAQ,2CAA2C;IACtF,YAAY,OAAe;QACzB,KAAK,CAAC,OAAO,CAAC,CAAA;QAGA,cAAS,GAAG,IAAI,eAAI,CAAS,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAA;QACrG,mBAAc,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,mBAAmB,EAAE,CAAC,CAAC,CAAA;IAHzH,CAAC;IAKS,OAAO;QACf,OAAO,CAAC,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAA;IACtH,CAAC;IAES,mBAAmB,CAAC,OAAsB;QAClD,MAAM,IAAI,GAAG,KAAK,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAA;QAC/C,MAAM,EAAE,oBAAoB,EAAE,aAAa,EAAE,GAAG,OAAO,CAAA;QACvD,OAAO,EAAE,GAAG,IAAI,EAAE,oBAAoB,EAAE,aAAa,EAAE,CAAA;IACzD,CAAC;IAES,+BAA+B,CAAC,IAAmB;;QAC3D,MAAM,KAAK,GAAG,MAAA,IAAI,CAAC,aAAa,mCAAI,EAAE,CAAA;QAEtC,IAAI,IAAI,GAAG,MAAA,IAAI,CAAC,YAAY,mCAAI,EAAE,CAAA;QAClC,IAAI,4BAA4B,GAAG,KAAK,CAAA;QAExC,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpE,kBAAG,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE,kCAAkC,CAAC,CAAA;YACzF,IAAI,GAAG,MAAA,MAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC,0CAAE,YAAY,mCAAI,EAAE,CAAA;YACnF,4BAA4B,GAAG,IAAI,CAAA;QACrC,CAAC;QAED,MAAM,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,CAAiC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YAC7F,MAAM,CAAC,WAAW,EAAE,UAAU,CAAC,GAAG,IAAI,CAAA;YACtC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAChE,OAAO,GAAG,CAAA;YACZ,CAAC;YACD,IAAI,4BAA4B,EAAE,CAAC;gBACjC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,UAAU,CAAA;gBACtD,MAAM,cAAc,GAAyB,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,CAAA;gBAChF,OAAO;oBACL,GAAG,GAAG;oBACN,CAAC,WAAW,CAAC,EAAE,EAAE,GAAG,cAAc,EAAE,4BAA4B,EAAE;iBACnE,CAAA;YACH,CAAC;YACD,OAAO;gBACL,GAAG,GAAG;gBACN,CAAC,WAAW,CAAC,EAAE,IAAI,CAAC,+BAA+B,CAAC,UAAU,CAAC;aAChE,CAAA;QACH,CAAC,EAAE,EAAE,CAAC,CAAA;QAEN,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,IAAI,CAAA;QAC7D,MAAM,OAAO,GAAmB;YAC9B,IAAI;YACJ,OAAO;YACP,IAAI,EAAE,WAAW;YACjB,UAAU;YACV,YAAY;YACZ,4BAA4B;SAC7B,CAAA;QACD,OAAO,OAAO,CAAA;IAChB,CAAC;IAES,qBAAqB,CAAC,QAAgB;QAC9C,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;IAC7B,CAAC;CACF;AAhED,0DAgEC", "sourcesContent": ["import { Lazy } from \"lazy-val\"\nimport { NodeModulesCollector } from \"./nodeModulesCollector\"\nimport { DependencyTree, NpmDependency, ParsedDependencyTree } from \"./types\"\nimport { log } from \"builder-util\"\n\nexport class NpmNodeModulesCollector extends NodeModulesCollector<NpmDependency, string> {\n  constructor(rootDir: string) {\n    super(rootDir)\n  }\n\n  public readonly pmCommand = new Lazy<string>(() => Promise.resolve(process.platform === \"win32\" ? \"npm.cmd\" : \"npm\"))\n  public readonly installOptions = this.pmCommand.value.then(cmd => ({ cmd, args: [\"ci\"], lockfile: \"package-lock.json\" }))\n\n  protected getArgs(): string[] {\n    return [\"list\", \"-a\", \"--include\", \"prod\", \"--include\", \"optional\", \"--omit\", \"dev\", \"--json\", \"--long\", \"--silent\"]\n  }\n\n  protected extractRelevantData(npmTree: NpmDependency): NpmDependency {\n    const tree = super.extractRelevantData(npmTree)\n    const { optionalDependencies, _dependencies } = npmTree\n    return { ...tree, optionalDependencies, _dependencies }\n  }\n\n  protected extractProductionDependencyTree(tree: NpmDependency): DependencyTree {\n    const _deps = tree._dependencies ?? {}\n\n    let deps = tree.dependencies ?? {}\n    let implicitDependenciesInjected = false\n\n    if (Object.keys(_deps).length > 0 && Object.keys(deps).length === 0) {\n      log.debug({ name: tree.name, version: tree.version }, \"injecting implicit _dependencies\")\n      deps = this.allDependencies.get(`${tree.name}@${tree.version}`)?.dependencies ?? {}\n      implicitDependenciesInjected = true\n    }\n\n    const dependencies = Object.entries(deps).reduce<DependencyTree[\"dependencies\"]>((acc, curr) => {\n      const [packageName, dependency] = curr\n      if (!_deps[packageName] || Object.keys(dependency).length === 0) {\n        return acc\n      }\n      if (implicitDependenciesInjected) {\n        const { name, version, path, workspaces } = dependency\n        const simplifiedTree: ParsedDependencyTree = { name, version, path, workspaces }\n        return {\n          ...acc,\n          [packageName]: { ...simplifiedTree, implicitDependenciesInjected },\n        }\n      }\n      return {\n        ...acc,\n        [packageName]: this.extractProductionDependencyTree(dependency),\n      }\n    }, {})\n\n    const { name, version, path: packagePath, workspaces } = tree\n    const depTree: DependencyTree = {\n      name,\n      version,\n      path: packagePath,\n      workspaces,\n      dependencies,\n      implicitDependenciesInjected,\n    }\n    return depTree\n  }\n\n  protected parseDependenciesTree(jsonBlob: string): NpmDependency {\n    return JSON.parse(jsonBlob)\n  }\n}\n"]}