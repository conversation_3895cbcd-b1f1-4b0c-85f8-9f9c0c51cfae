#!/usr/bin/env python3
"""
Development Server Runner
Quick script to run the FastAPI server with proper configuration
"""

import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """Run the development server"""
    import uvicorn
    from augment_vip.core.config import get_settings
    
    settings = get_settings()
    
    print(f"[*] Starting Augment VIP API Server")
    print(f"[*] Environment: {settings.ENVIRONMENT}")
    print(f"[*] Server: http://{settings.HOST}:{settings.PORT}")
    print(f"[*] API Docs: http://{settings.HOST}:{settings.PORT}/docs")
    print(f"[*] Health Check: http://{settings.HOST}:{settings.PORT}/health")
    print("-" * 50)
    
    # Create logs directory if it doesn't exist
    if settings.LOG_FILE:
        log_dir = Path(settings.LOG_FILE).parent
        log_dir.mkdir(parents=True, exist_ok=True)
    
    # Run the server
    uvicorn.run(
        "augment_vip.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_config=None,  # Use our custom logging
        access_log=False,  # Disable uvicorn access logs
        workers=1 if settings.DEBUG else settings.WORKERS,
    )


if __name__ == "__main__":
    main()
