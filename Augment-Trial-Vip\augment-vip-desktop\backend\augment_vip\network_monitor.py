"""
Safe Network Traffic Monitor
Passive monitoring of telemetry and tracking network traffic
"""

import os
import json
import time
import threading
import platform
from pathlib import Path
from typing import List, Dict, Any, Optional, Set, Callable
from datetime import datetime, timedelta
from collections import defaultdict
import re

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

from .utils import info, success, error, warning

class NetworkTelemetryMonitor:
    """Safe network traffic monitor for telemetry detection"""
    
    def __init__(self):
        if not PSUTIL_AVAILABLE:
            raise ImportError("psutil is required for network monitoring")
        
        self.platform = platform.system().lower()
        self.monitoring = False
        self.monitor_thread = None
        self.telemetry_patterns = self._load_telemetry_patterns()
        self.connection_log = []
        self.suspicious_domains = set()
        self.process_connections = defaultdict(list)
        
    def _load_telemetry_patterns(self) -> Dict[str, List[str]]:
        """Load known telemetry and tracking domain patterns"""
        return {
            'google_analytics': [
                'google-analytics.com', 'googletagmanager.com', 'doubleclick.net',
                'googlesyndication.com', 'googleadservices.com'
            ],
            'microsoft_telemetry': [
                'telemetry.microsoft.com', 'vortex.data.microsoft.com',
                'watson.telemetry.microsoft.com', 'settings-win.data.microsoft.com'
            ],
            'adobe_tracking': [
                'omtrdc.net', 'demdex.net', 'everesttech.net',
                'adobe.com/b/ss', 'adobedtm.com'
            ],
            'development_tools': [
                'vscode-update.azurewebsites.net', 'marketplace.visualstudio.com',
                'az764295.vo.msecnd.net', 'jetbrains.com/api',
                'resources.jetbrains.com'
            ],
            'social_tracking': [
                'facebook.com/tr', 'connect.facebook.net', 'twitter.com/i/adsct',
                'linkedin.com/li.lms-analytics', 'snapchat.com/tr'
            ],
            'general_tracking': [
                'hotjar.com', 'mixpanel.com', 'segment.com', 'amplitude.com',
                'fullstory.com', 'loggly.com', 'bugsnag.com'
            ]
        }
    
    def start_monitoring(self, 
                        duration_minutes: int = 10,
                        target_processes: Optional[List[str]] = None,
                        callback: Optional[Callable] = None) -> str:
        """
        Start passive network monitoring
        
        Args:
            duration_minutes: How long to monitor (0 = indefinite)
            target_processes: Specific processes to monitor
            callback: Optional callback for real-time updates
        
        Returns:
            Monitoring session ID
        """
        if self.monitoring:
            warning("Network monitoring is already active")
            return None
        
        session_id = f"monitor_{int(time.time())}"
        
        info(f"Starting network telemetry monitoring for {duration_minutes} minutes")
        info("This is passive monitoring - no network changes will be made")
        
        self.monitoring = True
        self.connection_log = []
        self.suspicious_domains = set()
        self.process_connections = defaultdict(list)
        
        # Start monitoring thread
        self.monitor_thread = threading.Thread(
            target=self._monitor_network_traffic,
            args=(duration_minutes, target_processes, callback),
            daemon=True
        )
        self.monitor_thread.start()
        
        return session_id
    
    def stop_monitoring(self) -> Dict[str, Any]:
        """Stop network monitoring and return results"""
        if not self.monitoring:
            warning("Network monitoring is not active")
            return {}
        
        info("Stopping network monitoring...")
        self.monitoring = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        # Generate monitoring report
        report = self._generate_monitoring_report()
        
        success("Network monitoring stopped")
        return report
    
    def _monitor_network_traffic(self, 
                                duration_minutes: int,
                                target_processes: Optional[List[str]],
                                callback: Optional[Callable]):
        """Internal method to monitor network traffic"""
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60) if duration_minutes > 0 else float('inf')
        
        try:
            while self.monitoring and time.time() < end_time:
                # Get current network connections
                connections = self._get_network_connections(target_processes)
                
                # Analyze connections for telemetry
                for conn in connections:
                    self._analyze_connection(conn)
                
                # Call callback if provided
                if callback:
                    callback(self._get_current_stats())
                
                # Sleep before next scan
                time.sleep(2)  # Check every 2 seconds
        
        except Exception as e:
            error(f"Network monitoring error: {e}")
        finally:
            self.monitoring = False
    
    def _get_network_connections(self, target_processes: Optional[List[str]]) -> List[Dict[str, Any]]:
        """Get current network connections"""
        connections = []
        
        try:
            # Get all network connections
            for conn in psutil.net_connections(kind='inet'):
                if conn.status == 'ESTABLISHED' and conn.raddr:
                    try:
                        # Get process info
                        process = psutil.Process(conn.pid) if conn.pid else None
                        process_name = process.name() if process else 'Unknown'
                        
                        # Filter by target processes if specified
                        if target_processes and process_name.lower() not in [p.lower() for p in target_processes]:
                            continue
                        
                        connection_info = {
                            'timestamp': datetime.now().isoformat(),
                            'pid': conn.pid,
                            'process_name': process_name,
                            'local_address': f"{conn.laddr.ip}:{conn.laddr.port}",
                            'remote_address': f"{conn.raddr.ip}:{conn.raddr.port}",
                            'remote_ip': conn.raddr.ip,
                            'remote_port': conn.raddr.port,
                            'status': conn.status
                        }
                        
                        # Try to resolve hostname
                        try:
                            import socket
                            hostname = socket.gethostbyaddr(conn.raddr.ip)[0]
                            connection_info['remote_hostname'] = hostname
                        except:
                            connection_info['remote_hostname'] = None
                        
                        connections.append(connection_info)
                    
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
        
        except Exception as e:
            warning(f"Error getting network connections: {e}")
        
        return connections
    
    def _analyze_connection(self, connection: Dict[str, Any]):
        """Analyze a network connection for telemetry patterns"""
        remote_ip = connection.get('remote_ip', '')
        remote_hostname = connection.get('remote_hostname', '')
        process_name = connection.get('process_name', '')
        
        # Check against telemetry patterns
        is_suspicious = False
        telemetry_categories = []
        
        for category, patterns in self.telemetry_patterns.items():
            for pattern in patterns:
                if (pattern in remote_hostname.lower() if remote_hostname else False) or \
                   (pattern in remote_ip):
                    is_suspicious = True
                    telemetry_categories.append(category)
                    self.suspicious_domains.add(remote_hostname or remote_ip)
        
        # Log the connection
        log_entry = {
            **connection,
            'is_suspicious': is_suspicious,
            'telemetry_categories': telemetry_categories,
            'analysis_timestamp': datetime.now().isoformat()
        }
        
        self.connection_log.append(log_entry)
        
        # Track by process
        if process_name:
            self.process_connections[process_name].append(log_entry)
        
        # Log suspicious connections
        if is_suspicious:
            warning(f"Suspicious connection detected: {process_name} -> {remote_hostname or remote_ip} ({', '.join(telemetry_categories)})")
    
    def _get_current_stats(self) -> Dict[str, Any]:
        """Get current monitoring statistics"""
        total_connections = len(self.connection_log)
        suspicious_connections = sum(1 for conn in self.connection_log if conn.get('is_suspicious', False))
        
        return {
            'total_connections': total_connections,
            'suspicious_connections': suspicious_connections,
            'suspicious_domains': list(self.suspicious_domains),
            'monitored_processes': list(self.process_connections.keys()),
            'monitoring_active': self.monitoring
        }
    
    def _generate_monitoring_report(self) -> Dict[str, Any]:
        """Generate comprehensive monitoring report"""
        total_connections = len(self.connection_log)
        suspicious_connections = [conn for conn in self.connection_log if conn.get('is_suspicious', False)]
        
        # Analyze by process
        process_analysis = {}
        for process_name, connections in self.process_connections.items():
            suspicious_count = sum(1 for conn in connections if conn.get('is_suspicious', False))
            
            process_analysis[process_name] = {
                'total_connections': len(connections),
                'suspicious_connections': suspicious_count,
                'risk_level': 'high' if suspicious_count > 5 else 'medium' if suspicious_count > 0 else 'low',
                'unique_domains': len(set(
                    conn.get('remote_hostname', conn.get('remote_ip', ''))
                    for conn in connections
                ))
            }
        
        # Analyze by telemetry category
        category_analysis = defaultdict(int)
        for conn in suspicious_connections:
            for category in conn.get('telemetry_categories', []):
                category_analysis[category] += 1
        
        # Generate domain frequency analysis
        domain_frequency = defaultdict(int)
        for conn in self.connection_log:
            domain = conn.get('remote_hostname') or conn.get('remote_ip', '')
            if domain:
                domain_frequency[domain] += 1
        
        # Calculate privacy risk score
        privacy_risk = self._calculate_network_privacy_risk(
            total_connections, len(suspicious_connections), len(self.suspicious_domains)
        )
        
        report = {
            'monitoring_summary': {
                'session_duration': self._calculate_session_duration(),
                'total_connections_monitored': total_connections,
                'suspicious_connections_detected': len(suspicious_connections),
                'unique_suspicious_domains': len(self.suspicious_domains),
                'monitored_processes': len(self.process_connections),
                'privacy_risk_score': privacy_risk
            },
            'process_analysis': dict(process_analysis),
            'telemetry_category_breakdown': dict(category_analysis),
            'suspicious_domains': list(self.suspicious_domains),
            'top_contacted_domains': dict(sorted(domain_frequency.items(), key=lambda x: x[1], reverse=True)[:20]),
            'detailed_connections': suspicious_connections,
            'recommendations': self._generate_network_recommendations(privacy_risk, process_analysis, category_analysis)
        }
        
        return report
    
    def _calculate_session_duration(self) -> str:
        """Calculate monitoring session duration"""
        if not self.connection_log:
            return "0 seconds"
        
        start_time = datetime.fromisoformat(self.connection_log[0]['timestamp'])
        end_time = datetime.fromisoformat(self.connection_log[-1]['timestamp'])
        duration = end_time - start_time
        
        return str(duration)
    
    def _calculate_network_privacy_risk(self, total_connections: int, suspicious_connections: int, unique_domains: int) -> int:
        """Calculate network privacy risk score (0-100)"""
        risk_score = 0
        
        # Risk from suspicious connection ratio
        if total_connections > 0:
            suspicious_ratio = suspicious_connections / total_connections
            if suspicious_ratio > 0.5:
                risk_score += 50
            elif suspicious_ratio > 0.2:
                risk_score += 30
            elif suspicious_ratio > 0.1:
                risk_score += 15
        
        # Risk from absolute number of suspicious connections
        if suspicious_connections > 50:
            risk_score += 30
        elif suspicious_connections > 20:
            risk_score += 20
        elif suspicious_connections > 5:
            risk_score += 10
        
        # Risk from unique suspicious domains
        if unique_domains > 20:
            risk_score += 20
        elif unique_domains > 10:
            risk_score += 15
        elif unique_domains > 5:
            risk_score += 10
        
        return min(risk_score, 100)
    
    def _generate_network_recommendations(self, 
                                        privacy_risk: int,
                                        process_analysis: Dict[str, Any],
                                        category_analysis: Dict[str, int]) -> List[str]:
        """Generate network privacy recommendations"""
        recommendations = []
        
        if privacy_risk > 70:
            recommendations.append("🚨 HIGH NETWORK PRIVACY RISK: Immediate action required")
        elif privacy_risk > 40:
            recommendations.append("⚠️ MODERATE NETWORK PRIVACY RISK: Consider privacy measures")
        else:
            recommendations.append("✅ LOW NETWORK PRIVACY RISK: Good network privacy posture")
        
        # Process-specific recommendations
        high_risk_processes = [
            process for process, data in process_analysis.items()
            if data.get('risk_level') == 'high'
        ]
        
        if high_risk_processes:
            recommendations.append(f"🔍 Review network activity for: {', '.join(high_risk_processes)}")
        
        # Category-specific recommendations
        if 'development_tools' in category_analysis:
            recommendations.append("🛠️ Development tools are sending telemetry - consider disabling")
        
        if 'google_analytics' in category_analysis:
            recommendations.append("📊 Google Analytics tracking detected - use ad blockers")
        
        if 'microsoft_telemetry' in category_analysis:
            recommendations.append("🪟 Microsoft telemetry detected - review Windows privacy settings")
        
        # General recommendations
        recommendations.extend([
            "🛡️ Use a privacy-focused DNS resolver (e.g., 1.1.1.1, 9.9.9.9)",
            "🚫 Consider using a Pi-hole or similar DNS blocking solution",
            "🔒 Use a VPN for additional privacy protection",
            "📱 Regular network monitoring is recommended",
            "🌐 Review application privacy settings regularly"
        ])
        
        return recommendations
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """Get current monitoring status"""
        return {
            'monitoring_active': self.monitoring,
            'session_stats': self._get_current_stats() if self.monitoring else {},
            'last_update': datetime.now().isoformat()
        }
    
    def export_connection_log(self, file_path: Optional[str] = None) -> str:
        """Export connection log to file"""
        if not file_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_path = f"network_monitoring_{timestamp}.json"
        
        export_data = {
            'export_metadata': {
                'timestamp': datetime.now().isoformat(),
                'total_connections': len(self.connection_log),
                'monitoring_session': self._calculate_session_duration()
            },
            'connection_log': self.connection_log,
            'suspicious_domains': list(self.suspicious_domains),
            'process_summary': dict(self.process_connections)
        }
        
        with open(file_path, 'w') as f:
            json.dump(export_data, f, indent=2, default=str)
        
        success(f"Connection log exported to: {file_path}")
        return file_path
