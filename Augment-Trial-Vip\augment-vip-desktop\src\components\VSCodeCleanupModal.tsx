import React, { useState, useEffect } from 'react';
import { 
  X, 
  Loader, 
  Database, 
  AlertTriangle, 
  CheckCircle, 
  FileText,
  Trash2,
  Shield,
  Info
} from 'lucide-react';

interface VSCodeCleanupPreview {
  database_path: string;
  database_exists: boolean;
  database_size: number;
  augment_entries: Array<{
    key: string;
    value: string;
  }>;
  total_entries: number;
  entries_to_remove: number;
  backup_location: string;
  safe_to_clean: boolean;
  warnings: string[];
  recommendations: string[];
}

interface VSCodeCleanupModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

export const VSCodeCleanupModal: React.FC<VSCodeCleanupModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
}) => {
  const [preview, setPreview] = useState<VSCodeCleanupPreview | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [scanProgress, setScanProgress] = useState(0);
  const [scanStep, setScanStep] = useState("");

  useEffect(() => {
    if (isOpen && window.electronAPI) {
      startPreviewScan();
    }
  }, [isOpen]);

  const startPreviewScan = async () => {
    if (!window.electronAPI) return;

    setIsScanning(true);
    setScanProgress(0);
    setScanStep("Analyzing VS Code database...");

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setScanProgress(prev => {
          if (prev >= 90) return prev;
          return prev + Math.random() * 20;
        });
      }, 200);

      const result = await window.electronAPI.executePythonCommand('preview-clean', ['--json']);
      clearInterval(progressInterval);
      setScanProgress(100);

      if (result.success && result.stdout) {
        const previewData = JSON.parse(result.stdout.trim());
        setPreview(previewData);
        setScanStep("Preview completed!");
      } else {
        throw new Error(result.stderr || "Preview failed");
      }
    } catch (error) {
      console.error("VS Code cleanup preview failed:", error);
      setScanStep(`Preview failed: ${error}`);
    } finally {
      setIsScanning(false);
    }
  };

  const formatSize = (bytes: number) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-slate-800 rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-slate-600">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <Database className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                VS Code Cleanup Preview
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Review what will be cleaned before proceeding
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden flex flex-col">
          {isScanning ? (
            /* Scanning in Progress */
            <div className="flex-1 flex items-center justify-center p-8">
              <div className="text-center max-w-md">
                <Loader className="w-16 h-16 text-blue-600 dark:text-blue-400 mx-auto mb-4 animate-spin" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Scanning VS Code Database
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  {scanStep}
                </p>
                
                <div className="w-full bg-gray-200 dark:bg-slate-700 rounded-full h-2 mb-2">
                  <div
                    className="h-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full transition-all duration-300"
                    style={{ width: `${scanProgress}%` }}
                  />
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {Math.round(scanProgress)}% complete
                </div>
              </div>
            </div>
          ) : preview ? (
            /* Preview Results */
            <div className="flex-1 overflow-y-auto p-6">
              {/* Safety Status */}
              <div className={`p-4 rounded-xl border-2 mb-6 ${
                preview.safe_to_clean 
                  ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-700'
                  : 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-700'
              }`}>
                <div className="flex items-center space-x-3">
                  {preview.safe_to_clean ? (
                    <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
                  ) : (
                    <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
                  )}
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white">
                      {preview.safe_to_clean ? 'Safe to Clean' : 'Cannot Clean Safely'}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {preview.safe_to_clean 
                        ? 'The cleanup operation can proceed safely'
                        : 'Issues detected that prevent safe cleanup'
                      }
                    </p>
                  </div>
                </div>
              </div>

              {/* Database Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div className="bg-gray-50 dark:bg-slate-700 rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                    <FileText className="w-4 h-4 mr-2" />
                    Database Information
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Location:</span>
                      <span className="text-gray-900 dark:text-white font-mono text-xs">
                        {preview.database_path.split('/').pop() || preview.database_path.split('\\').pop()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Size:</span>
                      <span className="text-gray-900 dark:text-white">
                        {formatSize(preview.database_size)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Total Entries:</span>
                      <span className="text-gray-900 dark:text-white">
                        {preview.total_entries.toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-slate-700 rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                    <Trash2 className="w-4 h-4 mr-2" />
                    Cleanup Impact
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Entries to Remove:</span>
                      <span className="text-red-600 dark:text-red-400 font-semibold">
                        {preview.entries_to_remove}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Backup Location:</span>
                      <span className="text-gray-900 dark:text-white font-mono text-xs">
                        {preview.backup_location.split('/').pop() || preview.backup_location.split('\\').pop()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Entries to be Removed */}
              {preview.augment_entries.length > 0 && (
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                    <Info className="w-4 h-4 mr-2" />
                    Entries to be Removed
                  </h4>
                  <div className="bg-gray-50 dark:bg-slate-700 rounded-lg p-4 max-h-48 overflow-y-auto">
                    <div className="space-y-2">
                      {preview.augment_entries.map((entry, index) => (
                        <div key={index} className="text-sm">
                          <div className="font-mono text-xs text-blue-600 dark:text-blue-400">
                            {entry.key}
                          </div>
                          {entry.value && (
                            <div className="text-gray-600 dark:text-gray-400 ml-2 truncate">
                              {entry.value}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Warnings */}
              {preview.warnings.length > 0 && (
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                    <AlertTriangle className="w-4 h-4 mr-2 text-yellow-500" />
                    Warnings
                  </h4>
                  <div className="space-y-2">
                    {preview.warnings.map((warning, index) => (
                      <div key={index} className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-3">
                        <p className="text-sm text-yellow-800 dark:text-yellow-200">{warning}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Recommendations */}
              {preview.recommendations.length > 0 && (
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                    <Shield className="w-4 h-4 mr-2 text-green-500" />
                    Recommendations
                  </h4>
                  <div className="space-y-2">
                    {preview.recommendations.map((recommendation, index) => (
                      <div key={index} className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-3">
                        <p className="text-sm text-blue-800 dark:text-blue-200">{recommendation}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            /* Error State */
            <div className="flex-1 flex items-center justify-center p-8">
              <div className="text-center">
                <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Preview Failed
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Unable to generate preview. Please try again.
                </p>
                <button
                  onClick={startPreviewScan}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Retry Preview
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        {preview && !isScanning && (
          <div className="p-6 border-t border-gray-200 dark:border-slate-600 flex justify-between">
            <button
              onClick={onClose}
              className="px-6 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
            >
              Cancel
            </button>
            <div className="flex space-x-3">
              <button
                onClick={startPreviewScan}
                className="px-6 py-2 bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-slate-600 transition-colors"
              >
                Refresh Preview
              </button>
              <button
                onClick={handleConfirm}
                disabled={!preview.safe_to_clean}
                className={`px-6 py-2 rounded-lg font-medium transition-colors ${
                  preview.safe_to_clean
                    ? 'bg-red-600 text-white hover:bg-red-700'
                    : 'bg-gray-300 dark:bg-slate-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                }`}
              >
                Proceed with Cleanup
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
