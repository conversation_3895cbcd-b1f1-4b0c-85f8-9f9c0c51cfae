import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, Info, Activity, Database, HardDrive, Wifi, Shield, Clock, Loader } from 'lucide-react';

interface SystemState {
  timestamp: Date;
  platform: string;
  processes: {
    running: Array<{ name: string; pid: number; command: string }>;
    critical: Array<{ name: string; pid: number; command: string }>;
    databases: Array<{ name: string; pid: number; command: string }>;
  };
  connections: {
    database: any[];
    network: any[];
  };
  filesystem: {
    locks: any[];
    permissions: Array<{ path: string; readable: boolean; writable: boolean; error?: string }>;
    diskSpace: {
      total: number;
      free: number;
      used: number;
      freePercentage: number;
    };
  };
  vscode: {
    running: boolean;
    instances: Array<{ name: string; pid: number; command: string }>;
    workspaces: any[];
  };
  databases: {
    postgresql: { running: boolean; connections: number };
    mysql: { running: boolean; connections: number };
    sqlite: { openFiles: any[] };
  };
  risks: Array<{
    level: string;
    category: string;
    message: string;
    impact: string;
    [key: string]: any;
  }>;
  recommendations: Array<{
    action: string;
    reason: string;
    priority: string;
  }>;
  safeToOperate: boolean;
}

interface SystemStateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onProceed: () => void;
  operationType: string;
}

export const SystemStateModal: React.FC<SystemStateModalProps> = ({ 
  isOpen, 
  onClose, 
  onProceed, 
  operationType 
}) => {
  const [systemState, setSystemState] = useState<SystemState | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisProgress, setAnalysisProgress] = useState(0);
  const [analysisStep, setAnalysisStep] = useState('');

  useEffect(() => {
    if (window.electronAPI && isOpen) {
      // Set up progress listener
      window.electronAPI.onStateAnalysisProgress((data) => {
        setAnalysisProgress(data.progress);
        setAnalysisStep(data.step);
      });

      // Start analysis when modal opens
      analyzeSystemState();

      return () => {
        window.electronAPI.removeAllListeners('state-analysis-progress');
      };
    }
  }, [isOpen]);

  const analyzeSystemState = async () => {
    if (!window.electronAPI) return;

    setIsAnalyzing(true);
    setAnalysisProgress(0);
    setAnalysisStep('Initializing system analysis...');

    try {
      const result = await window.electronAPI.analyzeSystemState();
      if (result.success && result.data) {
        setSystemState(result.data);
      } else {
        console.error('System state analysis failed:', result.error);
      }
    } catch (error) {
      console.error('System state analysis error:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getRiskIcon = (level: string) => {
    switch (level) {
      case 'critical':
      case 'high':
        return <AlertTriangle className="w-5 h-5 text-red-500" />;
      case 'medium':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case 'low':
        return <Info className="w-5 h-5 text-blue-500" />;
      default:
        return <Info className="w-5 h-5 text-gray-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'high':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 backdrop-blur-sm">
      <div className="bg-white dark:bg-slate-800 rounded-2xl w-full max-w-4xl max-h-[90vh] mx-4 shadow-2xl border border-gray-200 dark:border-slate-700 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-slate-700">
          <div className="flex items-center space-x-3">
            <Shield className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            <div>
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                System State Analysis
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Checking system safety before {operationType}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden flex flex-col">
          {isAnalyzing ? (
            /* Analysis in Progress */
            <div className="flex-1 flex items-center justify-center p-8">
              <div className="text-center max-w-md">
                <Loader className="w-16 h-16 text-blue-600 dark:text-blue-400 mx-auto mb-4 animate-spin" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Analyzing System State
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  {analysisStep}
                </p>
                
                <div className="w-full bg-gray-200 dark:bg-slate-700 rounded-full h-2 mb-2">
                  <div
                    className="h-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full transition-all duration-300"
                    style={{ width: `${analysisProgress}%` }}
                  />
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {analysisProgress}% complete
                </div>
              </div>
            </div>
          ) : systemState ? (
            /* Analysis Results */
            <div className="flex-1 overflow-y-auto p-6">
              {/* Safety Status */}
              <div className={`p-4 rounded-xl border-2 mb-6 ${
                systemState.safeToOperate 
                  ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-700'
                  : 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-700'
              }`}>
                <div className="flex items-center space-x-3">
                  {systemState.safeToOperate ? (
                    <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
                  ) : (
                    <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
                  )}
                  <div>
                    <h3 className={`font-semibold ${
                      systemState.safeToOperate 
                        ? 'text-green-800 dark:text-green-200'
                        : 'text-red-800 dark:text-red-200'
                    }`}>
                      {systemState.safeToOperate ? 'System Ready' : 'Risks Detected'}
                    </h3>
                    <p className={`text-sm ${
                      systemState.safeToOperate 
                        ? 'text-green-600 dark:text-green-400'
                        : 'text-red-600 dark:text-red-400'
                    }`}>
                      {systemState.safeToOperate 
                        ? 'It is safe to proceed with the operation'
                        : 'Please review the risks and recommendations below'
                      }
                    </p>
                  </div>
                </div>
              </div>

              {/* System Overview */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                {/* VS Code Status */}
                <div className="bg-gray-50 dark:bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Activity className="w-4 h-4 text-blue-600" />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">VS Code</span>
                  </div>
                  <div className={`text-lg font-bold ${
                    systemState.vscode.running ? 'text-red-600' : 'text-green-600'
                  }`}>
                    {systemState.vscode.running ? 'Running' : 'Stopped'}
                  </div>
                  {systemState.vscode.instances.length > 0 && (
                    <div className="text-xs text-gray-500">
                      {systemState.vscode.instances.length} instance(s)
                    </div>
                  )}
                </div>

                {/* Database Status */}
                <div className="bg-gray-50 dark:bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Database className="w-4 h-4 text-purple-600" />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Databases</span>
                  </div>
                  <div className="text-lg font-bold text-gray-900 dark:text-white">
                    {systemState.processes.databases.length}
                  </div>
                  <div className="text-xs text-gray-500">
                    Active processes
                  </div>
                </div>

                {/* Disk Space */}
                <div className="bg-gray-50 dark:bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <HardDrive className="w-4 h-4 text-green-600" />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Disk Space</span>
                  </div>
                  <div className="text-lg font-bold text-gray-900 dark:text-white">
                    {systemState.filesystem.diskSpace.freePercentage.toFixed(1)}%
                  </div>
                  <div className="text-xs text-gray-500">
                    {formatBytes(systemState.filesystem.diskSpace.free)} free
                  </div>
                </div>

                {/* Network */}
                <div className="bg-gray-50 dark:bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Wifi className="w-4 h-4 text-orange-600" />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Connections</span>
                  </div>
                  <div className="text-lg font-bold text-gray-900 dark:text-white">
                    {systemState.connections.database.length + systemState.connections.network.length}
                  </div>
                  <div className="text-xs text-gray-500">
                    Active connections
                  </div>
                </div>
              </div>

              {/* Risks */}
              {systemState.risks.length > 0 && (
                <div className="mb-6">
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                    <AlertTriangle className="w-5 h-5 mr-2 text-red-500" />
                    Detected Risks ({systemState.risks.length})
                  </h4>
                  <div className="space-y-3">
                    {systemState.risks.map((risk, index) => (
                      <div key={index} className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-4">
                        <div className="flex items-start space-x-3">
                          {getRiskIcon(risk.level)}
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <span className="font-medium text-red-800 dark:text-red-200">
                                {risk.message}
                              </span>
                              <span className={`px-2 py-1 rounded text-xs font-medium ${getPriorityColor(risk.level)}`}>
                                {risk.level.toUpperCase()}
                              </span>
                            </div>
                            <p className="text-sm text-red-600 dark:text-red-400">
                              Impact: {risk.impact}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Recommendations */}
              {systemState.recommendations.length > 0 && (
                <div className="mb-6">
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                    <Info className="w-5 h-5 mr-2 text-blue-500" />
                    Recommendations ({systemState.recommendations.length})
                  </h4>
                  <div className="space-y-3">
                    {systemState.recommendations.map((rec, index) => (
                      <div key={index} className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
                        <div className="flex items-start space-x-3">
                          <Info className="w-4 h-4 text-blue-600 mt-0.5" />
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <span className="font-medium text-blue-800 dark:text-blue-200">
                                {rec.action}
                              </span>
                              <span className={`px-2 py-1 rounded text-xs font-medium ${getPriorityColor(rec.priority)}`}>
                                {rec.priority.toUpperCase()}
                              </span>
                            </div>
                            <p className="text-sm text-blue-600 dark:text-blue-400">
                              {rec.reason}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            /* Error State */
            <div className="flex-1 flex items-center justify-center p-8">
              <div className="text-center">
                <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Analysis Failed
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Unable to analyze system state. Please try again.
                </p>
                <button
                  onClick={analyzeSystemState}
                  className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                >
                  Retry Analysis
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Actions */}
        {systemState && !isAnalyzing && (
          <div className="p-6 border-t border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4 text-gray-500" />
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Analysis completed at {new Date(systemState.timestamp).toLocaleTimeString()}
                </span>
              </div>
              <div className="flex space-x-3">
                <button
                  onClick={analyzeSystemState}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-white dark:bg-slate-600 border border-gray-300 dark:border-slate-500 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-500 transition-colors"
                >
                  Refresh Analysis
                </button>
                <button
                  onClick={onClose}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-white dark:bg-slate-600 border border-gray-300 dark:border-slate-500 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-500 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={onProceed}
                  disabled={!systemState.safeToOperate}
                  className={`px-6 py-2 rounded-lg font-medium transition-all duration-200 ${
                    systemState.safeToOperate
                      ? 'bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700 shadow-lg hover:shadow-xl transform hover:scale-105'
                      : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                  }`}
                >
                  {systemState.safeToOperate ? 'Proceed Safely' : 'Cannot Proceed'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
