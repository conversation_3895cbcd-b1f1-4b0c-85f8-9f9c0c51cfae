"""
Modern Database Management with SQLAlchemy 2.0
Async database operations with connection pooling and migrations
"""

import asyncio
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional

from sqlalchemy import MetaData, event
from sqlalchemy.ext.asyncio import (
    AsyncEngine,
    AsyncSession,
    async_sessionmaker,
    create_async_engine,
)
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
from sqlalchemy.pool import StaticPool
import structlog

from .config import get_settings

logger = structlog.get_logger(__name__)
settings = get_settings()

# Global database engine and session factory
engine: Optional[AsyncEngine] = None
async_session_factory: Optional[async_sessionmaker[AsyncSession]] = None


class Base(DeclarativeBase):
    """
    Base class for all database models
    """
    metadata = MetaData(
        naming_convention={
            "ix": "ix_%(column_0_label)s",
            "uq": "uq_%(table_name)s_%(column_0_name)s",
            "ck": "ck_%(table_name)s_%(constraint_name)s",
            "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
            "pk": "pk_%(table_name)s",
        }
    )


def create_engine() -> AsyncEngine:
    """
    Create async database engine with proper configuration
    """
    connect_args = {}
    
    # SQLite-specific configuration
    if "sqlite" in settings.DATABASE_URL:
        connect_args.update({
            "check_same_thread": False,
        })
        # Use StaticPool for SQLite to maintain connections
        engine = create_async_engine(
            settings.DATABASE_URL,
            echo=settings.DATABASE_ECHO,
            poolclass=StaticPool,
            connect_args=connect_args,
        )
    else:
        # PostgreSQL/MySQL configuration
        engine = create_async_engine(
            settings.DATABASE_URL,
            echo=settings.DATABASE_ECHO,
            pool_size=settings.DATABASE_POOL_SIZE,
            max_overflow=settings.DATABASE_MAX_OVERFLOW,
            connect_args=connect_args,
        )
    
    # Add connection event listeners
    @event.listens_for(engine.sync_engine, "connect")
    def set_sqlite_pragma(dbapi_connection, connection_record):
        """Set SQLite pragmas for better performance and reliability"""
        if "sqlite" in settings.DATABASE_URL:
            cursor = dbapi_connection.cursor()
            # Enable foreign key constraints
            cursor.execute("PRAGMA foreign_keys=ON")
            # Set WAL mode for better concurrency
            cursor.execute("PRAGMA journal_mode=WAL")
            # Set synchronous mode for better performance
            cursor.execute("PRAGMA synchronous=NORMAL")
            # Set cache size (negative value = KB)
            cursor.execute("PRAGMA cache_size=-64000")  # 64MB
            cursor.close()
    
    return engine


async def init_db() -> None:
    """
    Initialize database connection and create tables
    """
    global engine, async_session_factory
    
    logger.info("Initializing database connection", url=settings.DATABASE_URL)
    
    # Create engine
    engine = create_engine()
    
    # Create session factory
    async_session_factory = async_sessionmaker(
        engine,
        class_=AsyncSession,
        expire_on_commit=False,
    )
    
    # Test connection
    try:
        async with engine.begin() as conn:
            # Import all models to ensure they're registered
            from ..models.user import User  # noqa: F401
            from ..models.operation import Operation, OperationLog  # noqa: F401
            from ..models.system import SystemState, SystemMetric  # noqa: F401

            # Create all tables
            await conn.run_sync(Base.metadata.create_all)
            
        logger.info("Database initialized successfully")
        
    except Exception as e:
        logger.error("Failed to initialize database", error=str(e))
        raise


async def close_db() -> None:
    """
    Close database connections
    """
    global engine
    
    if engine:
        logger.info("Closing database connections")
        await engine.dispose()
        engine = None
        logger.info("Database connections closed")


@asynccontextmanager
async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Get async database session with automatic cleanup
    
    Usage:
        async with get_db_session() as session:
            # Use session here
            result = await session.execute(select(User))
    """
    if not async_session_factory:
        raise RuntimeError("Database not initialized. Call init_db() first.")
    
    async with async_session_factory() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency for FastAPI to get database session
    
    Usage in FastAPI endpoints:
        @app.get("/users")
        async def get_users(db: AsyncSession = Depends(get_db)):
            result = await db.execute(select(User))
            return result.scalars().all()
    """
    async with get_db_session() as session:
        yield session


class DatabaseHealthCheck:
    """
    Database health check utilities
    """
    
    @staticmethod
    async def check_connection() -> bool:
        """
        Check if database connection is healthy
        """
        try:
            from sqlalchemy import text
            async with get_db_session() as session:
                await session.execute(text("SELECT 1"))
                return True
        except Exception as e:
            logger.error("Database health check failed", error=str(e))
            return False
    
    @staticmethod
    async def get_connection_info() -> dict:
        """
        Get database connection information
        """
        if not engine:
            return {"status": "not_initialized"}

        pool = engine.pool
        info = {
            "status": "connected",
            "url": str(engine.url).replace(engine.url.password or "", "***"),
        }

        # Add pool info if available (not for StaticPool)
        try:
            info.update({
                "pool_size": pool.size(),
                "checked_in": pool.checkedin(),
                "checked_out": pool.checkedout(),
                "overflow": pool.overflow(),
            })
        except AttributeError:
            # StaticPool doesn't have these methods
            info["pool_type"] = "StaticPool"

        return info


# Export commonly used items
__all__ = [
    "Base",
    "init_db",
    "close_db",
    "get_db_session",
    "get_db",
    "DatabaseHealthCheck",
]
