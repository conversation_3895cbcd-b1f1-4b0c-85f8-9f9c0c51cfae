{"version": 3, "file": "AppXOptions.js", "sourceRoot": "", "sources": ["../../src/options/AppXOptions.ts"], "names": [], "mappings": "", "sourcesContent": ["import { TargetSpecificOptions } from \"../core\"\n\nexport interface AppXOptions extends TargetSpecificOptions {\n  /**\n   * The application id. Defaults to `identityName`. This string contains alpha-numeric fields separated by periods. Each field must begin with an ASCII alphabetic character.\n   */\n  readonly applicationId?: string\n\n  /**\n   * The background color of the app tile. See [Visual Elements](https://msdn.microsoft.com/en-us/library/windows/apps/br211471.aspx).\n   * @default #464646\n   */\n  readonly backgroundColor?: string | null\n\n  /**\n   * A friendly name that can be displayed to users. Corresponds to [Properties.DisplayName](https://msdn.microsoft.com/en-us/library/windows/apps/br211432.aspx).\n   * Defaults to the application product name.\n   */\n  readonly displayName?: string | null\n\n  /**\n   * The name. Corresponds to [Identity.Name](https://msdn.microsoft.com/en-us/library/windows/apps/br211441.aspx). Defaults to the [application name](./configuration.md#metadata).\n   */\n  readonly identityName?: string | null\n\n  /**\n   * The Windows Store publisher. Not used if AppX is build for testing. See [AppX Package Code Signing](#appx-package-code-signing) below.\n   */\n  readonly publisher?: string | null\n\n  /**\n   * A friendly name for the publisher that can be displayed to users. Corresponds to [Properties.PublisherDisplayName](https://msdn.microsoft.com/en-us/library/windows/apps/br211460.aspx).\n   * Defaults to company name from the application metadata.\n   */\n  readonly publisherDisplayName?: string | null\n\n  /**\n   * The list of [supported languages](https://docs.microsoft.com/en-us/windows/uwp/globalizing/manage-language-and-region#specify-the-supported-languages-in-the-apps-manifest) that will be listed in the Windows Store.\n   * The first entry (index 0) will be the default language.\n   * Defaults to en-US if omitted.\n   */\n  readonly languages?: Array<string> | string | null\n\n  /**\n   * Whether to add auto launch extension. Defaults to `true` if [electron-winstore-auto-launch](https://github.com/felixrieseberg/electron-winstore-auto-launch) in the dependencies.\n   */\n  readonly addAutoLaunchExtension?: boolean\n\n  /**\n   * Relative path to custom extensions xml to be included in an `appmanifest.xml`.\n   */\n  readonly customExtensionsPath?: string\n\n  /**\n   * (Advanced Option) Relative path to custom `appmanifest.xml` (file name doesn't matter, it'll be renamed) located in build resources directory.\n   * Supports the following template macros:\n   *\n   * - ${publisher}\n   * - ${publisherDisplayName}\n   * - ${version}\n   * - ${applicationId}\n   * - ${identityName}\n   * - ${executable}\n   * - ${displayName}\n   * - ${description}\n   * - ${backgroundColor}\n   * - ${logo}\n   * - ${square150x150Logo}\n   * - ${square44x44Logo}\n   * - ${lockScreen}\n   * - ${defaultTile}\n   * - ${splashScreen}\n   * - ${arch}\n   * - ${resourceLanguages}\n   * - ${extensions}\n   * - ${minVersion}\n   * - ${maxVersionTested}\n   */\n  readonly customManifestPath?: string\n\n  /**\n   * Whether to overlay the app's name on top of tile images on the Start screen. Defaults to `false`. (https://docs.microsoft.com/en-us/uwp/schemas/appxpackage/uapmanifestschema/element-uap-shownameontiles) in the dependencies.\n   * @default false\n   */\n  readonly showNameOnTiles?: boolean\n\n  /**\n   * @private\n   * @default false\n   */\n  readonly electronUpdaterAware?: boolean\n\n  /**\n   * Whether to set build number. See https://github.com/electron-userland/electron-builder/issues/3875\n   * @default false\n   */\n  readonly setBuildNumber?: boolean\n\n  /**\n   * Set the MinVersion field in the appx manifest.xml\n   * @default arch === Arch.arm64 ? \"10.0.16299.0\" : \"10.0.14316.0\"\n   */\n  readonly minVersion?: string | null\n\n  /**\n   * Set the `MaxVersionTested` field in the appx manifest.xml\n   * @default arch === Arch.arm64 ? \"10.0.16299.0\" : \"10.0.14316.0\"\n   */\n  readonly maxVersionTested?: string | null\n\n  /** @private */\n  readonly makeappxArgs?: Array<string> | null\n}\n"]}