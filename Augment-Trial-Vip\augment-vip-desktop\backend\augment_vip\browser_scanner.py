"""
Safe Browser Telemetry Scanner
Read-only analysis of browser tracking data and fingerprinting
"""

import os
import json
import sqlite3
import platform
from pathlib import Path
from typing import List, Dict, Any, Optional, Set
from datetime import datetime
import re

from .utils import info, success, error, warning

class BrowserTelemetryScanner:
    """Safe browser telemetry and tracking data scanner"""
    
    def __init__(self):
        self.platform = platform.system().lower()
        self.scan_results = {}
        
    def scan_all_browsers(self, browsers: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Scan all supported browsers for telemetry data
        
        Args:
            browsers: List of browsers to scan (default: all supported)
        
        Returns:
            Comprehensive scan results
        """
        if browsers is None:
            browsers = ['chrome', 'firefox', 'edge', 'brave', 'opera']
        
        scan_results = {
            'scan_metadata': {
                'timestamp': datetime.now().isoformat(),
                'platform': self.platform,
                'browsers_scanned': browsers,
                'scan_mode': 'read_only_safe'
            },
            'browser_results': {},
            'summary': {
                'total_browsers_found': 0,
                'total_tracking_entries': 0,
                'total_fingerprint_data': 0,
                'privacy_risk_score': 0
            },
            'recommendations': []
        }
        
        for browser in browsers:
            try:
                browser_result = self._scan_browser(browser)
                if browser_result['found']:
                    scan_results['browser_results'][browser] = browser_result
                    scan_results['summary']['total_browsers_found'] += 1
                    scan_results['summary']['total_tracking_entries'] += browser_result.get('tracking_entries', 0)
                    scan_results['summary']['total_fingerprint_data'] += browser_result.get('fingerprint_entries', 0)
                
            except Exception as e:
                warning(f"Error scanning {browser}: {e}")
                scan_results['browser_results'][browser] = {
                    'found': False,
                    'error': str(e)
                }
        
        # Calculate privacy risk score
        scan_results['summary']['privacy_risk_score'] = self._calculate_privacy_risk(scan_results)
        
        # Generate recommendations
        scan_results['recommendations'] = self._generate_privacy_recommendations(scan_results)
        
        return scan_results
    
    def _scan_browser(self, browser: str) -> Dict[str, Any]:
        """Scan a specific browser for telemetry data"""
        browser_paths = self._get_browser_paths(browser)
        
        result = {
            'browser': browser,
            'found': False,
            'installation_paths': [],
            'profile_paths': [],
            'databases_found': [],
            'tracking_entries': 0,
            'fingerprint_entries': 0,
            'telemetry_domains': set(),
            'privacy_violations': [],
            'last_activity': None
        }
        
        for path_info in browser_paths:
            if path_info['path'].exists():
                result['found'] = True
                result['installation_paths'].append(str(path_info['path']))
                
                # Scan for profiles
                profiles = self._find_browser_profiles(path_info['path'], browser)
                result['profile_paths'].extend(profiles)
                
                # Scan each profile for telemetry data
                for profile_path in profiles:
                    profile_data = self._scan_browser_profile(Path(profile_path), browser)
                    
                    result['tracking_entries'] += profile_data.get('tracking_entries', 0)
                    result['fingerprint_entries'] += profile_data.get('fingerprint_entries', 0)
                    result['telemetry_domains'].update(profile_data.get('telemetry_domains', set()))
                    result['privacy_violations'].extend(profile_data.get('privacy_violations', []))
                    result['databases_found'].extend(profile_data.get('databases', []))
                    
                    if profile_data.get('last_activity'):
                        if not result['last_activity'] or profile_data['last_activity'] > result['last_activity']:
                            result['last_activity'] = profile_data['last_activity']
        
        # Convert sets to lists for JSON serialization
        result['telemetry_domains'] = list(result['telemetry_domains'])
        
        return result
    
    def _get_browser_paths(self, browser: str) -> List[Dict[str, Any]]:
        """Get potential installation paths for a browser"""
        paths = []
        
        if self.platform == 'windows':
            base_paths = {
                'chrome': [
                    Path(os.environ.get('LOCALAPPDATA', '')) / 'Google' / 'Chrome',
                    Path(os.environ.get('PROGRAMFILES', '')) / 'Google' / 'Chrome',
                    Path(os.environ.get('PROGRAMFILES(X86)', '')) / 'Google' / 'Chrome'
                ],
                'firefox': [
                    Path(os.environ.get('APPDATA', '')) / 'Mozilla' / 'Firefox',
                    Path(os.environ.get('PROGRAMFILES', '')) / 'Mozilla Firefox',
                    Path(os.environ.get('PROGRAMFILES(X86)', '')) / 'Mozilla Firefox'
                ],
                'edge': [
                    Path(os.environ.get('LOCALAPPDATA', '')) / 'Microsoft' / 'Edge',
                    Path(os.environ.get('PROGRAMFILES', '')) / 'Microsoft' / 'Edge',
                    Path(os.environ.get('PROGRAMFILES(X86)', '')) / 'Microsoft' / 'Edge'
                ],
                'brave': [
                    Path(os.environ.get('LOCALAPPDATA', '')) / 'BraveSoftware' / 'Brave-Browser',
                    Path(os.environ.get('PROGRAMFILES', '')) / 'BraveSoftware' / 'Brave-Browser'
                ],
                'opera': [
                    Path(os.environ.get('APPDATA', '')) / 'Opera Software' / 'Opera Stable',
                    Path(os.environ.get('PROGRAMFILES', '')) / 'Opera'
                ]
            }
        elif self.platform == 'darwin':  # macOS
            home = Path.home()
            base_paths = {
                'chrome': [
                    home / 'Library' / 'Application Support' / 'Google' / 'Chrome',
                    Path('/Applications/Google Chrome.app')
                ],
                'firefox': [
                    home / 'Library' / 'Application Support' / 'Firefox',
                    Path('/Applications/Firefox.app')
                ],
                'edge': [
                    home / 'Library' / 'Application Support' / 'Microsoft Edge',
                    Path('/Applications/Microsoft Edge.app')
                ],
                'brave': [
                    home / 'Library' / 'Application Support' / 'BraveSoftware' / 'Brave-Browser',
                    Path('/Applications/Brave Browser.app')
                ],
                'opera': [
                    home / 'Library' / 'Application Support' / 'com.operasoftware.Opera',
                    Path('/Applications/Opera.app')
                ]
            }
        else:  # Linux
            home = Path.home()
            base_paths = {
                'chrome': [
                    home / '.config' / 'google-chrome',
                    home / '.config' / 'chromium'
                ],
                'firefox': [
                    home / '.mozilla' / 'firefox'
                ],
                'brave': [
                    home / '.config' / 'BraveSoftware' / 'Brave-Browser'
                ],
                'opera': [
                    home / '.config' / 'opera'
                ]
            }
        
        browser_paths = base_paths.get(browser, [])
        return [{'path': path, 'type': 'user_data' if 'AppData' in str(path) or 'Library' in str(path) or '.config' in str(path) else 'installation'} for path in browser_paths]
    
    def _find_browser_profiles(self, browser_path: Path, browser: str) -> List[str]:
        """Find browser profiles within the browser directory"""
        profiles = []
        
        try:
            if browser in ['chrome', 'edge', 'brave']:
                # Chromium-based browsers
                user_data_path = browser_path / 'User Data'
                if user_data_path.exists():
                    for item in user_data_path.iterdir():
                        if item.is_dir() and (item.name == 'Default' or item.name.startswith('Profile ')):
                            profiles.append(str(item))
            
            elif browser == 'firefox':
                # Firefox profiles
                profiles_ini = browser_path / 'profiles.ini'
                if profiles_ini.exists():
                    with open(profiles_ini, 'r', encoding='utf-8') as f:
                        content = f.read()
                        # Parse profiles.ini to find profile paths
                        for line in content.split('\n'):
                            if line.startswith('Path='):
                                profile_path = line.split('=', 1)[1]
                                full_path = browser_path / profile_path
                                if full_path.exists():
                                    profiles.append(str(full_path))
            
            elif browser == 'opera':
                # Opera profiles
                if browser_path.exists():
                    profiles.append(str(browser_path))
        
        except Exception as e:
            warning(f"Error finding profiles for {browser}: {e}")
        
        return profiles
    
    def _scan_browser_profile(self, profile_path: Path, browser: str) -> Dict[str, Any]:
        """Scan a specific browser profile for telemetry data"""
        result = {
            'profile_path': str(profile_path),
            'tracking_entries': 0,
            'fingerprint_entries': 0,
            'telemetry_domains': set(),
            'privacy_violations': [],
            'databases': [],
            'last_activity': None
        }
        
        try:
            # Scan for database files
            db_files = self._find_database_files(profile_path)
            result['databases'] = [str(db) for db in db_files]
            
            # Analyze each database
            for db_file in db_files:
                db_analysis = self._analyze_browser_database(db_file, browser)
                result['tracking_entries'] += db_analysis.get('tracking_entries', 0)
                result['fingerprint_entries'] += db_analysis.get('fingerprint_entries', 0)
                result['telemetry_domains'].update(db_analysis.get('telemetry_domains', set()))
                result['privacy_violations'].extend(db_analysis.get('privacy_violations', []))
                
                if db_analysis.get('last_activity'):
                    if not result['last_activity'] or db_analysis['last_activity'] > result['last_activity']:
                        result['last_activity'] = db_analysis['last_activity']
            
            # Scan for preference files
            prefs_analysis = self._analyze_browser_preferences(profile_path, browser)
            result['privacy_violations'].extend(prefs_analysis.get('privacy_violations', []))
        
        except Exception as e:
            warning(f"Error scanning profile {profile_path}: {e}")
            result['error'] = str(e)
        
        return result
    
    def _find_database_files(self, profile_path: Path) -> List[Path]:
        """Find database files in a browser profile"""
        db_files = []
        
        try:
            # Common database file patterns
            db_patterns = ['*.db', '*.sqlite', '*.sqlite3']
            
            for pattern in db_patterns:
                db_files.extend(profile_path.glob(pattern))
                # Also check subdirectories
                for subdir in profile_path.iterdir():
                    if subdir.is_dir():
                        db_files.extend(subdir.glob(pattern))
        
        except Exception as e:
            warning(f"Error finding database files: {e}")
        
        return db_files
    
    def _analyze_browser_database(self, db_file: Path, browser: str) -> Dict[str, Any]:
        """Analyze a browser database for telemetry and tracking data"""
        result = {
            'database_file': str(db_file),
            'tracking_entries': 0,
            'fingerprint_entries': 0,
            'telemetry_domains': set(),
            'privacy_violations': [],
            'last_activity': None
        }
        
        try:
            # Known telemetry and tracking patterns
            telemetry_patterns = [
                'google-analytics', 'googletagmanager', 'doubleclick',
                'facebook.com/tr', 'analytics', 'telemetry', 'tracking',
                'metrics', 'crash', 'usage', 'stats', 'beacon'
            ]
            
            fingerprint_patterns = [
                'canvas', 'webgl', 'font', 'screen', 'timezone',
                'language', 'platform', 'useragent', 'fingerprint'
            ]
            
            # Safely read database
            conn = sqlite3.connect(f"file:{db_file}?mode=ro", uri=True)
            cursor = conn.cursor()
            
            # Get table names
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            for table in tables:
                try:
                    # Get table info
                    cursor.execute(f"PRAGMA table_info({table})")
                    columns = [row[1] for row in cursor.fetchall()]
                    
                    # Look for tracking-related columns
                    tracking_columns = [col for col in columns if any(pattern in col.lower() for pattern in telemetry_patterns)]
                    fingerprint_columns = [col for col in columns if any(pattern in col.lower() for pattern in fingerprint_patterns)]
                    
                    if tracking_columns or fingerprint_columns:
                        # Count entries in tracking tables
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count = cursor.fetchone()[0]
                        
                        if tracking_columns:
                            result['tracking_entries'] += count
                            result['privacy_violations'].append({
                                'type': 'tracking_data',
                                'table': table,
                                'columns': tracking_columns,
                                'entry_count': count
                            })
                        
                        if fingerprint_columns:
                            result['fingerprint_entries'] += count
                            result['privacy_violations'].append({
                                'type': 'fingerprint_data',
                                'table': table,
                                'columns': fingerprint_columns,
                                'entry_count': count
                            })
                    
                    # Look for URL/domain data that might contain telemetry endpoints
                    url_columns = [col for col in columns if 'url' in col.lower() or 'domain' in col.lower() or 'host' in col.lower()]
                    if url_columns:
                        for url_col in url_columns:
                            cursor.execute(f"SELECT DISTINCT {url_col} FROM {table} WHERE {url_col} IS NOT NULL LIMIT 100")
                            urls = [row[0] for row in cursor.fetchall() if row[0]]
                            
                            for url in urls:
                                if any(pattern in str(url).lower() for pattern in telemetry_patterns):
                                    result['telemetry_domains'].add(self._extract_domain(str(url)))
                
                except Exception as e:
                    # Skip problematic tables
                    continue
            
            conn.close()
            
            # Get file modification time as last activity
            result['last_activity'] = datetime.fromtimestamp(db_file.stat().st_mtime).isoformat()
        
        except Exception as e:
            warning(f"Error analyzing database {db_file}: {e}")
            result['error'] = str(e)
        
        return result
    
    def _analyze_browser_preferences(self, profile_path: Path, browser: str) -> Dict[str, Any]:
        """Analyze browser preference files for privacy settings"""
        result = {
            'privacy_violations': []
        }
        
        try:
            if browser in ['chrome', 'edge', 'brave']:
                prefs_file = profile_path / 'Preferences'
                if prefs_file.exists():
                    with open(prefs_file, 'r', encoding='utf-8') as f:
                        prefs = json.load(f)
                        
                        # Check for telemetry settings
                        if prefs.get('metrics_reporting_enabled', False):
                            result['privacy_violations'].append({
                                'type': 'telemetry_enabled',
                                'setting': 'metrics_reporting_enabled',
                                'value': True
                            })
                        
                        if prefs.get('safebrowsing', {}).get('enabled', False):
                            result['privacy_violations'].append({
                                'type': 'safe_browsing_tracking',
                                'setting': 'safebrowsing.enabled',
                                'value': True
                            })
            
            elif browser == 'firefox':
                prefs_file = profile_path / 'prefs.js'
                if prefs_file.exists():
                    with open(prefs_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                        # Check for telemetry preferences
                        if 'toolkit.telemetry.enabled", true' in content:
                            result['privacy_violations'].append({
                                'type': 'telemetry_enabled',
                                'setting': 'toolkit.telemetry.enabled',
                                'value': True
                            })
                        
                        if 'datareporting.healthreport.uploadEnabled", true' in content:
                            result['privacy_violations'].append({
                                'type': 'health_report_enabled',
                                'setting': 'datareporting.healthreport.uploadEnabled',
                                'value': True
                            })
        
        except Exception as e:
            warning(f"Error analyzing preferences: {e}")
        
        return result
    
    def _extract_domain(self, url: str) -> str:
        """Extract domain from URL"""
        try:
            if '://' in url:
                domain = url.split('://')[1].split('/')[0]
            else:
                domain = url.split('/')[0]
            return domain.lower()
        except:
            return url
    
    def _calculate_privacy_risk(self, scan_results: Dict[str, Any]) -> int:
        """Calculate overall privacy risk score (0-100)"""
        risk_score = 0
        
        summary = scan_results.get('summary', {})
        
        # Base risk from tracking entries
        tracking_entries = summary.get('total_tracking_entries', 0)
        if tracking_entries > 1000:
            risk_score += 40
        elif tracking_entries > 100:
            risk_score += 25
        elif tracking_entries > 10:
            risk_score += 10
        
        # Risk from fingerprint data
        fingerprint_entries = summary.get('total_fingerprint_data', 0)
        if fingerprint_entries > 100:
            risk_score += 30
        elif fingerprint_entries > 10:
            risk_score += 15
        
        # Risk from privacy violations
        total_violations = sum(
            len(browser_data.get('privacy_violations', []))
            for browser_data in scan_results.get('browser_results', {}).values()
            if isinstance(browser_data, dict)
        )
        
        if total_violations > 10:
            risk_score += 30
        elif total_violations > 5:
            risk_score += 20
        elif total_violations > 0:
            risk_score += 10
        
        return min(risk_score, 100)
    
    def _generate_privacy_recommendations(self, scan_results: Dict[str, Any]) -> List[str]:
        """Generate privacy recommendations based on scan results"""
        recommendations = []
        
        summary = scan_results.get('summary', {})
        risk_score = summary.get('privacy_risk_score', 0)
        
        if risk_score > 70:
            recommendations.append("🚨 HIGH PRIVACY RISK: Immediate action required to reduce tracking exposure")
        elif risk_score > 40:
            recommendations.append("⚠️ MODERATE PRIVACY RISK: Consider implementing privacy measures")
        else:
            recommendations.append("✅ LOW PRIVACY RISK: Good privacy posture maintained")
        
        if summary.get('total_tracking_entries', 0) > 100:
            recommendations.append("🧹 Clear browser tracking data regularly")
        
        if summary.get('total_fingerprint_data', 0) > 10:
            recommendations.append("🎭 Use fingerprint protection extensions")
        
        # Browser-specific recommendations
        for browser, data in scan_results.get('browser_results', {}).items():
            if isinstance(data, dict) and data.get('privacy_violations'):
                recommendations.append(f"🔧 Review {browser.title()} privacy settings")
        
        recommendations.extend([
            "🛡️ Use privacy-focused browser extensions",
            "🔒 Enable strict tracking protection",
            "🌐 Consider using privacy-focused browsers",
            "📱 Regular privacy audits recommended"
        ])
        
        return recommendations
