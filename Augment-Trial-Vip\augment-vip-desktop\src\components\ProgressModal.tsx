import React, { useEffect, useState } from 'react';
import { X, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import * as Dialog from '@radix-ui/react-dialog';
import * as Progress from '@radix-ui/react-progress';
import useAppStore from '../store/useAppStore';

interface ProgressModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ProgressModal: React.FC<ProgressModalProps> = ({ isOpen, onClose }) => {
  const {
    isOperationRunning,
    currentOperation,
    operationProgress,
    operationLogs
  } = useAppStore();

  const [outputLines, setOutputLines] = useState<Array<{
    type: 'stdout' | 'stderr';
    text: string;
    timestamp: Date;
  }>>([]);

  useEffect(() => {
    if (window.electronAPI) {
      const handleOutput = (data: { type: 'stdout' | 'stderr'; data: string }) => {
        setOutputLines(prev => [
          ...prev,
          {
            type: data.type,
            text: data.data,
            timestamp: new Date()
          }
        ].slice(-100)); // Keep only last 100 lines
      };

      window.electronAPI.onCommandOutput(handleOutput);

      return () => {
        window.electronAPI.removeAllListeners('command-output');
      };
    }
  }, []);

  // Clear output when modal closes
  useEffect(() => {
    if (!isOpen) {
      setOutputLines([]);
    }
  }, [isOpen]);

  const getOperationTitle = () => {
    switch (currentOperation) {
      case 'clean':
        return 'Cleaning VS Code Databases';
      case 'modify-ids':
        return 'Modifying Telemetry IDs';
      case 'all':
        return 'Running All Operations';
      default:
        return 'Processing...';
    }
  };

  const getProgressColor = () => {
    if (!isOperationRunning && operationProgress === 100) {
      const lastLog = operationLogs[0];
      if (lastLog?.status === 'success') {
        return 'bg-green-500';
      } else if (lastLog?.status === 'error') {
        return 'bg-red-500';
      }
    }
    return 'bg-blue-500';
  };

  const getStatusIcon = () => {
    if (isOperationRunning) {
      return <Loader2 className="w-6 h-6 text-blue-500 animate-spin" />;
    } else if (operationProgress === 100) {
      const lastLog = operationLogs[0];
      if (lastLog?.status === 'success') {
        return <CheckCircle className="w-6 h-6 text-green-500" />;
      } else if (lastLog?.status === 'error') {
        return <AlertCircle className="w-6 h-6 text-red-500" />;
      }
    }
    return <Loader2 className="w-6 h-6 text-gray-400" />;
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 
                                 bg-white dark:bg-dark-800 rounded-lg shadow-xl border border-gray-200 
                                 dark:border-dark-700 w-full max-w-2xl max-h-[80vh] z-50">
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-dark-700">
            <div className="flex items-center space-x-3">
              {getStatusIcon()}
              <div>
                <Dialog.Title className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  {getOperationTitle()}
                </Dialog.Title>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {isOperationRunning ? 'Operation in progress...' : 'Operation completed'}
                </p>
              </div>
            </div>
            <Dialog.Close asChild>
              <button className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-700 transition-colors">
                <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
              </button>
            </Dialog.Close>
          </div>

          <div className="p-6 space-y-6">
            {/* Progress Bar */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Progress
                </span>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {Math.round(operationProgress)}%
                </span>
              </div>
              <Progress.Root className="relative overflow-hidden bg-gray-200 dark:bg-dark-700 rounded-full w-full h-2">
                <Progress.Indicator
                  className={`h-full transition-transform duration-300 ease-out ${getProgressColor()}`}
                  style={{ transform: `translateX(-${100 - operationProgress}%)` }}
                />
              </Progress.Root>
            </div>

            {/* Output Console */}
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Console Output
              </h3>
              <div className="bg-gray-900 dark:bg-black rounded-lg p-4 h-64 overflow-y-auto font-mono text-sm">
                {outputLines.length > 0 ? (
                  outputLines.map((line, index) => (
                    <div
                      key={index}
                      className={`mb-1 ${
                        line.type === 'stderr' 
                          ? 'text-red-400' 
                          : 'text-green-400'
                      }`}
                    >
                      <span className="text-gray-500 text-xs mr-2">
                        {line.timestamp.toLocaleTimeString()}
                      </span>
                      {line.text}
                    </div>
                  ))
                ) : (
                  <div className="text-gray-500 italic">
                    Waiting for output...
                  </div>
                )}
              </div>
            </div>

            {/* Operation Summary */}
            {!isOperationRunning && operationLogs.length > 0 && (
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Summary
                </h3>
                <div className={`p-4 rounded-lg border ${
                  operationLogs[0]?.status === 'success'
                    ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                    : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
                }`}>
                  <p className={`text-sm ${
                    operationLogs[0]?.status === 'success'
                      ? 'text-green-800 dark:text-green-200'
                      : 'text-red-800 dark:text-red-200'
                  }`}>
                    {operationLogs[0]?.message}
                  </p>
                  {operationLogs[0]?.details && (
                    <p className="text-xs mt-2 text-gray-600 dark:text-gray-400">
                      {operationLogs[0].details}
                    </p>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end p-6 border-t border-gray-200 dark:border-dark-700 space-x-3">
            {!isOperationRunning && (
              <Dialog.Close asChild>
                <button className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 
                                 transition-colors font-medium">
                  Close
                </button>
              </Dialog.Close>
            )}
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default ProgressModal;
