"""
Security Middleware and Utilities
Comprehensive security features including authentication, rate limiting, and protection
"""

import hashlib
import hmac
import time
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

from fastapi import Request, Response, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JW<PERSON>rror, jwt
from passlib.context import CryptContext
import structlog

from .config import get_settings
from .exceptions import AuthenticationError, RateLimitError

logger = structlog.get_logger(__name__)
settings = get_settings()

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT token handler
security = HTTPBearer(auto_error=False)

# Rate limiting storage (in production, use Redis)
rate_limit_storage: Dict[str, Dict[str, Any]] = {}


class SecurityMiddleware:
    """
    Security middleware for request processing
    """
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        request = Request(scope, receive)
        
        # Security headers
        async def send_wrapper(message):
            if message["type"] == "http.response.start":
                headers = dict(message.get("headers", []))
                
                # Add security headers
                security_headers = {
                    b"x-content-type-options": b"nosniff",
                    b"x-frame-options": b"DENY",
                    b"x-xss-protection": b"1; mode=block",
                    b"strict-transport-security": b"max-age=31536000; includeSubDomains",
                    b"referrer-policy": b"strict-origin-when-cross-origin",
                    b"permissions-policy": b"geolocation=(), microphone=(), camera=()",
                }
                
                headers.update(security_headers)
                message["headers"] = list(headers.items())
            
            await send(message)
        
        # Rate limiting check
        if settings.RATE_LIMIT_ENABLED:
            await self._check_rate_limit(request)
        
        await self.app(scope, receive, send_wrapper)
    
    async def _check_rate_limit(self, request: Request):
        """Check rate limiting for the request"""
        client_ip = self._get_client_ip(request)
        current_time = time.time()
        window_start = current_time - settings.RATE_LIMIT_WINDOW
        
        # Clean old entries
        if client_ip in rate_limit_storage:
            rate_limit_storage[client_ip]["requests"] = [
                req_time for req_time in rate_limit_storage[client_ip]["requests"]
                if req_time > window_start
            ]
        else:
            rate_limit_storage[client_ip] = {"requests": []}
        
        # Check rate limit
        request_count = len(rate_limit_storage[client_ip]["requests"])
        if request_count >= settings.RATE_LIMIT_REQUESTS:
            logger.warning(
                "Rate limit exceeded",
                client_ip=client_ip,
                request_count=request_count,
                limit=settings.RATE_LIMIT_REQUESTS,
            )
            raise RateLimitError(retry_after=settings.RATE_LIMIT_WINDOW)
        
        # Record request
        rate_limit_storage[client_ip]["requests"].append(current_time)
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address from request"""
        # Check for forwarded headers (when behind proxy)
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # Fallback to direct connection
        if request.client:
            return request.client.host
        
        return "unknown"


class AuthManager:
    """
    Authentication and authorization manager
    """
    
    @staticmethod
    def hash_password(password: str) -> str:
        """Hash a password"""
        return pwd_context.hash(password)
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash"""
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """Create JWT access token"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode.update({"exp": expire, "type": "access"})
        
        return jwt.encode(to_encode, settings.SECRET_KEY, algorithm="HS256")
    
    @staticmethod
    def create_refresh_token(data: Dict[str, Any]) -> str:
        """Create JWT refresh token"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
        to_encode.update({"exp": expire, "type": "refresh"})
        
        return jwt.encode(to_encode, settings.SECRET_KEY, algorithm="HS256")
    
    @staticmethod
    def verify_token(token: str, token_type: str = "access") -> Dict[str, Any]:
        """Verify and decode JWT token"""
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
            
            # Check token type
            if payload.get("type") != token_type:
                raise AuthenticationError("Invalid token type")
            
            # Check expiration
            exp = payload.get("exp")
            if exp and datetime.utcnow() > datetime.fromtimestamp(exp):
                raise AuthenticationError("Token expired")
            
            return payload
            
        except JWTError as e:
            logger.warning("JWT verification failed", error=str(e))
            raise AuthenticationError("Invalid token")
    
    @staticmethod
    def generate_api_key() -> str:
        """Generate a secure API key"""
        import secrets
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def verify_api_key(api_key: str, stored_hash: str) -> bool:
        """Verify API key against stored hash"""
        return hmac.compare_digest(
            hashlib.sha256(api_key.encode()).hexdigest(),
            stored_hash
        )


async def get_current_user(credentials: Optional[HTTPAuthorizationCredentials] = None):
    """
    Dependency to get current authenticated user
    """
    if not credentials:
        raise AuthenticationError("Missing authentication credentials")
    
    try:
        payload = AuthManager.verify_token(credentials.credentials)
        user_id = payload.get("sub")
        
        if not user_id:
            raise AuthenticationError("Invalid token payload")
        
        # In a real application, you would fetch the user from the database
        # For now, return the user_id from the token
        return {"user_id": user_id, "payload": payload}
        
    except AuthenticationError:
        raise
    except Exception as e:
        logger.error("Authentication error", error=str(e))
        raise AuthenticationError("Authentication failed")


async def get_optional_user(credentials: Optional[HTTPAuthorizationCredentials] = None):
    """
    Dependency to get current user if authenticated, None otherwise
    """
    if not credentials:
        return None
    
    try:
        return await get_current_user(credentials)
    except AuthenticationError:
        return None


class APIKeyAuth:
    """
    API Key authentication handler
    """
    
    def __init__(self, api_key_header: str = "X-API-Key"):
        self.api_key_header = api_key_header
    
    async def __call__(self, request: Request) -> Optional[Dict[str, Any]]:
        """Authenticate request using API key"""
        api_key = request.headers.get(self.api_key_header)
        
        if not api_key:
            raise AuthenticationError("Missing API key")
        
        # In a real application, you would verify the API key against the database
        # For now, we'll use a simple check
        if not self._verify_api_key(api_key):
            raise AuthenticationError("Invalid API key")
        
        return {"api_key": api_key, "authenticated": True}
    
    def _verify_api_key(self, api_key: str) -> bool:
        """Verify API key (placeholder implementation)"""
        # In production, verify against database
        return len(api_key) >= 32  # Simple validation


def require_permissions(*permissions: str):
    """
    Decorator to require specific permissions
    
    Usage:
        @require_permissions("admin", "write")
        async def protected_endpoint(user = Depends(get_current_user)):
            # Endpoint logic here
            pass
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Get user from kwargs (injected by FastAPI dependency)
            user = kwargs.get('user') or kwargs.get('current_user')
            
            if not user:
                raise AuthenticationError("Authentication required")
            
            # Check permissions (placeholder implementation)
            user_permissions = user.get("permissions", [])
            
            for permission in permissions:
                if permission not in user_permissions:
                    from .exceptions import AuthorizationError
                    raise AuthorizationError(f"Missing permission: {permission}")
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


# Export commonly used items
__all__ = [
    'SecurityMiddleware',
    'AuthManager',
    'get_current_user',
    'get_optional_user',
    'APIKeyAuth',
    'require_permissions',
]
