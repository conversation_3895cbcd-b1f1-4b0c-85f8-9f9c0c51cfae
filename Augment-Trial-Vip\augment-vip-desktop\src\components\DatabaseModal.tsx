import React, { useState, useEffect } from "react";
import {
  X,
  Database,
  HardDrive,
  Clock,
  FileText,
  Trash2,
  Download,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>cle,
  Loader,
  Shield,
} from "lucide-react";

interface DatabaseInfo {
  path?: string;
  name: string;
  size: number;
  modified?: Date;
  type: "sqlite" | "postgresql" | "mysql";
  canClean: boolean;
  connectionInfo?: any;
}

interface DatabaseScanResult {
  sqlite: DatabaseInfo[];
  postgresql: DatabaseInfo[];
  mysql: DatabaseInfo[];
  totalSize: number;
  scanTime: number;
}

interface DatabaseModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const DatabaseModal: React.FC<DatabaseModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [scanResult, setScanResult] = useState<DatabaseScanResult | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [scanProgress, setScanProgress] = useState(0);
  const [scanStep, setScanStep] = useState("");
  const [selectedDatabases, setSelectedDatabases] = useState<Set<string>>(
    new Set()
  );
  const [isOperating, setIsOperating] = useState(false);
  const [operationProgress, setOperationProgress] = useState(0);
  const [operationStep, setOperationStep] = useState("");
  const [operationLogs, setOperationLogs] = useState<string[]>([]);

  useEffect(() => {
    if (window.electronAPI && isOpen) {
      // Set up progress listeners
      window.electronAPI.onScanProgress((data) => {
        setScanProgress(data.progress);
        setScanStep(data.step);
      });

      window.electronAPI.onCleanProgress((data) => {
        setOperationProgress(data.progress);
        setOperationStep(data.step);
        setOperationLogs((prev) => [
          ...prev,
          `${data.step} (${data.progress}%)`,
        ]);
      });

      window.electronAPI.onBackupProgress((data) => {
        setOperationProgress(data.progress);
        setOperationStep(data.step);
        setOperationLogs((prev) => [
          ...prev,
          `${data.step} (${data.progress}%)`,
        ]);
      });

      return () => {
        window.electronAPI.removeAllListeners("scan-progress");
        window.electronAPI.removeAllListeners("clean-progress");
        window.electronAPI.removeAllListeners("backup-progress");
      };
    }
  }, [isOpen]);

  const startScan = async () => {
    if (!window.electronAPI) return;

    setIsScanning(true);
    setScanProgress(0);
    setScanStep("Initializing scan...");
    setOperationLogs([]);

    try {
      const result = await window.electronAPI.scanDatabases();
      if (result.success && result.data) {
        // Type-safe conversion of the result data
        const typedResult: DatabaseScanResult = {
          sqlite: result.data.sqlite?.map((db: any) => ({
            ...db,
            type: "sqlite" as const
          })) || [],
          postgresql: result.data.postgresql?.map((db: any) => ({
            ...db,
            type: "postgresql" as const
          })) || [],
          mysql: result.data.mysql?.map((db: any) => ({
            ...db,
            type: "mysql" as const
          })) || [],
          totalSize: result.data.totalSize || 0,
          scanTime: result.data.scanTime || 0
        };
        setScanResult(typedResult);
        setScanStep("Scan completed successfully!");
        setScanProgress(100);
      } else {
        throw new Error(result.error || "Scan failed");
      }
    } catch (error) {
      console.error("Database scan failed:", error);
      setScanStep(`Scan failed: ${error}`);
    } finally {
      setIsScanning(false);
    }
  };

  const cleanSelectedDatabases = async () => {
    if (!window.electronAPI || !scanResult || selectedDatabases.size === 0)
      return;

    setIsOperating(true);
    setOperationProgress(0);
    setOperationStep("Starting cleanup...");
    setOperationLogs(["Starting database cleanup operation..."]);

    try {
      const allDatabases = [
        ...scanResult.sqlite,
        ...scanResult.postgresql,
        ...scanResult.mysql,
      ];

      const databasesToClean = allDatabases.filter((db) =>
        selectedDatabases.has(getDatabaseId(db))
      );

      for (let i = 0; i < databasesToClean.length; i++) {
        const db = databasesToClean[i];
        setOperationStep(`Cleaning ${db.name}...`);

        const result = await window.electronAPI.cleanDatabase(db);
        if (result.success) {
          setOperationLogs((prev) => [
            ...prev,
            `✓ Successfully cleaned ${db.name}`,
          ]);
        } else {
          setOperationLogs((prev) => [
            ...prev,
            `✗ Failed to clean ${db.name}: ${result.error}`,
          ]);
        }

        setOperationProgress(
          Math.round(((i + 1) / databasesToClean.length) * 100)
        );
      }

      setOperationStep("Cleanup completed!");
      // Refresh scan results
      await startScan();
    } catch (error) {
      console.error("Database cleanup failed:", error);
      setOperationStep(`Cleanup failed: ${error}`);
      setOperationLogs((prev) => [...prev, `✗ Cleanup failed: ${error}`]);
    } finally {
      setIsOperating(false);
    }
  };

  const getDatabaseId = (db: DatabaseInfo) => {
    return `${db.type}-${db.name}-${db.path || ""}`;
  };

  const formatSize = (bytes: number) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const toggleDatabaseSelection = (db: DatabaseInfo) => {
    const id = getDatabaseId(db);
    const newSelection = new Set(selectedDatabases);
    if (newSelection.has(id)) {
      newSelection.delete(id);
    } else {
      newSelection.add(id);
    }
    setSelectedDatabases(newSelection);
  };

  const selectAllCleanable = () => {
    if (!scanResult) return;

    const allDatabases = [
      ...scanResult.sqlite,
      ...scanResult.postgresql,
      ...scanResult.mysql,
    ];

    const cleanableDatabases = allDatabases.filter((db) => db.canClean);
    const newSelection = new Set(cleanableDatabases.map(getDatabaseId));
    setSelectedDatabases(newSelection);
  };

  const clearSelection = () => {
    setSelectedDatabases(new Set());
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 backdrop-blur-sm">
      <div className="bg-white dark:bg-slate-800 rounded-2xl w-full max-w-4xl max-h-[90vh] mx-4 shadow-2xl border border-gray-200 dark:border-slate-700 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-slate-700">
          <div className="flex items-center space-x-3">
            <Database className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              Database Manager
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden flex flex-col">
          {!scanResult ? (
            /* Initial Scan */
            <div className="flex-1 flex items-center justify-center p-8">
              <div className="text-center max-w-md">
                <Database className="w-16 h-16 text-blue-600 dark:text-blue-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Scan for Databases
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Discover SQLite, PostgreSQL, and MySQL databases on your
                  system that can be safely cleaned.
                </p>

                {isScanning ? (
                  <div className="space-y-4">
                    <div className="flex items-center justify-center space-x-2">
                      <Loader className="w-5 h-5 text-blue-600 animate-spin" />
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {scanStep}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-slate-700 rounded-full h-2">
                      <div
                        className="h-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full transition-all duration-300"
                        style={{ width: `${scanProgress}%` }}
                      />
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {scanProgress}% complete
                    </div>
                  </div>
                ) : (
                  <button
                    onClick={startScan}
                    className="px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:scale-105"
                  >
                    Start Database Scan
                  </button>
                )}
              </div>
            </div>
          ) : (
            /* Scan Results */
            <div className="flex-1 flex flex-col overflow-hidden">
              {/* Summary */}
              <div className="p-6 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-slate-700 dark:to-slate-600 border-b border-gray-200 dark:border-slate-600">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      Scan Results
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Found{" "}
                      {scanResult.sqlite.length +
                        scanResult.postgresql.length +
                        scanResult.mysql.length}{" "}
                      databases • Total size: {formatSize(scanResult.totalSize)}
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={selectAllCleanable}
                      className="px-4 py-2 text-sm bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors"
                    >
                      Select All Cleanable
                    </button>
                    <button
                      onClick={clearSelection}
                      className="px-4 py-2 text-sm bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-slate-600 transition-colors"
                    >
                      Clear Selection
                    </button>
                    <button
                      onClick={startScan}
                      className="px-4 py-2 text-sm bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 rounded-lg hover:bg-green-200 dark:hover:bg-green-800 transition-colors"
                    >
                      Rescan
                    </button>
                  </div>
                </div>
              </div>

              {/* Database List */}
              <div className="flex-1 overflow-y-auto p-6">
                <div className="space-y-6">
                  {/* SQLite Databases */}
                  {scanResult.sqlite.length > 0 && (
                    <div>
                      <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                        <HardDrive className="w-4 h-4 mr-2" />
                        SQLite Databases ({scanResult.sqlite.length})
                      </h4>
                      <div className="space-y-2">
                        {scanResult.sqlite.map((db, index) => (
                          <DatabaseItem
                            key={`sqlite-${index}`}
                            database={db}
                            isSelected={selectedDatabases.has(
                              getDatabaseId(db)
                            )}
                            onToggle={() => toggleDatabaseSelection(db)}
                          />
                        ))}
                      </div>
                    </div>
                  )}

                  {/* PostgreSQL Databases */}
                  {scanResult.postgresql.length > 0 && (
                    <div>
                      <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                        <Database className="w-4 h-4 mr-2" />
                        PostgreSQL Databases ({scanResult.postgresql.length})
                      </h4>
                      <div className="space-y-2">
                        {scanResult.postgresql.map((db, index) => (
                          <DatabaseItem
                            key={`postgresql-${index}`}
                            database={db}
                            isSelected={selectedDatabases.has(
                              getDatabaseId(db)
                            )}
                            onToggle={() => toggleDatabaseSelection(db)}
                          />
                        ))}
                      </div>
                    </div>
                  )}

                  {/* MySQL Databases */}
                  {scanResult.mysql.length > 0 && (
                    <div>
                      <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                        <Database className="w-4 h-4 mr-2" />
                        MySQL Databases ({scanResult.mysql.length})
                      </h4>
                      <div className="space-y-2">
                        {scanResult.mysql.map((db, index) => (
                          <DatabaseItem
                            key={`mysql-${index}`}
                            database={db}
                            isSelected={selectedDatabases.has(
                              getDatabaseId(db)
                            )}
                            onToggle={() => toggleDatabaseSelection(db)}
                          />
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Operation Progress */}
              {isOperating && (
                <div className="p-4 bg-gray-50 dark:bg-slate-700 border-t border-gray-200 dark:border-slate-600">
                  <div className="flex items-center space-x-3 mb-2">
                    <Loader className="w-4 h-4 text-blue-600 animate-spin" />
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {operationStep}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-slate-600 rounded-full h-2 mb-2">
                    <div
                      className="h-2 bg-gradient-to-r from-red-500 to-red-600 rounded-full transition-all duration-300"
                      style={{ width: `${operationProgress}%` }}
                    />
                  </div>
                  {operationLogs.length > 0 && (
                    <div className="max-h-20 overflow-y-auto bg-gray-900 rounded p-2">
                      <div className="text-xs font-mono space-y-1">
                        {operationLogs.slice(-5).map((log, index) => (
                          <div key={index} className="text-green-400">
                            {log}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Actions */}
              <div className="p-6 border-t border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-700">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {selectedDatabases.size} database(s) selected
                  </div>
                  <div className="flex space-x-3">
                    <button
                      onClick={cleanSelectedDatabases}
                      disabled={selectedDatabases.size === 0 || isOperating}
                      className="px-6 py-2 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg hover:from-red-600 hover:to-red-700 transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center space-x-2"
                    >
                      <Trash2 className="w-4 h-4" />
                      <span>Clean Selected</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

interface DatabaseItemProps {
  database: DatabaseInfo;
  isSelected: boolean;
  onToggle: () => void;
}

const DatabaseItem: React.FC<DatabaseItemProps> = ({
  database,
  isSelected,
  onToggle,
}) => {
  const formatSize = (bytes: number) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <div
      className={`p-4 rounded-lg border transition-all duration-200 cursor-pointer ${
        isSelected
          ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
          : "border-gray-200 dark:border-slate-600 hover:border-gray-300 dark:hover:border-slate-500"
      } ${!database.canClean ? "opacity-50" : ""}`}
      onClick={database.canClean ? onToggle : undefined}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div
            className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
              isSelected
                ? "border-blue-500 bg-blue-500"
                : "border-gray-300 dark:border-slate-500"
            }`}
          >
            {isSelected && <CheckCircle className="w-3 h-3 text-white" />}
          </div>
          <div>
            <div className="font-medium text-gray-900 dark:text-white">
              {database.name}
            </div>
            {database.path && (
              <div className="text-xs text-gray-500 dark:text-gray-400 truncate max-w-md">
                {database.path}
              </div>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {formatSize(database.size)}
          </div>
          <div
            className={`px-2 py-1 rounded text-xs font-medium ${
              database.type === "sqlite"
                ? "bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300"
                : database.type === "postgresql"
                ? "bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300"
                : "bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-300"
            }`}
          >
            {database.type.toUpperCase()}
          </div>
          {!database.canClean && (
            <div className="flex items-center space-x-1 text-yellow-600 dark:text-yellow-400">
              <AlertTriangle className="w-4 h-4" />
              <span className="text-xs">Protected</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
