/**
 * Utility for merging Tailwind CSS classes with proper conflict resolution
 */

import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * Combines clsx and tailwind-merge for optimal class name handling
 * 
 * @param inputs - Class names to merge
 * @returns Merged class string
 * 
 * @example
 * cn("px-2 py-1", "px-4") // "py-1 px-4"
 * cn("text-red-500", condition && "text-blue-500") // "text-blue-500" if condition is true
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Variant utility for creating component variants
 */
export type VariantProps<T> = {
  [K in keyof T]: T[K] extends Record<string, any> ? keyof T[K] : never;
};

/**
 * Creates a variant function for component styling
 */
export function createVariants<T extends Record<string, Record<string, string>>>(
  variants: T
) {
  return function getVariant<K extends keyof T>(
    variant: K,
    value: keyof T[K]
  ): string {
    return variants[variant][value] || "";
  };
}
