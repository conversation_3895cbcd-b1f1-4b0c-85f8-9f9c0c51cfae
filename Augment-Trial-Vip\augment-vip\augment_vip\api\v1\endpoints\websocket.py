"""
WebSocket Endpoints
Real-time communication for live updates and notifications
"""

import json
from datetime import datetime
from typing import Dict, List, Any, Optional

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from pydantic import BaseModel
import structlog

from ....core.config import get_settings
from ....core.metrics import MetricsCollector

logger = structlog.get_logger(__name__)
settings = get_settings()

router = APIRouter()

# Connection manager for WebSocket connections
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.user_connections: Dict[str, List[WebSocket]] = {}
    
    async def connect(self, websocket: WebSocket, user_id: Optional[str] = None):
        await websocket.accept()
        self.active_connections.append(websocket)
        
        if user_id:
            if user_id not in self.user_connections:
                self.user_connections[user_id] = []
            self.user_connections[user_id].append(websocket)
        
        MetricsCollector.increment_websocket_connections()
        logger.info("WebSocket connection established", user_id=user_id)
    
    def disconnect(self, websocket: WebSocket, user_id: Optional[str] = None):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        
        if user_id and user_id in self.user_connections:
            if websocket in self.user_connections[user_id]:
                self.user_connections[user_id].remove(websocket)
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]
        
        MetricsCollector.decrement_websocket_connections()
        logger.info("WebSocket connection closed", user_id=user_id)
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)
        MetricsCollector.record_websocket_message("outbound", "personal")
    
    async def send_to_user(self, message: str, user_id: str):
        if user_id in self.user_connections:
            for connection in self.user_connections[user_id]:
                try:
                    await connection.send_text(message)
                    MetricsCollector.record_websocket_message("outbound", "user")
                except:
                    # Connection might be closed
                    pass
    
    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
                MetricsCollector.record_websocket_message("outbound", "broadcast")
            except:
                # Connection might be closed
                pass


# Global connection manager
manager = ConnectionManager()


class WebSocketMessage(BaseModel):
    """WebSocket message model"""
    type: str
    data: Dict[str, Any]
    timestamp: datetime = None
    
    def __init__(self, **data):
        if data.get('timestamp') is None:
            data['timestamp'] = datetime.utcnow()
        super().__init__(**data)


@router.websocket("/connect")
async def websocket_endpoint(websocket: WebSocket):
    """
    Main WebSocket endpoint for real-time communication
    """
    user_id = None
    
    try:
        await manager.connect(websocket)
        
        # Send welcome message
        welcome_msg = WebSocketMessage(
            type="connection",
            data={"status": "connected", "message": "Welcome to Augment VIP WebSocket"}
        )
        await manager.send_personal_message(welcome_msg.json(), websocket)
        
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            MetricsCollector.record_websocket_message("inbound", "client")
            
            try:
                message = json.loads(data)
                await handle_websocket_message(websocket, message, user_id)
            except json.JSONDecodeError:
                error_msg = WebSocketMessage(
                    type="error",
                    data={"message": "Invalid JSON format"}
                )
                await manager.send_personal_message(error_msg.json(), websocket)
            
    except WebSocketDisconnect:
        manager.disconnect(websocket, user_id)
    except Exception as e:
        logger.error("WebSocket error", error=str(e))
        manager.disconnect(websocket, user_id)


async def handle_websocket_message(websocket: WebSocket, message: Dict[str, Any], user_id: Optional[str]):
    """
    Handle incoming WebSocket messages
    """
    message_type = message.get("type")
    data = message.get("data", {})
    
    if message_type == "auth":
        # Handle authentication
        token = data.get("token")
        if token:
            # In production, verify JWT token
            user_id = "authenticated_user"  # Placeholder
            
            response = WebSocketMessage(
                type="auth_response",
                data={"status": "authenticated", "user_id": user_id}
            )
            await manager.send_personal_message(response.json(), websocket)
        else:
            response = WebSocketMessage(
                type="auth_response",
                data={"status": "failed", "message": "Token required"}
            )
            await manager.send_personal_message(response.json(), websocket)
    
    elif message_type == "subscribe":
        # Handle subscription to events
        event_types = data.get("events", [])
        
        response = WebSocketMessage(
            type="subscription_response",
            data={"status": "subscribed", "events": event_types}
        )
        await manager.send_personal_message(response.json(), websocket)
    
    elif message_type == "ping":
        # Handle ping/pong for keepalive
        response = WebSocketMessage(
            type="pong",
            data={"timestamp": datetime.utcnow().isoformat()}
        )
        await manager.send_personal_message(response.json(), websocket)
    
    elif message_type == "operation_status":
        # Handle operation status request
        task_id = data.get("task_id")
        if task_id:
            # In production, get actual task status
            response = WebSocketMessage(
                type="operation_update",
                data={
                    "task_id": task_id,
                    "status": "running",
                    "progress": 45.0,
                    "message": "Processing..."
                }
            )
            await manager.send_personal_message(response.json(), websocket)
    
    else:
        # Unknown message type
        response = WebSocketMessage(
            type="error",
            data={"message": f"Unknown message type: {message_type}"}
        )
        await manager.send_personal_message(response.json(), websocket)


# Utility functions for sending notifications
async def send_operation_update(user_id: str, task_id: str, status: str, progress: float, message: str):
    """
    Send operation update to specific user
    """
    update_msg = WebSocketMessage(
        type="operation_update",
        data={
            "task_id": task_id,
            "status": status,
            "progress": progress,
            "message": message
        }
    )
    await manager.send_to_user(update_msg.json(), user_id)


async def send_system_alert(alert_level: str, message: str, details: Dict[str, Any] = None):
    """
    Broadcast system alert to all connected clients
    """
    alert_msg = WebSocketMessage(
        type="system_alert",
        data={
            "level": alert_level,
            "message": message,
            "details": details or {}
        }
    )
    await manager.broadcast(alert_msg.json())


async def send_notification(user_id: str, title: str, message: str, notification_type: str = "info"):
    """
    Send notification to specific user
    """
    notification_msg = WebSocketMessage(
        type="notification",
        data={
            "title": title,
            "message": message,
            "type": notification_type
        }
    )
    await manager.send_to_user(notification_msg.json(), user_id)


# Health check for WebSocket connections
@router.get("/status")
async def websocket_status():
    """
    Get WebSocket connection status
    """
    return {
        "enabled": settings.WEBSOCKET_ENABLED,
        "active_connections": len(manager.active_connections),
        "user_connections": len(manager.user_connections),
        "heartbeat_interval": settings.WEBSOCKET_HEARTBEAT_INTERVAL
    }
