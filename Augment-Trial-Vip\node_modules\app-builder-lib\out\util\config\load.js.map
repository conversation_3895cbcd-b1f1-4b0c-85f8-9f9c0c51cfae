{"version": 3, "file": "load.js", "sourceRoot": "", "sources": ["../../../src/util/config/load.ts"], "names": [], "mappings": ";;AAoDA,8CAUC;AAED,oDAEC;AAED,4CAOC;AAUD,gCASC;AAED,8BAMC;AAED,4CA0BC;AAED,0BAgBC;AApJD,+CAAkC;AAClC,mDAA6C;AAC7C,mCAA0C;AAC1C,iDAAwD;AACxD,2BAAmC;AACnC,qCAA8B;AAE9B,6BAA4B;AAC5B,wCAA0C;AAO1C,KAAK,UAAU,UAAU,CAAI,UAAkB,EAAE,OAA0B;IACzE,MAAM,IAAI,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC,CAAA;IAClD,IAAI,MAAW,CAAA;IACf,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;QAClE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;IACvC,CAAC;SAAM,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QACpG,MAAM,IAAI,GAAG,MAAM,oBAAoB,CAAC,aAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,cAAc,CAAC,EAAE,MAAM,CAAC,CAAC,CAAA;QACtG,MAAM,UAAU,GAAG,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAA;QAC/D,MAAM,GAAG,MAAM,IAAA,uBAAa,EAAC,UAAU,EAAE,UAAU,CAAC,CAAA;QACpD,IAAI,MAAM,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;YAC3B,MAAM,GAAG,MAAM,CAAC,OAAO,CAAA;QACzB,CAAC;QACD,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE,CAAC;YACjC,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;QAC1B,CAAC;QACD,MAAM,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;IACxC,CAAC;SAAM,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;QACtC,oJAAoJ;QACpJ,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAA;QACjC,OAAO,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,EAAE,EAAE;YACxB,kBAAG,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,EAAE,0BAA0B,CAAC,CAAA;QACjD,CAAC,CAAA;QACD,MAAM,GAAG,IAAA,6BAAY,EAAC,UAAU,CAAC,CAAA;QACjC,OAAO,CAAC,GAAG,GAAG,aAAa,CAAA;QAE3B,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE,CAAC;YACjC,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;QAC1B,CAAC;QACD,MAAM,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;IACxC,CAAC;SAAM,IAAI,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;QACxC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;IACtC,CAAC;SAAM,CAAC;QACN,MAAM,GAAG,IAAA,cAAI,EAAC,IAAI,CAAC,CAAA;IACrB,CAAC;IACD,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,CAAA;AAC/B,CAAC;AAEM,KAAK,UAAU,iBAAiB,CAAI,OAA0B;IACnE,MAAM,MAAM,GAAG,OAAO,CAAC,cAAc,CAAA;IACrC,KAAK,MAAM,UAAU,IAAI,CAAC,GAAG,MAAM,MAAM,EAAE,GAAG,MAAM,OAAO,EAAE,GAAG,MAAM,OAAO,EAAE,GAAG,MAAM,QAAQ,EAAE,GAAG,MAAM,OAAO,EAAE,GAAG,MAAM,KAAK,EAAE,GAAG,MAAM,MAAM,EAAE,GAAG,MAAM,KAAK,CAAC,EAAE,CAAC;QACrK,MAAM,IAAI,GAAG,MAAM,oBAAoB,CAAC,UAAU,CAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,OAAO,CAAC,CAAC,CAAA;QAC1G,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACjB,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAED,SAAgB,oBAAoB,CAAI,OAAmB;IACzD,OAAO,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;AACxC,CAAC;AAED,SAAgB,gBAAgB,CAAI,OAAmB,EAAE,aAAgB;IACvE,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;QACvB,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAChD,OAAO,aAAa,CAAA;QACtB,CAAC;QACD,MAAM,CAAC,CAAA;IACT,CAAC,CAAC,CAAA;AACJ,CAAC;AAUM,KAAK,UAAU,UAAU,CAAI,OAA0B;IAC5D,IAAI,eAAe,GAAG,OAAO,CAAC,eAAe,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,OAAO,CAAC,eAAe,CAAC,KAAK,CAAA;IAClG,IAAI,eAAe,IAAI,IAAI,EAAE,CAAC;QAC5B,MAAM,IAAI,GAAG,MAAM,oBAAoB,CAAC,aAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,cAAc,CAAC,EAAE,MAAM,CAAC,CAAC,CAAA;QAC3G,eAAe,GAAG,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;IAC1D,CAAC;IAED,MAAM,IAAI,GAAM,eAAe,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;IACpF,OAAO,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,iBAAiB,CAAI,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,CAAA;AAC1F,CAAC;AAED,SAAgB,SAAS,CAAI,OAA0B,EAAE,UAA0B;IACjF,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;QACvB,OAAO,UAAU,CAAI,OAAO,CAAC,CAAA;IAC/B,CAAC;SAAM,CAAC;QACN,OAAO,UAAU,CAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,OAAO,CAAC,CAAA;IAC7E,CAAC;AACH,CAAC;AAEM,KAAK,UAAU,gBAAgB,CAAI,OAA0B,EAAE,IAAY;IAChF,IAAI,UAA+B,CAAA;IACnC,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;QAC7B,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;QACrC,UAAU,GAAG,IAAI,CAAA;IACnB,CAAC;IAED,IAAI,YAAY,GAAG,MAAM,oBAAoB,CAAC,UAAU,CAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,CAAA;IAC7G,IAAI,YAAY,IAAI,IAAI,IAAI,UAAU,KAAK,IAAI,EAAE,CAAC;QAChD,IAAI,QAAQ,GAAkB,IAAI,CAAA;QAClC,IAAI,CAAC;YACH,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QAClC,CAAC;QAAC,OAAO,EAAE,EAAE,CAAC;YACZ,SAAS;QACX,CAAC;QAED,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,YAAY,GAAG,MAAM,UAAU,CAAI,QAAQ,EAAE,OAAO,CAAC,CAAA;QACvD,CAAC;IACH,CAAC;IAED,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;QACzB,MAAM,IAAI,KAAK,CAAC,mCAAmC,IAAI,EAAE,CAAC,CAAA;IAC5D,CAAC;IAED,OAAO,YAAY,CAAA;AACrB,CAAC;AAEM,KAAK,UAAU,OAAO,CAAC,OAAe;IAC3C,MAAM,IAAI,GAAG,MAAM,oBAAoB,CAAC,aAAE,CAAC,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAA;IACrE,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;QACjB,OAAO,IAAI,CAAA;IACb,CAAC;IAED,MAAM,MAAM,GAAG,IAAA,cAAQ,EAAmB,IAAI,CAAC,CAAA;IAE/C,kBAAG,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,EAAE,uBAAuB,CAAC,CAAA;IAC9C,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;QAC9C,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC;YAC5D,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;QAC1B,CAAC;IACH,CAAC,CAAC,CAAA;IACF,IAAA,sBAAM,EAAC,EAAE,MAAM,EAAE,CAAC,CAAA;IAClB,OAAO,MAAM,CAAA;AACf,CAAC", "sourcesContent": ["import { log } from \"builder-util\"\nimport { loadTsConfig } from \"config-file-ts\"\nimport { parse as parseEnv } from \"dotenv\"\nimport { DotenvParseInput, expand } from \"dotenv-expand\"\nimport { promises as fs } from \"fs\"\nimport { load } from \"js-yaml\"\nimport { Lazy } from \"lazy-val\"\nimport * as path from \"path\"\nimport { resolveModule } from \"../resolve\"\n\nexport interface ReadConfigResult<T> {\n  readonly result: T\n  readonly configFile: string | null\n}\n\nasync function readConfig<T>(configFile: string, request: ReadConfigRequest): Promise<ReadConfigResult<T>> {\n  const data = await fs.readFile(configFile, \"utf8\")\n  let result: any\n  if (configFile.endsWith(\".json5\") || configFile.endsWith(\".json\")) {\n    result = require(\"json5\").parse(data)\n  } else if (configFile.endsWith(\".js\") || configFile.endsWith(\".cjs\") || configFile.endsWith(\".mjs\")) {\n    const json = await orNullIfFileNotExist(fs.readFile(path.join(process.cwd(), \"package.json\"), \"utf8\"))\n    const moduleType = json === null ? null : JSON.parse(json).type\n    result = await resolveModule(moduleType, configFile)\n    if (result.default != null) {\n      result = result.default\n    }\n    if (typeof result === \"function\") {\n      result = result(request)\n    }\n    result = await Promise.resolve(result)\n  } else if (configFile.endsWith(\".ts\")) {\n    // override logger temporarily to clean up console (config-file-ts does some internal logging that blogs up the default electron-builder log format)\n    const consoleLogger = console.log\n    console.log = (...args) => {\n      log.debug({ args }, \"executing config-file-ts\")\n    }\n    result = loadTsConfig(configFile)\n    console.log = consoleLogger\n\n    if (typeof result === \"function\") {\n      result = result(request)\n    }\n    result = await Promise.resolve(result)\n  } else if (configFile.endsWith(\".toml\")) {\n    result = require(\"toml\").parse(data)\n  } else {\n    result = load(data)\n  }\n  return { result, configFile }\n}\n\nexport async function findAndReadConfig<T>(request: ReadConfigRequest): Promise<ReadConfigResult<T> | null> {\n  const prefix = request.configFilename\n  for (const configFile of [`${prefix}.yml`, `${prefix}.yaml`, `${prefix}.json`, `${prefix}.json5`, `${prefix}.toml`, `${prefix}.js`, `${prefix}.cjs`, `${prefix}.ts`]) {\n    const data = await orNullIfFileNotExist(readConfig<T>(path.join(request.projectDir, configFile), request))\n    if (data != null) {\n      return data\n    }\n  }\n\n  return null\n}\n\nexport function orNullIfFileNotExist<T>(promise: Promise<T>): Promise<T | null> {\n  return orIfFileNotExist(promise, null)\n}\n\nexport function orIfFileNotExist<T>(promise: Promise<T>, fallbackValue: T): Promise<T> {\n  return promise.catch(e => {\n    if (e.code === \"ENOENT\" || e.code === \"ENOTDIR\") {\n      return fallbackValue\n    }\n    throw e\n  })\n}\n\nexport interface ReadConfigRequest {\n  packageKey: string\n  configFilename: string\n\n  projectDir: string\n  packageMetadata: Lazy<Record<string, any> | null> | null\n}\n\nexport async function loadConfig<T>(request: ReadConfigRequest): Promise<ReadConfigResult<T> | null> {\n  let packageMetadata = request.packageMetadata == null ? null : await request.packageMetadata.value\n  if (packageMetadata == null) {\n    const json = await orNullIfFileNotExist(fs.readFile(path.join(request.projectDir, \"package.json\"), \"utf8\"))\n    packageMetadata = json == null ? null : JSON.parse(json)\n  }\n\n  const data: T = packageMetadata == null ? null : packageMetadata[request.packageKey]\n  return data == null ? findAndReadConfig<T>(request) : { result: data, configFile: null }\n}\n\nexport function getConfig<T>(request: ReadConfigRequest, configPath?: string | null): Promise<ReadConfigResult<T> | null> {\n  if (configPath == null) {\n    return loadConfig<T>(request)\n  } else {\n    return readConfig<T>(path.resolve(request.projectDir, configPath), request)\n  }\n}\n\nexport async function loadParentConfig<T>(request: ReadConfigRequest, spec: string): Promise<ReadConfigResult<T>> {\n  let isFileSpec: boolean | undefined\n  if (spec.startsWith(\"file:\")) {\n    spec = spec.substring(\"file:\".length)\n    isFileSpec = true\n  }\n\n  let parentConfig = await orNullIfFileNotExist(readConfig<T>(path.resolve(request.projectDir, spec), request))\n  if (parentConfig == null && isFileSpec !== true) {\n    let resolved: string | null = null\n    try {\n      resolved = require.resolve(spec)\n    } catch (_e) {\n      // ignore\n    }\n\n    if (resolved != null) {\n      parentConfig = await readConfig<T>(resolved, request)\n    }\n  }\n\n  if (parentConfig == null) {\n    throw new Error(`Cannot find parent config file: ${spec}`)\n  }\n\n  return parentConfig\n}\n\nexport async function loadEnv(envFile: string) {\n  const data = await orNullIfFileNotExist(fs.readFile(envFile, \"utf8\"))\n  if (data == null) {\n    return null\n  }\n\n  const parsed = parseEnv<DotenvParseInput>(data)\n\n  log.info({ envFile }, \"injecting environment\")\n  Object.entries(parsed).forEach(([key, value]) => {\n    if (!Object.prototype.hasOwnProperty.call(process.env, key)) {\n      process.env[key] = value\n    }\n  })\n  expand({ parsed })\n  return parsed\n}\n"]}