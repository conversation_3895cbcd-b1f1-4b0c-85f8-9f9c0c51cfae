const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

class SystemStateAnalyzer {
  constructor() {
    this.platform = process.platform;
    this.criticalProcesses = [
      'code.exe', 'Code.exe', 'code', // VS Code
      'postgres', 'postgresql', 'pg_ctl', // PostgreSQL
      'mysql', 'mysqld', 'mariadb', // MySQL/MariaDB
      'sqlite3', 'sqlite', // SQLite
      'node.exe', 'node', // Node.js
      'npm.exe', 'npm', // NPM
      'git.exe', 'git', // Git
    ];
  }

  async analyzeSystemState(progressCallback = null) {
    const state = {
      timestamp: new Date(),
      platform: this.platform,
      processes: {
        running: [],
        critical: [],
        databases: []
      },
      connections: {
        database: [],
        network: []
      },
      filesystem: {
        locks: [],
        permissions: [],
        diskSpace: {}
      },
      vscode: {
        running: false,
        instances: [],
        workspaces: []
      },
      databases: {
        postgresql: { running: false, connections: 0 },
        mysql: { running: false, connections: 0 },
        sqlite: { openFiles: [] }
      },
      risks: [],
      recommendations: [],
      safeToOperate: true
    };

    try {
      if (progressCallback) progressCallback({ step: 'Analyzing running processes...', progress: 10 });
      await this.analyzeRunningProcesses(state);

      if (progressCallback) progressCallback({ step: 'Checking VS Code instances...', progress: 25 });
      await this.analyzeVSCodeState(state);

      if (progressCallback) progressCallback({ step: 'Analyzing database connections...', progress: 40 });
      await this.analyzeDatabaseConnections(state);

      if (progressCallback) progressCallback({ step: 'Checking file system locks...', progress: 55 });
      await this.analyzeFileSystemState(state);

      if (progressCallback) progressCallback({ step: 'Checking network connections...', progress: 70 });
      await this.analyzeNetworkConnections(state);

      if (progressCallback) progressCallback({ step: 'Analyzing disk space...', progress: 85 });
      await this.analyzeDiskSpace(state);

      if (progressCallback) progressCallback({ step: 'Generating recommendations...', progress: 95 });
      this.generateRisksAndRecommendations(state);

      if (progressCallback) progressCallback({ step: 'Analysis complete!', progress: 100 });

      return state;
    } catch (error) {
      throw new Error(`System state analysis failed: ${error.message}`);
    }
  }

  async analyzeRunningProcesses(state) {
    try {
      const processes = await this.getRunningProcesses();
      state.processes.running = processes;

      // Identify critical processes
      state.processes.critical = processes.filter(proc => 
        this.criticalProcesses.some(critical => 
          proc.name.toLowerCase().includes(critical.toLowerCase())
        )
      );

      // Identify database processes
      state.processes.databases = processes.filter(proc => 
        ['postgres', 'mysql', 'sqlite', 'mariadb'].some(db => 
          proc.name.toLowerCase().includes(db)
        )
      );

    } catch (error) {
      console.error('Failed to analyze processes:', error);
    }
  }

  async analyzeVSCodeState(state) {
    try {
      // Check for running VS Code instances
      const vscodeProcesses = state.processes.running.filter(proc => 
        proc.name.toLowerCase().includes('code')
      );

      state.vscode.running = vscodeProcesses.length > 0;
      state.vscode.instances = vscodeProcesses;

      // Try to detect open workspaces
      if (state.vscode.running) {
        const workspaces = await this.detectVSCodeWorkspaces();
        state.vscode.workspaces = workspaces;
      }

    } catch (error) {
      console.error('Failed to analyze VS Code state:', error);
    }
  }

  async analyzeDatabaseConnections(state) {
    try {
      // Check PostgreSQL
      const pgConnections = await this.checkPostgreSQLConnections();
      state.databases.postgresql = pgConnections;

      // Check MySQL
      const mysqlConnections = await this.checkMySQLConnections();
      state.databases.mysql = mysqlConnections;

      // Check for open SQLite files
      const sqliteFiles = await this.checkOpenSQLiteFiles();
      state.databases.sqlite.openFiles = sqliteFiles;

    } catch (error) {
      console.error('Failed to analyze database connections:', error);
    }
  }

  async analyzeFileSystemState(state) {
    try {
      // Check for file locks
      const locks = await this.checkFileLocks();
      state.filesystem.locks = locks;

      // Check permissions for common directories
      const permissions = await this.checkDirectoryPermissions();
      state.filesystem.permissions = permissions;

    } catch (error) {
      console.error('Failed to analyze filesystem state:', error);
    }
  }

  async analyzeNetworkConnections(state) {
    try {
      // Check for database network connections
      const dbConnections = await this.checkDatabaseNetworkConnections();
      state.connections.database = dbConnections;

      // Check for other relevant network connections
      const networkConnections = await this.checkNetworkConnections();
      state.connections.network = networkConnections;

    } catch (error) {
      console.error('Failed to analyze network connections:', error);
    }
  }

  async analyzeDiskSpace(state) {
    try {
      const diskInfo = await this.getDiskSpaceInfo();
      state.filesystem.diskSpace = diskInfo;
    } catch (error) {
      console.error('Failed to analyze disk space:', error);
    }
  }

  generateRisksAndRecommendations(state) {
    const risks = [];
    const recommendations = [];

    // Check if VS Code is running
    if (state.vscode.running) {
      risks.push({
        level: 'medium',
        category: 'application',
        message: 'VS Code is currently running',
        impact: 'Database operations may fail or cause data corruption',
        processes: state.vscode.instances.map(p => p.name)
      });
      recommendations.push({
        action: 'Close VS Code before proceeding',
        reason: 'Prevents file locks and ensures clean database operations',
        priority: 'high'
      });
    }

    // Check database processes
    if (state.databases.postgresql.running) {
      risks.push({
        level: 'high',
        category: 'database',
        message: 'PostgreSQL is running with active connections',
        impact: 'Database cleanup operations will fail',
        connections: state.databases.postgresql.connections
      });
      recommendations.push({
        action: 'Stop PostgreSQL service or close connections',
        reason: 'Required for safe database operations',
        priority: 'critical'
      });
    }

    if (state.databases.mysql.running) {
      risks.push({
        level: 'high',
        category: 'database',
        message: 'MySQL is running with active connections',
        impact: 'Database cleanup operations will fail',
        connections: state.databases.mysql.connections
      });
      recommendations.push({
        action: 'Stop MySQL service or close connections',
        reason: 'Required for safe database operations',
        priority: 'critical'
      });
    }

    // Check disk space
    if (state.filesystem.diskSpace.freePercentage < 10) {
      risks.push({
        level: 'high',
        category: 'storage',
        message: 'Low disk space detected',
        impact: 'Backup operations may fail',
        freeSpace: state.filesystem.diskSpace.free
      });
      recommendations.push({
        action: 'Free up disk space before proceeding',
        reason: 'Ensures backup operations can complete successfully',
        priority: 'high'
      });
    }

    // Check file locks
    if (state.filesystem.locks.length > 0) {
      risks.push({
        level: 'medium',
        category: 'filesystem',
        message: 'File locks detected',
        impact: 'Some files may not be accessible for cleanup',
        lockedFiles: state.filesystem.locks
      });
      recommendations.push({
        action: 'Close applications that may be locking files',
        reason: 'Ensures all files can be safely accessed',
        priority: 'medium'
      });
    }

    // Determine if it's safe to operate
    const criticalRisks = risks.filter(risk => risk.level === 'high' || risk.level === 'critical');
    state.safeToOperate = criticalRisks.length === 0;

    state.risks = risks;
    state.recommendations = recommendations;
  }

  async getRunningProcesses() {
    return new Promise((resolve, reject) => {
      const command = this.platform === 'win32' 
        ? 'wmic process get Name,ProcessId,CommandLine /format:csv'
        : 'ps aux';

      exec(command, (error, stdout, stderr) => {
        if (error) {
          reject(error);
          return;
        }

        try {
          const processes = this.parseProcessList(stdout);
          resolve(processes);
        } catch (parseError) {
          reject(parseError);
        }
      });
    });
  }

  parseProcessList(output) {
    const processes = [];
    const lines = output.split('\n').filter(line => line.trim());

    if (this.platform === 'win32') {
      // Parse Windows WMIC output
      for (let i = 1; i < lines.length; i++) { // Skip header
        const parts = lines[i].split(',');
        if (parts.length >= 3 && parts[1]) {
          processes.push({
            name: parts[1].trim(),
            pid: parseInt(parts[2]) || 0,
            command: parts[0] || ''
          });
        }
      }
    } else {
      // Parse Unix ps output
      for (let i = 1; i < lines.length; i++) { // Skip header
        const parts = lines[i].trim().split(/\s+/);
        if (parts.length >= 11) {
          processes.push({
            name: parts[10],
            pid: parseInt(parts[1]) || 0,
            command: parts.slice(10).join(' ')
          });
        }
      }
    }

    return processes;
  }

  async detectVSCodeWorkspaces() {
    // This is a simplified implementation
    // In practice, you might want to check VS Code's workspace files
    return [];
  }

  async checkPostgreSQLConnections() {
    try {
      return new Promise((resolve) => {
        const child = spawn('psql', ['-c', 'SELECT count(*) FROM pg_stat_activity;'], { stdio: 'pipe' });
        
        let output = '';
        child.stdout.on('data', (data) => {
          output += data.toString();
        });

        child.on('close', (code) => {
          if (code === 0) {
            const connections = parseInt(output.match(/\d+/)?.[0]) || 0;
            resolve({ running: true, connections });
          } else {
            resolve({ running: false, connections: 0 });
          }
        });

        child.on('error', () => {
          resolve({ running: false, connections: 0 });
        });
      });
    } catch (error) {
      return { running: false, connections: 0 };
    }
  }

  async checkMySQLConnections() {
    try {
      return new Promise((resolve) => {
        const child = spawn('mysql', ['-e', 'SHOW PROCESSLIST;'], { stdio: 'pipe' });
        
        let output = '';
        child.stdout.on('data', (data) => {
          output += data.toString();
        });

        child.on('close', (code) => {
          if (code === 0) {
            const connections = (output.match(/\n/g) || []).length - 1; // Subtract header
            resolve({ running: true, connections: Math.max(0, connections) });
          } else {
            resolve({ running: false, connections: 0 });
          }
        });

        child.on('error', () => {
          resolve({ running: false, connections: 0 });
        });
      });
    } catch (error) {
      return { running: false, connections: 0 };
    }
  }

  async checkOpenSQLiteFiles() {
    // This would require platform-specific tools like lsof on Unix or handle.exe on Windows
    // For now, return empty array
    return [];
  }

  async checkFileLocks() {
    // Platform-specific implementation would go here
    return [];
  }

  async checkDirectoryPermissions() {
    const directories = [
      os.homedir(),
      os.tmpdir(),
      process.cwd()
    ];

    const permissions = [];
    for (const dir of directories) {
      try {
        await fs.promises.access(dir, fs.constants.R_OK | fs.constants.W_OK);
        permissions.push({ path: dir, readable: true, writable: true });
      } catch (error) {
        permissions.push({ path: dir, readable: false, writable: false, error: error.message });
      }
    }

    return permissions;
  }

  async checkDatabaseNetworkConnections() {
    // Check for connections on common database ports
    const ports = [5432, 3306, 1433, 1521]; // PostgreSQL, MySQL, SQL Server, Oracle
    const connections = [];

    // This would require netstat or similar tools
    // For now, return empty array
    return connections;
  }

  async checkNetworkConnections() {
    return [];
  }

  async getDiskSpaceInfo() {
    try {
      const stats = await fs.promises.statfs ? fs.promises.statfs('.') : null;
      if (stats) {
        const total = stats.blocks * stats.bsize;
        const free = stats.bavail * stats.bsize;
        const used = total - free;
        
        return {
          total,
          free,
          used,
          freePercentage: (free / total) * 100
        };
      }
    } catch (error) {
      console.error('Failed to get disk space info:', error);
    }

    return {
      total: 0,
      free: 0,
      used: 0,
      freePercentage: 100
    };
  }
}

module.exports = SystemStateAnalyzer;
