"""
VS Code telemetry ID modifier module
"""

import os
import sys
import json
from pathlib import Path
from typing import Dict, Any, Optional

from .utils import (
    info, success, error, warning,
    get_vscode_paths, backup_file,
    generate_machine_id, generate_device_id
)

def preview_id_modification(silent: bool = False) -> Dict[str, Any]:
    """
    Preview what telemetry IDs will be modified

    Args:
        silent: If True, suppress info messages

    Returns:
        Dictionary containing preview information
    """
    if not silent:
        info("Scanning VS Code storage for ID modification preview...")

    # Get VS Code paths
    paths = get_vscode_paths()
    storage_json = paths["storage_json"]

    preview_data = {
        "storage_path": str(storage_json),
        "storage_exists": storage_json.exists(),
        "storage_size": 0,
        "current_machine_id": None,
        "current_device_id": None,
        "new_machine_id": None,
        "new_device_id": None,
        "backup_location": f"{storage_json}.backup",
        "safe_to_modify": False,
        "warnings": [],
        "recommendations": [],
        "other_telemetry_keys": []
    }

    if not storage_json.exists():
        preview_data["warnings"].append(f"VS Code storage.json not found at: {storage_json}")
        return preview_data

    # Get file size
    try:
        preview_data["storage_size"] = storage_json.stat().st_size
    except Exception as e:
        preview_data["warnings"].append(f"Could not get storage file size: {e}")

    # Generate new IDs for preview
    preview_data["new_machine_id"] = generate_machine_id()
    preview_data["new_device_id"] = generate_device_id()

    # Read current storage
    try:
        with open(storage_json, 'r', encoding='utf-8') as f:
            content = json.load(f)

        # Get current IDs
        preview_data["current_machine_id"] = content.get("telemetry.machineId", "Not set")
        preview_data["current_device_id"] = content.get("telemetry.devDeviceId", "Not set")

        # Find other telemetry-related keys
        telemetry_keys = [key for key in content.keys() if "telemetry" in key.lower()]
        preview_data["other_telemetry_keys"] = [
            {"key": key, "value": str(content[key])[:50] + "..." if len(str(content[key])) > 50 else str(content[key])}
            for key in telemetry_keys if key not in ["telemetry.machineId", "telemetry.devDeviceId"]
        ]

        preview_data["safe_to_modify"] = True

        # Add recommendations
        if preview_data["current_machine_id"] == "Not set":
            preview_data["recommendations"].append("No existing machine ID found. A new one will be created.")
        else:
            preview_data["recommendations"].append("Existing machine ID will be replaced with a new random value.")

        if preview_data["current_device_id"] == "Not set":
            preview_data["recommendations"].append("No existing device ID found. A new one will be created.")
        else:
            preview_data["recommendations"].append("Existing device ID will be replaced with a new random value.")

        preview_data["recommendations"].append("A backup will be created before making any changes.")
        preview_data["recommendations"].append("You may need to restart VS Code for changes to take effect.")

    except json.JSONDecodeError:
        preview_data["warnings"].append("The storage file is not valid JSON")
        preview_data["safe_to_modify"] = False
    except Exception as e:
        preview_data["warnings"].append(f"Unexpected error reading storage file: {e}")
        preview_data["safe_to_modify"] = False

    return preview_data

def modify_telemetry_ids() -> bool:
    """
    Modify telemetry IDs in VS Code storage.json file
    
    Returns:
        True if successful, False otherwise
    """
    info("Starting VS Code telemetry ID modification")
    
    # Get VS Code paths
    paths = get_vscode_paths()
    storage_json = paths["storage_json"]
    
    if not storage_json.exists():
        warning(f"VS Code storage.json not found at: {storage_json}")
        return False
    
    info(f"Found storage.json at: {storage_json}")
    
    # Create backup
    backup_path = backup_file(storage_json)
    
    # Generate new IDs
    info("Generating new telemetry IDs...")
    machine_id = generate_machine_id()
    device_id = generate_device_id()
    
    # Read the current file
    try:
        with open(storage_json, 'r', encoding='utf-8') as f:
            content = json.load(f)
        
        # Update the values
        content["telemetry.machineId"] = machine_id
        content["telemetry.devDeviceId"] = device_id
        
        # Write the updated content back to the file
        with open(storage_json, 'w', encoding='utf-8') as f:
            json.dump(content, f, indent=2)
        
        success("Successfully updated telemetry IDs")
        info(f"New machineId: {machine_id}")
        info(f"New devDeviceId: {device_id}")
        info("You may need to restart VS Code for changes to take effect")
        
        return True
        
    except json.JSONDecodeError:
        error("The storage file is not valid JSON")
        return False
    except Exception as e:
        error(f"Unexpected error: {e}")
        return False
