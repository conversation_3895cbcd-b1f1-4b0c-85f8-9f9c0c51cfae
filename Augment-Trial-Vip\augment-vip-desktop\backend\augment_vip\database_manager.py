"""
Enhanced Database Manager with cross-platform support
Supports SQLite, PostgreSQL, MySQL with selective operations
"""

import os
import sys
import sqlite3
import json
import shutil
import gzip
import platform
from pathlib import Path
from typing import List, Dict, Any, Optional, Set, Tuple
from datetime import datetime
import re

try:
    import psycopg2
    POSTGRESQL_AVAILABLE = True
except ImportError:
    POSTGRESQL_AVAILABLE = False

try:
    import mysql.connector
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False

from .utils import info, success, error, warning, get_vscode_paths, backup_file

class DatabaseManager:
    """Enhanced database manager with multi-database support"""
    
    def __init__(self):
        self.platform = platform.system().lower()
        self.supported_databases = ['sqlite']
        
        if POSTGRESQL_AVAILABLE:
            self.supported_databases.append('postgresql')
        if MYSQL_AVAILABLE:
            self.supported_databases.append('mysql')
    
    def get_platform_specific_paths(self) -> Dict[str, List[Path]]:
        """Get database paths for current platform"""
        paths = {
            'vscode': [],
            'jetbrains': [],
            'sublime': [],
            'chrome': [],
            'firefox': []
        }
        
        if self.platform == 'windows':
            base_paths = {
                'appdata': Path(os.environ.get('APPDATA', '')),
                'localappdata': Path(os.environ.get('LOCALAPPDATA', '')),
                'userprofile': Path(os.environ.get('USERPROFILE', ''))
            }
            
            # VS Code paths
            paths['vscode'].extend([
                base_paths['appdata'] / 'Code' / 'User' / 'globalStorage',
                base_paths['appdata'] / 'Code' / 'logs',
                base_paths['appdata'] / 'Code' / 'CachedExtensions'
            ])
            
            # JetBrains paths
            jetbrains_base = base_paths['appdata'] / 'JetBrains'
            if jetbrains_base.exists():
                for ide_dir in jetbrains_base.iterdir():
                    if ide_dir.is_dir():
                        paths['jetbrains'].append(ide_dir)
            
            # Chrome paths
            paths['chrome'].extend([
                base_paths['localappdata'] / 'Google' / 'Chrome' / 'User Data',
                base_paths['appdata'] / 'Google' / 'Chrome' / 'User Data'
            ])
            
        elif self.platform == 'darwin':  # macOS
            home = Path.home()
            
            # VS Code paths
            paths['vscode'].extend([
                home / 'Library' / 'Application Support' / 'Code' / 'User' / 'globalStorage',
                home / 'Library' / 'Application Support' / 'Code' / 'logs',
                home / 'Library' / 'Application Support' / 'Code' / 'CachedExtensions'
            ])
            
            # JetBrains paths
            jetbrains_base = home / 'Library' / 'Application Support' / 'JetBrains'
            if jetbrains_base.exists():
                for ide_dir in jetbrains_base.iterdir():
                    if ide_dir.is_dir():
                        paths['jetbrains'].append(ide_dir)
            
            # Chrome paths
            paths['chrome'].append(
                home / 'Library' / 'Application Support' / 'Google' / 'Chrome'
            )
            
        return paths
    
    def scan_all_databases(self, include_apps: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Comprehensive database scan across multiple applications
        
        Args:
            include_apps: List of apps to scan ['vscode', 'jetbrains', 'chrome', etc.]
        
        Returns:
            Dictionary with scan results
        """
        if include_apps is None:
            include_apps = ['vscode']
        
        scan_results = {
            'platform': self.platform,
            'scan_time': datetime.now().isoformat(),
            'databases': {
                'sqlite': [],
                'postgresql': [],
                'mysql': []
            },
            'applications': {},
            'total_size': 0,
            'total_files': 0,
            'warnings': [],
            'recommendations': []
        }
        
        platform_paths = self.get_platform_specific_paths()
        
        for app in include_apps:
            if app in platform_paths:
                app_results = self._scan_application_databases(app, platform_paths[app])
                scan_results['applications'][app] = app_results
                
                # Aggregate results
                for db_type in ['sqlite', 'postgresql', 'mysql']:
                    scan_results['databases'][db_type].extend(app_results.get(db_type, []))
        
        # Calculate totals
        for db_list in scan_results['databases'].values():
            for db in db_list:
                scan_results['total_size'] += db.get('size', 0)
                scan_results['total_files'] += 1
        
        # Add recommendations
        self._add_scan_recommendations(scan_results)
        
        return scan_results
    
    def _scan_application_databases(self, app_name: str, paths: List[Path]) -> Dict[str, Any]:
        """Scan databases for a specific application"""
        app_results = {
            'sqlite': [],
            'postgresql': [],
            'mysql': [],
            'scan_errors': []
        }
        
        for path in paths:
            if not path.exists():
                continue
                
            try:
                # Scan for SQLite databases
                sqlite_files = self._find_sqlite_files(path)
                for sqlite_file in sqlite_files:
                    db_info = self._analyze_sqlite_database(sqlite_file, app_name)
                    if db_info:
                        app_results['sqlite'].append(db_info)
                
                # Scan for PostgreSQL connections (if available)
                if POSTGRESQL_AVAILABLE and app_name == 'vscode':
                    pg_connections = self._find_postgresql_connections(path)
                    app_results['postgresql'].extend(pg_connections)
                
                # Scan for MySQL connections (if available)
                if MYSQL_AVAILABLE and app_name == 'vscode':
                    mysql_connections = self._find_mysql_connections(path)
                    app_results['mysql'].extend(mysql_connections)
                    
            except Exception as e:
                app_results['scan_errors'].append(f"Error scanning {path}: {str(e)}")
        
        return app_results
    
    def _find_sqlite_files(self, directory: Path) -> List[Path]:
        """Find SQLite database files in directory"""
        sqlite_files = []
        sqlite_extensions = ['.db', '.sqlite', '.sqlite3', '.vscdb']
        
        try:
            for root, dirs, files in os.walk(directory):
                # Skip hidden directories and common non-database directories
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', 'cache']]
                
                for file in files:
                    file_path = Path(root) / file
                    
                    # Check by extension
                    if any(file.lower().endswith(ext) for ext in sqlite_extensions):
                        sqlite_files.append(file_path)
                    
                    # Check by file signature for files without extension
                    elif self._is_sqlite_file(file_path):
                        sqlite_files.append(file_path)
                        
        except PermissionError:
            # Skip directories we can't access
            pass
        except Exception as e:
            warning(f"Error scanning directory {directory}: {e}")
        
        return sqlite_files
    
    def _is_sqlite_file(self, file_path: Path) -> bool:
        """Check if file is SQLite database by reading header"""
        try:
            if file_path.stat().st_size < 16:
                return False
                
            with open(file_path, 'rb') as f:
                header = f.read(16)
                return header.startswith(b'SQLite format 3\x00')
        except:
            return False
    
    def _analyze_sqlite_database(self, db_path: Path, app_name: str) -> Optional[Dict[str, Any]]:
        """Analyze SQLite database and extract metadata"""
        try:
            db_info = {
                'path': str(db_path),
                'name': db_path.name,
                'app': app_name,
                'type': 'sqlite',
                'size': db_path.stat().st_size,
                'modified': db_path.stat().st_mtime,
                'tables': [],
                'augment_entries': [],
                'total_entries': 0,
                'can_clean': True,
                'risk_level': 'low',
                'backup_recommended': True
            }
            
            # Connect and analyze
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # Get table list
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            db_info['tables'] = tables
            
            # Analyze each table for Augment-related content
            augment_patterns = [
                r'.*augment.*',
                r'.*Augment.*',
                r'.*AUGMENT.*'
            ]
            
            total_entries = 0
            augment_entries = []
            
            for table in tables:
                try:
                    # Get total entries in table
                    cursor.execute(f"SELECT COUNT(*) FROM `{table}`")
                    table_count = cursor.fetchone()[0]
                    total_entries += table_count
                    
                    # Check for Augment-related entries
                    cursor.execute(f"PRAGMA table_info(`{table}`)")
                    columns = [col[1] for col in cursor.fetchall()]
                    
                    for column in columns:
                        if column in ['key', 'name', 'id', 'value', 'data']:
                            for pattern in augment_patterns:
                                try:
                                    cursor.execute(f"SELECT COUNT(*) FROM `{table}` WHERE `{column}` LIKE ?", (f'%{pattern[2:-2]}%',))
                                    match_count = cursor.fetchone()[0]
                                    if match_count > 0:
                                        # Get sample entries
                                        cursor.execute(f"SELECT `{column}` FROM `{table}` WHERE `{column}` LIKE ? LIMIT 5", (f'%{pattern[2:-2]}%',))
                                        samples = [row[0] for row in cursor.fetchall()]
                                        
                                        augment_entries.append({
                                            'table': table,
                                            'column': column,
                                            'count': match_count,
                                            'samples': samples
                                        })
                                except sqlite3.Error:
                                    continue
                                    
                except sqlite3.Error as e:
                    warning(f"Error analyzing table {table}: {e}")
                    continue
            
            db_info['total_entries'] = total_entries
            db_info['augment_entries'] = augment_entries
            
            # Determine risk level
            if len(augment_entries) == 0:
                db_info['risk_level'] = 'none'
                db_info['can_clean'] = False
            elif any(entry['count'] > 100 for entry in augment_entries):
                db_info['risk_level'] = 'high'
            elif any(entry['count'] > 10 for entry in augment_entries):
                db_info['risk_level'] = 'medium'
            
            conn.close()
            return db_info
            
        except Exception as e:
            warning(f"Error analyzing database {db_path}: {e}")
            return None
    
    def _find_postgresql_connections(self, path: Path) -> List[Dict[str, Any]]:
        """Find PostgreSQL connection information"""
        # Placeholder for PostgreSQL connection discovery
        # This would scan for connection strings, config files, etc.
        return []
    
    def _find_mysql_connections(self, path: Path) -> List[Dict[str, Any]]:
        """Find MySQL connection information"""
        # Placeholder for MySQL connection discovery
        # This would scan for connection strings, config files, etc.
        return []
    
    def _add_scan_recommendations(self, scan_results: Dict[str, Any]) -> None:
        """Add recommendations based on scan results"""
        total_augment_entries = sum(
            len(db.get('augment_entries', []))
            for db_list in scan_results['databases'].values()
            for db in db_list
        )
        
        if total_augment_entries == 0:
            scan_results['recommendations'].append("No Augment-related entries found. System appears clean.")
        else:
            scan_results['recommendations'].append(f"Found {total_augment_entries} databases with Augment-related entries.")
            scan_results['recommendations'].append("Consider creating backups before cleaning operations.")
            
        if scan_results['total_size'] > 100 * 1024 * 1024:  # 100MB
            scan_results['recommendations'].append("Large database files detected. Cleaning may free significant space.")
            
        # Platform-specific recommendations
        if self.platform == 'windows':
            scan_results['recommendations'].append("Windows detected: Ensure VS Code is closed before operations.")
        elif self.platform == 'darwin':
            scan_results['recommendations'].append("macOS detected: May require elevated permissions for some operations.")

    def selective_clean(self,
                       databases: List[Dict[str, Any]],
                       patterns: List[str] = None,
                       exclude_patterns: List[str] = None,
                       dry_run: bool = False) -> Dict[str, Any]:
        """
        Perform selective cleaning with custom patterns

        Args:
            databases: List of database info dictionaries
            patterns: Patterns to match for cleaning (default: augment patterns)
            exclude_patterns: Patterns to exclude from cleaning
            dry_run: If True, simulate without making changes

        Returns:
            Dictionary with cleaning results
        """
        if patterns is None:
            patterns = ['augment', 'Augment', 'AUGMENT']

        if exclude_patterns is None:
            exclude_patterns = []

        cleaning_results = {
            'operation': 'selective_clean',
            'dry_run': dry_run,
            'start_time': datetime.now().isoformat(),
            'patterns_used': patterns,
            'exclude_patterns': exclude_patterns,
            'databases_processed': [],
            'total_entries_removed': 0,
            'total_size_freed': 0,
            'errors': [],
            'warnings': [],
            'backups_created': []
        }

        for db_info in databases:
            if not db_info.get('can_clean', False):
                cleaning_results['warnings'].append(f"Skipping {db_info['name']} - marked as unsafe to clean")
                continue

            try:
                db_result = self._clean_single_database(db_info, patterns, exclude_patterns, dry_run)
                cleaning_results['databases_processed'].append(db_result)
                cleaning_results['total_entries_removed'] += db_result.get('entries_removed', 0)
                cleaning_results['total_size_freed'] += db_result.get('size_freed', 0)

                if db_result.get('backup_path'):
                    cleaning_results['backups_created'].append(db_result['backup_path'])

            except Exception as e:
                error_msg = f"Error cleaning {db_info['name']}: {str(e)}"
                cleaning_results['errors'].append(error_msg)
                error(error_msg)

        cleaning_results['end_time'] = datetime.now().isoformat()
        return cleaning_results

    def _clean_single_database(self,
                              db_info: Dict[str, Any],
                              patterns: List[str],
                              exclude_patterns: List[str],
                              dry_run: bool) -> Dict[str, Any]:
        """Clean a single database with specified patterns"""
        db_result = {
            'database': db_info['name'],
            'path': db_info['path'],
            'type': db_info['type'],
            'entries_removed': 0,
            'size_before': db_info['size'],
            'size_after': 0,
            'size_freed': 0,
            'backup_path': None,
            'tables_affected': [],
            'success': False
        }

        if db_info['type'] == 'sqlite':
            return self._clean_sqlite_database(db_info, patterns, exclude_patterns, dry_run, db_result)
        elif db_info['type'] == 'postgresql':
            return self._clean_postgresql_database(db_info, patterns, exclude_patterns, dry_run, db_result)
        elif db_info['type'] == 'mysql':
            return self._clean_mysql_database(db_info, patterns, exclude_patterns, dry_run, db_result)
        else:
            raise ValueError(f"Unsupported database type: {db_info['type']}")

    def _clean_sqlite_database(self,
                              db_info: Dict[str, Any],
                              patterns: List[str],
                              exclude_patterns: List[str],
                              dry_run: bool,
                              db_result: Dict[str, Any]) -> Dict[str, Any]:
        """Clean SQLite database with selective patterns"""
        db_path = Path(db_info['path'])

        # Create backup first (unless dry run)
        if not dry_run:
            backup_path = self._create_versioned_backup(db_path)
            db_result['backup_path'] = str(backup_path)

        # Connect to database
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()

        try:
            # Get all tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]

            total_removed = 0

            for table in tables:
                try:
                    # Get table schema
                    cursor.execute(f"PRAGMA table_info(`{table}`)")
                    columns = [col[1] for col in cursor.fetchall()]

                    # Look for text columns that might contain our patterns
                    text_columns = []
                    for column in columns:
                        if column in ['key', 'name', 'id', 'value', 'data', 'content', 'text']:
                            text_columns.append(column)

                    if not text_columns:
                        continue

                    # Build WHERE clause for patterns
                    where_conditions = []
                    params = []

                    for column in text_columns:
                        for pattern in patterns:
                            # Check if pattern should be excluded
                            should_exclude = False
                            for exclude_pattern in exclude_patterns:
                                if exclude_pattern.lower() in pattern.lower():
                                    should_exclude = True
                                    break

                            if not should_exclude:
                                where_conditions.append(f"`{column}` LIKE ?")
                                params.append(f'%{pattern}%')

                    if not where_conditions:
                        continue

                    where_clause = " OR ".join(where_conditions)

                    # Count entries that would be removed
                    count_query = f"SELECT COUNT(*) FROM `{table}` WHERE {where_clause}"
                    cursor.execute(count_query, params)
                    entries_to_remove = cursor.fetchone()[0]

                    if entries_to_remove > 0:
                        if not dry_run:
                            # Actually remove the entries
                            delete_query = f"DELETE FROM `{table}` WHERE {where_clause}"
                            cursor.execute(delete_query, params)
                            conn.commit()

                        total_removed += entries_to_remove
                        db_result['tables_affected'].append({
                            'table': table,
                            'entries_removed': entries_to_remove
                        })

                        info(f"{'Would remove' if dry_run else 'Removed'} {entries_to_remove} entries from {table}")

                except sqlite3.Error as e:
                    warning(f"Error processing table {table}: {e}")
                    continue

            # Update results
            db_result['entries_removed'] = total_removed

            if not dry_run:
                # Get new file size
                conn.close()
                # Vacuum to reclaim space
                conn = sqlite3.connect(str(db_path))
                conn.execute("VACUUM")
                conn.close()

                db_result['size_after'] = db_path.stat().st_size
                db_result['size_freed'] = db_result['size_before'] - db_result['size_after']
            else:
                db_result['size_after'] = db_result['size_before']

            db_result['success'] = True

        finally:
            conn.close()

        return db_result

    def _clean_postgresql_database(self, db_info, patterns, exclude_patterns, dry_run, db_result):
        """Clean PostgreSQL database (placeholder for future implementation)"""
        db_result['success'] = False
        raise NotImplementedError("PostgreSQL cleaning not yet implemented")

    def _clean_mysql_database(self, db_info, patterns, exclude_patterns, dry_run, db_result):
        """Clean MySQL database (placeholder for future implementation)"""
        db_result['success'] = False
        raise NotImplementedError("MySQL cleaning not yet implemented")

    def _create_versioned_backup(self, file_path: Path, compress: bool = True) -> Path:
        """
        Create a versioned, optionally compressed backup

        Args:
            file_path: Path to file to backup
            compress: Whether to compress the backup

        Returns:
            Path to created backup file
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir = file_path.parent / 'backups'
        backup_dir.mkdir(exist_ok=True)

        if compress:
            backup_name = f"{file_path.stem}_{timestamp}.backup.gz"
            backup_path = backup_dir / backup_name

            with open(file_path, 'rb') as f_in:
                with gzip.open(backup_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
        else:
            backup_name = f"{file_path.stem}_{timestamp}.backup"
            backup_path = backup_dir / backup_name
            shutil.copy2(file_path, backup_path)

        success(f"Created backup: {backup_path}")
        return backup_path

    def manage_backup_retention(self, backup_dir: Path, max_backups: int = 10) -> Dict[str, Any]:
        """
        Manage backup retention policy

        Args:
            backup_dir: Directory containing backups
            max_backups: Maximum number of backups to keep

        Returns:
            Dictionary with cleanup results
        """
        if not backup_dir.exists():
            return {'cleaned': 0, 'errors': []}

        cleanup_results = {
            'cleaned': 0,
            'errors': [],
            'space_freed': 0
        }

        try:
            # Get all backup files sorted by modification time
            backup_files = []
            for file_path in backup_dir.iterdir():
                if file_path.is_file() and '.backup' in file_path.name:
                    backup_files.append((file_path.stat().st_mtime, file_path))

            # Sort by modification time (newest first)
            backup_files.sort(reverse=True)

            # Remove old backups beyond the limit
            if len(backup_files) > max_backups:
                files_to_remove = backup_files[max_backups:]

                for _, file_path in files_to_remove:
                    try:
                        file_size = file_path.stat().st_size
                        file_path.unlink()
                        cleanup_results['cleaned'] += 1
                        cleanup_results['space_freed'] += file_size
                        info(f"Removed old backup: {file_path.name}")
                    except Exception as e:
                        cleanup_results['errors'].append(f"Error removing {file_path}: {e}")

        except Exception as e:
            cleanup_results['errors'].append(f"Error managing backups in {backup_dir}: {e}")

        return cleanup_results

    def verify_backup_integrity(self, backup_path: Path) -> Dict[str, Any]:
        """
        Verify backup file integrity

        Args:
            backup_path: Path to backup file

        Returns:
            Dictionary with verification results
        """
        verification_result = {
            'valid': False,
            'compressed': False,
            'size': 0,
            'errors': []
        }

        try:
            if not backup_path.exists():
                verification_result['errors'].append("Backup file does not exist")
                return verification_result

            verification_result['size'] = backup_path.stat().st_size

            # Check if compressed
            if backup_path.name.endswith('.gz'):
                verification_result['compressed'] = True
                try:
                    with gzip.open(backup_path, 'rb') as f:
                        # Try to read first few bytes to verify it's valid gzip
                        f.read(1024)
                    verification_result['valid'] = True
                except Exception as e:
                    verification_result['errors'].append(f"Invalid gzip file: {e}")
            else:
                # For uncompressed backups, check if it's a valid SQLite file
                try:
                    with open(backup_path, 'rb') as f:
                        header = f.read(16)
                        if header.startswith(b'SQLite format 3\x00'):
                            verification_result['valid'] = True
                        else:
                            verification_result['errors'].append("Not a valid SQLite backup")
                except Exception as e:
                    verification_result['errors'].append(f"Error reading backup: {e}")

        except Exception as e:
            verification_result['errors'].append(f"Error verifying backup: {e}")

        return verification_result

    def restore_from_backup(self, backup_path: Path, target_path: Path) -> Dict[str, Any]:
        """
        Restore database from backup

        Args:
            backup_path: Path to backup file
            target_path: Path where to restore the database

        Returns:
            Dictionary with restore results
        """
        restore_result = {
            'success': False,
            'backup_verified': False,
            'target_backed_up': False,
            'restored_size': 0,
            'errors': []
        }

        try:
            # Verify backup integrity first
            verification = self.verify_backup_integrity(backup_path)
            restore_result['backup_verified'] = verification['valid']

            if not verification['valid']:
                restore_result['errors'].extend(verification['errors'])
                return restore_result

            # Create backup of current target if it exists
            if target_path.exists():
                current_backup = self._create_versioned_backup(target_path)
                restore_result['target_backed_up'] = True
                info(f"Created backup of current file: {current_backup}")

            # Restore from backup
            if backup_path.name.endswith('.gz'):
                # Decompress and restore
                with gzip.open(backup_path, 'rb') as f_in:
                    with open(target_path, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
            else:
                # Direct copy
                shutil.copy2(backup_path, target_path)

            restore_result['restored_size'] = target_path.stat().st_size
            restore_result['success'] = True
            success(f"Successfully restored {target_path} from {backup_path}")

        except Exception as e:
            restore_result['errors'].append(f"Error during restore: {e}")
            error(f"Failed to restore from backup: {e}")

        return restore_result

    def estimate_cleaning_impact(self, databases: List[Dict[str, Any]], patterns: List[str] = None) -> Dict[str, Any]:
        """
        Estimate the impact of cleaning operations without making changes

        Args:
            databases: List of database info dictionaries
            patterns: Patterns to match for cleaning

        Returns:
            Dictionary with impact estimation
        """
        if patterns is None:
            patterns = ['augment', 'Augment', 'AUGMENT']

        # Perform dry run to estimate impact
        dry_run_results = self.selective_clean(databases, patterns, dry_run=True)

        impact_estimation = {
            'total_databases': len(databases),
            'databases_affected': len(dry_run_results['databases_processed']),
            'total_entries_to_remove': dry_run_results['total_entries_removed'],
            'estimated_space_freed': 0,  # Will be calculated
            'risk_assessment': 'low',
            'recommendations': [],
            'warnings': dry_run_results['warnings'],
            'detailed_impact': dry_run_results['databases_processed']
        }

        # Calculate estimated space freed (rough estimation)
        for db_result in dry_run_results['databases_processed']:
            if db_result['entries_removed'] > 0:
                # Rough estimation: assume each entry is about 100 bytes on average
                estimated_freed = db_result['entries_removed'] * 100
                impact_estimation['estimated_space_freed'] += estimated_freed

        # Assess risk level
        high_impact_dbs = [db for db in dry_run_results['databases_processed']
                          if db['entries_removed'] > 100]

        if len(high_impact_dbs) > 0:
            impact_estimation['risk_assessment'] = 'high'
            impact_estimation['recommendations'].append("High impact operation detected. Ensure backups are created.")
        elif impact_estimation['total_entries_to_remove'] > 50:
            impact_estimation['risk_assessment'] = 'medium'
            impact_estimation['recommendations'].append("Medium impact operation. Review affected databases carefully.")

        # Add general recommendations
        if impact_estimation['total_entries_to_remove'] > 0:
            impact_estimation['recommendations'].append("Close all applications before proceeding.")
            impact_estimation['recommendations'].append("Verify backups are created successfully.")
        else:
            impact_estimation['recommendations'].append("No Augment-related entries found to clean.")

        return impact_estimation
