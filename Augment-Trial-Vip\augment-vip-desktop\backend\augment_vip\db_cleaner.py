"""
VS Code database cleaner module
"""

import os
import sys
import sqlite3
from pathlib import Path
from typing import List, Dict, Any, Optional

from .utils import info, success, error, warning, get_vscode_paths, backup_file

def preview_vscode_cleanup(silent: bool = False) -> Dict[str, Any]:
    """
    Preview what will be cleaned from VS Code databases

    Args:
        silent: If True, suppress info messages

    Returns:
        Dictionary containing preview information
    """
    if not silent:
        info("Scanning VS Code databases for preview...")

    # Get VS Code paths
    paths = get_vscode_paths()
    state_db = paths["state_db"]

    preview_data = {
        "database_path": str(state_db),
        "database_exists": state_db.exists(),
        "database_size": 0,
        "augment_entries": [],
        "total_entries": 0,
        "entries_to_remove": 0,
        "backup_location": f"{state_db}.backup",
        "safe_to_clean": False,
        "warnings": [],
        "recommendations": []
    }

    if not state_db.exists():
        preview_data["warnings"].append(f"VS Code database not found at: {state_db}")
        return preview_data

    # Get database size
    try:
        preview_data["database_size"] = state_db.stat().st_size
    except Exception as e:
        preview_data["warnings"].append(f"Could not get database size: {e}")

    # Connect to database and analyze
    try:
        conn = sqlite3.connect(str(state_db))
        cursor = conn.cursor()

        # Get total entries
        cursor.execute("SELECT COUNT(*) FROM ItemTable")
        preview_data["total_entries"] = cursor.fetchone()[0]

        # Get Augment-related entries
        cursor.execute("SELECT key, value FROM ItemTable WHERE key LIKE '%augment%'")
        augment_entries = cursor.fetchall()

        preview_data["entries_to_remove"] = len(augment_entries)
        preview_data["augment_entries"] = [
            {"key": entry[0], "value": entry[1][:100] + "..." if len(entry[1]) > 100 else entry[1]}
            for entry in augment_entries[:10]  # Limit to first 10 for preview
        ]

        if len(augment_entries) > 10:
            preview_data["augment_entries"].append({
                "key": f"... and {len(augment_entries) - 10} more entries",
                "value": ""
            })

        conn.close()

        # Determine if safe to clean
        preview_data["safe_to_clean"] = True

        if preview_data["entries_to_remove"] == 0:
            preview_data["recommendations"].append("No Augment-related entries found. No cleanup needed.")
        else:
            preview_data["recommendations"].append(f"Found {preview_data['entries_to_remove']} Augment-related entries that can be safely removed.")
            preview_data["recommendations"].append("A backup will be created before making any changes.")

    except sqlite3.Error as e:
        preview_data["warnings"].append(f"SQLite error during preview: {e}")
        preview_data["safe_to_clean"] = False
    except Exception as e:
        preview_data["warnings"].append(f"Unexpected error during preview: {e}")
        preview_data["safe_to_clean"] = False

    return preview_data

def clean_vscode_db() -> bool:
    """
    Clean VS Code databases by removing entries containing "augment"
    
    Returns:
        True if successful, False otherwise
    """
    info("Starting database cleanup process")
    
    # Get VS Code paths
    paths = get_vscode_paths()
    state_db = paths["state_db"]
    
    if not state_db.exists():
        warning(f"VS Code database not found at: {state_db}")
        return False
    
    info(f"Found VS Code database at: {state_db}")
    
    # Create backup
    backup_path = backup_file(state_db)
    
    # Connect to the database
    try:
        # Connect to the original database
        conn = sqlite3.connect(str(state_db))
        cursor = conn.cursor()
        
        # Get the count of records before deletion
        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
        count_before = cursor.fetchone()[0]
        
        if count_before == 0:
            info("No Augment-related entries found in the database")
            conn.close()
            return True
        
        # Delete records containing "augment"
        cursor.execute("DELETE FROM ItemTable WHERE key LIKE '%augment%'")
        conn.commit()
        
        # Get the count of records after deletion
        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
        count_after = cursor.fetchone()[0]
        
        conn.close()
        
        success(f"Removed {count_before - count_after} Augment-related entries from the database")
        return True
        
    except sqlite3.Error as e:
        error(f"SQLite error: {e}")
        
        # Restore from backup if there was an error
        if backup_path.exists():
            info("Restoring from backup...")
            try:
                shutil.copy2(backup_path, state_db)
                success("Restored from backup")
            except Exception as restore_error:
                error(f"Failed to restore from backup: {restore_error}")
        
        return False
    except Exception as e:
        error(f"Unexpected error: {e}")
        return False
