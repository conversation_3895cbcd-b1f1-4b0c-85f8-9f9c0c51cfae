"""
Smart Domain Blocking System
Safe hosts file management with automatic backup and restore
"""

import os
import shutil
import platform
from pathlib import Path
from typing import List, Dict, Any, Optional, Set
from datetime import datetime
import re

from .utils import info, success, error, warning

class SmartDomainBlocker:
    """Safe domain blocking with hosts file management"""
    
    def __init__(self):
        self.platform = platform.system().lower()
        self.hosts_file = self._get_hosts_file_path()
        self.backup_directory = Path.home() / '.augment-vip' / 'backups' / 'hosts'
        self.backup_directory.mkdir(parents=True, exist_ok=True)
        
        # Augment VIP markers for safe management
        self.start_marker = "# === AUGMENT VIP PRIVACY PROTECTION START ==="
        self.end_marker = "# === AUGMENT VIP PRIVACY PROTECTION END ==="
        
    def _get_hosts_file_path(self) -> Path:
        """Get the hosts file path for the current platform"""
        if self.platform == 'windows':
            return Path(r'C:\Windows\System32\drivers\etc\hosts')
        else:  # macOS/Linux
            return Path('/etc/hosts')
    
    def analyze_current_blocking(self) -> Dict[str, Any]:
        """
        Analyze current domain blocking status
        
        Returns:
            Dictionary with current blocking analysis
        """
        analysis = {
            'analysis_metadata': {
                'timestamp': datetime.now().isoformat(),
                'platform': self.platform,
                'hosts_file': str(self.hosts_file),
                'hosts_file_exists': self.hosts_file.exists()
            },
            'current_status': {
                'augment_vip_section_exists': False,
                'total_blocked_domains': 0,
                'augment_blocked_domains': 0,
                'other_blocked_domains': 0,
                'hosts_file_size': 0,
                'last_modified': None,
                'is_writable': False
            },
            'blocked_domains': {
                'augment_vip_managed': [],
                'other_entries': []
            },
            'recommendations': []
        }
        
        try:
            if not self.hosts_file.exists():
                analysis['recommendations'].append("⚠️ Hosts file not found - this is unusual")
                return analysis
            
            # Check file permissions
            analysis['current_status']['is_writable'] = os.access(self.hosts_file, os.W_OK)
            analysis['current_status']['hosts_file_size'] = self.hosts_file.stat().st_size
            analysis['current_status']['last_modified'] = datetime.fromtimestamp(
                self.hosts_file.stat().st_mtime
            ).isoformat()
            
            # Read and analyze hosts file
            with open(self.hosts_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Check for Augment VIP section
            if self.start_marker in content and self.end_marker in content:
                analysis['current_status']['augment_vip_section_exists'] = True
                
                # Extract Augment VIP managed domains
                start_idx = content.find(self.start_marker)
                end_idx = content.find(self.end_marker)
                
                if start_idx != -1 and end_idx != -1:
                    augment_section = content[start_idx:end_idx + len(self.end_marker)]
                    augment_domains = self._extract_domains_from_section(augment_section)
                    analysis['blocked_domains']['augment_vip_managed'] = augment_domains
                    analysis['current_status']['augment_blocked_domains'] = len(augment_domains)
            
            # Count all blocked domains
            all_domains = self._extract_all_blocked_domains(content)
            analysis['current_status']['total_blocked_domains'] = len(all_domains)
            analysis['current_status']['other_blocked_domains'] = (
                len(all_domains) - analysis['current_status']['augment_blocked_domains']
            )
            
            # Store other entries for reference
            augment_managed = set(analysis['blocked_domains']['augment_vip_managed'])
            analysis['blocked_domains']['other_entries'] = [
                domain for domain in all_domains if domain not in augment_managed
            ]
            
            # Generate recommendations
            analysis['recommendations'] = self._generate_blocking_recommendations(analysis)
            
        except Exception as e:
            error(f"Error analyzing hosts file: {e}")
            analysis['error'] = str(e)
        
        return analysis
    
    def preview_domain_blocking(self, 
                               domains_to_block: List[str],
                               domains_to_unblock: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Preview what domain blocking changes would be made
        
        Args:
            domains_to_block: List of domains to add to blocking
            domains_to_unblock: List of domains to remove from blocking
        
        Returns:
            Preview of changes that would be made
        """
        if domains_to_unblock is None:
            domains_to_unblock = []
        
        preview = {
            'preview_metadata': {
                'timestamp': datetime.now().isoformat(),
                'operation': 'domain_blocking_preview',
                'safe_operation': True
            },
            'current_state': self.analyze_current_blocking(),
            'proposed_changes': {
                'domains_to_add': [],
                'domains_to_remove': [],
                'domains_already_blocked': [],
                'domains_not_currently_blocked': []
            },
            'after_changes': {
                'total_augment_blocked': 0,
                'new_domains_count': 0,
                'removed_domains_count': 0
            },
            'safety_checks': {
                'backup_will_be_created': True,
                'changes_are_reversible': True,
                'no_system_domains_affected': True,
                'hosts_file_writable': False
            },
            'warnings': []
        }
        
        try:
            current_blocked = set(preview['current_state']['blocked_domains']['augment_vip_managed'])
            
            # Analyze domains to block
            for domain in domains_to_block:
                if domain in current_blocked:
                    preview['proposed_changes']['domains_already_blocked'].append(domain)
                else:
                    preview['proposed_changes']['domains_to_add'].append(domain)
            
            # Analyze domains to unblock
            for domain in domains_to_unblock:
                if domain in current_blocked:
                    preview['proposed_changes']['domains_to_remove'].append(domain)
                else:
                    preview['proposed_changes']['domains_not_currently_blocked'].append(domain)
            
            # Calculate after-changes state
            preview['after_changes']['new_domains_count'] = len(preview['proposed_changes']['domains_to_add'])
            preview['after_changes']['removed_domains_count'] = len(preview['proposed_changes']['domains_to_remove'])
            preview['after_changes']['total_augment_blocked'] = (
                len(current_blocked) + 
                preview['after_changes']['new_domains_count'] - 
                preview['after_changes']['removed_domains_count']
            )
            
            # Safety checks
            preview['safety_checks']['hosts_file_writable'] = os.access(self.hosts_file, os.W_OK)
            
            # Check for system-critical domains
            system_domains = {'localhost', 'local', 'broadcasthost'}
            for domain in domains_to_block:
                if any(sys_domain in domain.lower() for sys_domain in system_domains):
                    preview['safety_checks']['no_system_domains_affected'] = False
                    preview['warnings'].append(f"⚠️ Domain '{domain}' may be system-critical")
            
            # Permission warnings
            if not preview['safety_checks']['hosts_file_writable']:
                preview['warnings'].append("⚠️ Hosts file is not writable - administrator privileges may be required")
            
            # No changes warning
            if (not preview['proposed_changes']['domains_to_add'] and 
                not preview['proposed_changes']['domains_to_remove']):
                preview['warnings'].append("ℹ️ No changes would be made - all domains are already in desired state")
        
        except Exception as e:
            error(f"Error creating domain blocking preview: {e}")
            preview['error'] = str(e)
        
        return preview
    
    def apply_domain_blocking(self, 
                             domains_to_block: List[str],
                             domains_to_unblock: Optional[List[str]] = None,
                             create_backup: bool = True) -> Dict[str, Any]:
        """
        Apply domain blocking changes to hosts file
        
        Args:
            domains_to_block: List of domains to block
            domains_to_unblock: List of domains to unblock
            create_backup: Whether to create backup before changes
        
        Returns:
            Results of the blocking operation
        """
        if domains_to_unblock is None:
            domains_to_unblock = []
        
        result = {
            'operation_metadata': {
                'timestamp': datetime.now().isoformat(),
                'operation': 'apply_domain_blocking',
                'backup_created': False,
                'backup_path': None
            },
            'changes_made': {
                'domains_blocked': [],
                'domains_unblocked': [],
                'total_changes': 0
            },
            'operation_result': {
                'success': False,
                'error_message': None,
                'hosts_file_updated': False
            }
        }
        
        try:
            # Create backup if requested
            if create_backup:
                backup_path = self._create_hosts_backup()
                result['operation_metadata']['backup_created'] = True
                result['operation_metadata']['backup_path'] = str(backup_path)
                info(f"Created hosts file backup: {backup_path}")
            
            # Read current hosts file
            if not self.hosts_file.exists():
                # Create basic hosts file if it doesn't exist
                self._create_basic_hosts_file()
            
            with open(self.hosts_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Process the content
            new_content = self._update_hosts_content(
                content, domains_to_block, domains_to_unblock
            )
            
            # Write updated content
            with open(self.hosts_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            result['changes_made']['domains_blocked'] = domains_to_block
            result['changes_made']['domains_unblocked'] = domains_to_unblock
            result['changes_made']['total_changes'] = len(domains_to_block) + len(domains_to_unblock)
            result['operation_result']['success'] = True
            result['operation_result']['hosts_file_updated'] = True
            
            success(f"Domain blocking applied successfully: {len(domains_to_block)} blocked, {len(domains_to_unblock)} unblocked")
            
        except PermissionError:
            error_msg = "Permission denied - administrator privileges required to modify hosts file"
            result['operation_result']['error_message'] = error_msg
            error(error_msg)
        except Exception as e:
            error_msg = f"Failed to apply domain blocking: {e}"
            result['operation_result']['error_message'] = error_msg
            error(error_msg)
        
        return result
    
    def restore_hosts_backup(self, backup_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Restore hosts file from backup
        
        Args:
            backup_path: Specific backup to restore (latest if None)
        
        Returns:
            Results of the restore operation
        """
        result = {
            'restore_metadata': {
                'timestamp': datetime.now().isoformat(),
                'operation': 'restore_hosts_backup'
            },
            'restore_result': {
                'success': False,
                'backup_restored': None,
                'error_message': None
            }
        }
        
        try:
            if backup_path is None:
                # Find latest backup
                backup_files = list(self.backup_directory.glob('hosts_backup_*.txt'))
                if not backup_files:
                    result['restore_result']['error_message'] = "No backup files found"
                    return result
                
                backup_path = str(max(backup_files, key=lambda x: x.stat().st_mtime))
            
            backup_file = Path(backup_path)
            if not backup_file.exists():
                result['restore_result']['error_message'] = f"Backup file not found: {backup_path}"
                return result
            
            # Create backup of current state before restore
            current_backup = self._create_hosts_backup()
            info(f"Created backup of current state: {current_backup}")
            
            # Restore from backup
            shutil.copy2(backup_file, self.hosts_file)
            
            result['restore_result']['success'] = True
            result['restore_result']['backup_restored'] = backup_path
            
            success(f"Hosts file restored from backup: {backup_path}")
            
        except Exception as e:
            error_msg = f"Failed to restore hosts backup: {e}"
            result['restore_result']['error_message'] = error_msg
            error(error_msg)
        
        return result
    
    def get_predefined_blocklists(self) -> Dict[str, List[str]]:
        """Get predefined blocklists for common tracking domains"""
        return {
            'detected_tracking': [
                # Domains detected in user's system
                'td.doubleclick.net',
                'cm.g.doubleclick.net',
                'analytics.tiktok.com',
                'stats.g.doubleclick.net',
                'googleads.g.doubleclick.net',
                'analytics.google.com',
                'collector.prd-278964.gl-product-analytics.com'
            ],
            'google_analytics': [
                'google-analytics.com',
                'googletagmanager.com',
                'doubleclick.net',
                'googlesyndication.com',
                'googleadservices.com',
                'google.com/analytics',
                'google.com/adsense'
            ],
            'microsoft_telemetry': [
                'telemetry.microsoft.com',
                'vortex.data.microsoft.com',
                'watson.telemetry.microsoft.com',
                'settings-win.data.microsoft.com',
                'browser.events.data.microsoft.com'
            ],
            'development_tools': [
                'vscode-update.azurewebsites.net',
                'marketplace.visualstudio.com',
                'az764295.vo.msecnd.net',
                'jetbrains.com/api',
                'resources.jetbrains.com'
            ],
            'social_tracking': [
                'facebook.com/tr',
                'connect.facebook.net',
                'twitter.com/i/adsct',
                'linkedin.com/li.lms-analytics',
                'snapchat.com/tr'
            ]
        }
    
    def _extract_domains_from_section(self, section: str) -> List[str]:
        """Extract blocked domains from a hosts file section"""
        domains = []
        lines = section.split('\n')
        
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#'):
                # Match pattern: IP domain
                parts = line.split()
                if len(parts) >= 2 and (parts[0] == '0.0.0.0' or parts[0] == '127.0.0.1'):
                    domains.append(parts[1])
        
        return domains
    
    def _extract_all_blocked_domains(self, content: str) -> List[str]:
        """Extract all blocked domains from hosts file content"""
        domains = []
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#'):
                parts = line.split()
                if len(parts) >= 2 and (parts[0] == '0.0.0.0' or parts[0] == '127.0.0.1'):
                    domain = parts[1]
                    if domain not in ['localhost', 'local', 'broadcasthost']:
                        domains.append(domain)
        
        return domains
    
    def _create_hosts_backup(self) -> Path:
        """Create a timestamped backup of the hosts file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"hosts_backup_{timestamp}.txt"
        backup_path = self.backup_directory / backup_filename
        
        if self.hosts_file.exists():
            shutil.copy2(self.hosts_file, backup_path)
        else:
            # Create empty backup file
            backup_path.touch()
        
        return backup_path
    
    def _create_basic_hosts_file(self):
        """Create a basic hosts file if none exists"""
        basic_content = """# Hosts file
127.0.0.1   localhost
::1         localhost
"""
        with open(self.hosts_file, 'w', encoding='utf-8') as f:
            f.write(basic_content)
    
    def _update_hosts_content(self, 
                             content: str, 
                             domains_to_block: List[str], 
                             domains_to_unblock: List[str]) -> str:
        """Update hosts file content with new blocking rules"""
        # Remove existing Augment VIP section
        if self.start_marker in content and self.end_marker in content:
            start_idx = content.find(self.start_marker)
            end_idx = content.find(self.end_marker) + len(self.end_marker)
            content = content[:start_idx] + content[end_idx:]
        
        # Get currently managed domains
        current_analysis = self.analyze_current_blocking()
        current_domains = set(current_analysis['blocked_domains']['augment_vip_managed'])
        
        # Calculate new domain set
        new_domains = current_domains.copy()
        new_domains.update(domains_to_block)
        new_domains.difference_update(domains_to_unblock)
        
        # Create new Augment VIP section
        if new_domains:
            augment_section = f"\n{self.start_marker}\n"
            augment_section += f"# Augment VIP Privacy Protection - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            augment_section += "# This section is automatically managed - do not edit manually\n"
            
            for domain in sorted(new_domains):
                augment_section += f"0.0.0.0 {domain}\n"
            
            augment_section += f"{self.end_marker}\n"
            
            # Append to content
            content = content.rstrip() + augment_section
        
        return content
    
    def _generate_blocking_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on current blocking analysis"""
        recommendations = []
        
        current_status = analysis['current_status']
        
        if not current_status['is_writable']:
            recommendations.append("🔐 Run as administrator to enable domain blocking")
        
        if current_status['augment_blocked_domains'] == 0:
            recommendations.append("🛡️ No domains currently blocked - consider blocking detected tracking domains")
        
        if current_status['total_blocked_domains'] > 100:
            recommendations.append("📊 Large number of blocked domains detected - consider reviewing for performance")
        
        if not current_status['augment_vip_section_exists']:
            recommendations.append("✨ Augment VIP can safely manage domain blocking with automatic backups")
        
        recommendations.extend([
            "💾 Automatic backups are created before any changes",
            "🔄 All blocking changes are easily reversible",
            "🎯 Focus on blocking detected tracking domains first",
            "📱 Regular review of blocked domains is recommended"
        ])
        
        return recommendations
