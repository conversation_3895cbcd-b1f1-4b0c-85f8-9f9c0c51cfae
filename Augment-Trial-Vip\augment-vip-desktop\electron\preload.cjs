const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require("electron");

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld("electronAPI", {
  // Python CLI operations
  executePythonCommand: (command, args) =>
    ipcRenderer.invoke("execute-python-command", command, args),

  // System information
  getSystemInfo: () => ipcRenderer.invoke("get-system-info"),
  getAppVersion: () => ipcRenderer.invoke("get-app-version"),

  // VS Code detection
  checkVsCodeInstallation: () =>
    ipcRenderer.invoke("check-vscode-installation"),

  // System state analysis
  analyzeSystemState: () => ipcRenderer.invoke("analyze-system-state"),

  // Database operations
  findDatabaseFiles: () => ipcRenderer.invoke("find-database-files"),
  scanDatabases: () => ipcRenderer.invoke("scan-databases"),
  cleanDatabase: (databaseInfo) =>
    ipcRenderer.invoke("clean-database", databaseInfo),
  backupDatabase: (databaseInfo, backupPath) =>
    ipcRenderer.invoke("backup-database", databaseInfo, backupPath),

  // File system operations
  checkFileExists: (filePath) =>
    ipcRenderer.invoke("check-file-exists", filePath),
  readFile: (filePath) => ipcRenderer.invoke("read-file", filePath),
  openPath: (path) => ipcRenderer.invoke("open-path", path),
  copyToClipboard: (text) => ipcRenderer.invoke("copy-to-clipboard", text),

  // Event listeners
  onCommandOutput: (callback) => {
    ipcRenderer.on("command-output", (event, data) => callback(data));
  },

  onQuickClean: (callback) => {
    ipcRenderer.on("quick-clean", () => callback());
  },

  onScanProgress: (callback) => {
    ipcRenderer.on("scan-progress", (event, data) => callback(data));
  },

  onCleanProgress: (callback) => {
    ipcRenderer.on("clean-progress", (event, data) => callback(data));
  },

  onBackupProgress: (callback) => {
    ipcRenderer.on("backup-progress", (event, data) => callback(data));
  },

  onStateAnalysisProgress: (callback) => {
    ipcRenderer.on("state-analysis-progress", (event, data) => callback(data));
  },

  // Remove listeners
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  },
});

// Expose a limited API for theme management
contextBridge.exposeInMainWorld("themeAPI", {
  setTheme: (theme) => {
    document.documentElement.classList.toggle("dark", theme === "dark");
    localStorage.setItem("theme", theme);
  },

  getTheme: () => {
    return localStorage.getItem("theme") || "light";
  },
});
